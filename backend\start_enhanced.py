#!/usr/bin/env python3
"""
Enhanced startup script for Agent Lee™ System v2.0
Handles dependency checking, configuration validation, and graceful startup
"""

import os
import sys
import subprocess
import importlib.util

# Set UTF-8 encoding for Windows console compatibility
import sys
if sys.platform == "win32":
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# Load environment variables FIRST before any other imports
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("[OK] Environment variables loaded")
except ImportError:
    print("[WARN] python-dotenv not available - environment variables not loaded")
except Exception as e:
    print(f"[WARN] Failed to load environment variables: {e}")

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("[ERROR] Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"[OK] Python version: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'psutil',
        'sqlalchemy',
        'python-dotenv'
    ]

    optional_packages = [
        ('google-generativeai', 'Google Gemini API support'),
        ('pyttsx3', 'Text-to-speech functionality'),
        ('speech_recognition', 'Voice recognition support')
    ]

    missing_required = []
    missing_optional = []

    print("[INFO] Checking dependencies...")

    # Check required packages
    for package in required_packages:
        if importlib.util.find_spec(package.replace('-', '_')) is None:
            missing_required.append(package)
        else:
            print(f"[OK] {package}")

    # Check optional packages
    for package, description in optional_packages:
        if importlib.util.find_spec(package.replace('-', '_')) is None:
            missing_optional.append((package, description))
        else:
            print(f"[OK] {package} ({description})")

    if missing_required:
        print(f"\n[ERROR] Missing required packages: {', '.join(missing_required)}")
        print("   Install with: pip install -r requirements.txt")
        return False

    if missing_optional:
        print(f"\n[WARN] Missing optional packages:")
        for package, description in missing_optional:
            print(f"   - {package}: {description}")
        print("   Install with: pip install -r requirements.txt")

    return True

def check_configuration():
    """Check configuration and environment setup"""
    print("\n[INFO] Checking configuration...")

    # Check if .env file exists
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("[WARN] .env file not found. Creating from .env.example...")
            try:
                with open('.env.example', 'r') as src, open('.env', 'w') as dst:
                    dst.write(src.read())
                print("[OK] .env file created. Please edit it with your configuration.")
            except Exception as e:
                print(f"[ERROR] Failed to create .env file: {e}")
                return False
        else:
            print("[ERROR] No .env or .env.example file found")
            return False
    else:
        print("[OK] .env file found")

    # Load and validate configuration
    try:
        from config import AppConfig
        warnings = AppConfig.validate_config()

        if warnings:
            print("[WARN] Configuration warnings:")
            for warning in warnings:
                print(f"   - {warning}")
        else:
            print("[OK] Configuration validation passed")

        # Show configuration summary
        summary = AppConfig.get_config_summary()
        print(f"[OK] Configuration summary:")
        print(f"   - Debug mode: {summary['debug']}")
        print(f"   - Host: {summary['host']}:{summary['port']}")
        print(f"   - Gemini configured: {summary['gemini_configured']}")
        print(f"   - Database type: {summary['database_type']}")

    except Exception as e:
        print(f"[ERROR] Configuration error: {e}")
        return False

    return True

def run_tests():
    """Run basic system tests"""
    print("\n[INFO] Running system tests...")

    try:
        # Add current directory to path for imports
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        # Test configuration loading
        try:
            from config import AppConfig
            print("[OK] Configuration module loaded")
        except Exception as e:
            print(f"[ERROR] Configuration test failed: {e}")
            return False

        # Test database models
        try:
            from models import create_tables
            print("[OK] Database models loaded")
        except Exception as e:
            print(f"[ERROR] Database models test failed: {e}")
            return False

        # Test LLM service
        try:
            from llm_service import LLMService
            llm = LLMService()
            status = llm.get_service_status()
            print(f"[OK] LLM service loaded ({status['primary_model']})")
        except Exception as e:
            print(f"[WARN] LLM service test failed: {e} (will use fallback)")

        print("[OK] Basic tests passed")
        return True

    except Exception as e:
        print(f"[ERROR] Tests failed: {e}")
        return False

def start_server():
    """Start the enhanced Agent Lee server"""
    print("\n[INFO] Starting Agent Lee System v2.0...")

    try:
        # Import the enhanced controller
        from agentlee_controller_v2 import app, AppConfig
        import uvicorn

        print(f"[INFO] Server will start at http://{AppConfig.HOST}:{AppConfig.PORT}")
        print("[INFO] API documentation available at http://localhost:8000/docs")
        print("[INFO] Press Ctrl+C to stop the server")
        print("-" * 50)

        # Start the server
        uvicorn.run(
            app,
            host=AppConfig.HOST,
            port=AppConfig.PORT,
            reload=AppConfig.DEBUG,
            log_level=AppConfig.LOG_LEVEL.lower()
        )

    except KeyboardInterrupt:
        print("\n[INFO] Server stopped by user")
    except Exception as e:
        print(f"[ERROR] Server startup failed: {e}")
        return False

    return True

def main():
    """Main startup routine"""
    print("Agent Lee System v2.0 Enhanced Startup")
    print("=" * 50)

    # Check Python version
    if not check_python_version():
        sys.exit(1)

    # Check dependencies
    if not check_dependencies():
        print("\n[INFO] To install dependencies, run:")
        print("   pip install -r requirements.txt")
        sys.exit(1)

    # Check configuration
    if not check_configuration():
        print("\n[INFO] Please check your configuration and try again")
        sys.exit(1)

    # Run tests
    if not run_tests():
        print("\n[WARN] Some tests failed, but continuing startup...")

    # Start server
    start_server()

if __name__ == "__main__":
    main()
