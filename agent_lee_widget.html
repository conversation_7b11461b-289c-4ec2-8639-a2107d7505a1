<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Lee™ - AI Assistant Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            overflow: hidden;
            user-select: none;
        }

        /* Floating Agent Card */
        .agent-widget {
            width: 350px;
            height: 500px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: fixed;
            top: 50px;
            right: 50px;
            cursor: move;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .agent-widget.minimized {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            cursor: pointer;
        }

        .agent-widget.minimized .widget-content {
            display: none;
        }

        .agent-widget.minimized .mini-avatar {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }

        .mini-avatar {
            display: none;
        }

        .mini-avatar img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }

        /* Window Controls */
        .window-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            z-index: 1000;
        }

        .window-btn {
            width: 20px;
            height: 20px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .minimize { background: #ffbd2e; }
        .maximize { background: #28ca42; }
        .close { background: #ff5f56; }

        /* Widget Content */
        .widget-content {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .agent-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .agent-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .agent-name {
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .agent-status {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
        }

        /* Chat Area */
        .chat-area {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: rgba(255,255,255,0.2);
            color: white;
            margin-left: auto;
        }

        .agent-message {
            background: rgba(255,255,255,0.9);
            color: #333;
        }

        /* Input Area */
        .input-area {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .message-input {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 20px;
            background: rgba(255,255,255,0.9);
            outline: none;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Action Buttons */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        .action-btn {
            padding: 8px;
            border: none;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        /* Personality Indicator */
        .personality-indicator {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.3);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="agent-widget" id="agentWidget">
        <!-- Window Controls -->
        <div class="window-controls">
            <button class="window-btn minimize" id="minimizeBtn">−</button>
            <button class="window-btn close" id="closeBtn">×</button>
        </div>

        <!-- Mini Avatar (shown when minimized) -->
        <div class="mini-avatar">
            <img src="frontend/AgetleeAvatar.png" alt="Agent Lee">
        </div>

        <!-- Widget Content -->
        <div class="widget-content">
            <!-- Agent Header -->
            <div class="agent-header">
                <img src="frontend/AgetleeAvatar.png" alt="Agent Lee" class="agent-avatar">
                <div class="agent-name">Agent Lee™</div>
                <div class="agent-status">AI Assistant Ready</div>
            </div>

            <!-- Chat Area -->
            <div class="chat-area" id="chatArea">
                <div class="message agent-message">
                    Hello! I'm Agent Lee. I can help you with apps, web searches, emails, and more! 😊
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <input type="text" class="message-input" id="messageInput" placeholder="Ask me anything...">
                <button class="send-btn" id="sendBtn">📤</button>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="action-btn" id="emailBtn">📧 Email</button>
                <button class="action-btn" id="phoneBtn">📱 Phone</button>
                <button class="action-btn" id="webBtn">🌐 Web</button>
                <button class="action-btn" id="appsBtn">🚀 Apps</button>
                <button class="action-btn" id="listenBtn">🎤 Listen</button>
                <button class="action-btn" id="stopBtn">🛑 Stop</button>
                <button class="action-btn" id="memoryBtn">🧠 Memory</button>
                <button class="action-btn" id="moodBtn">🎭 Mood</button>
            </div>
        </div>

        <!-- Personality Indicator -->
        <div class="personality-indicator" id="personalityIndicator">
            Default Mode
        </div>
    </div>

    <script>
        // Make widget draggable
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        const widget = document.getElementById('agentWidget');
        
        widget.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
            if (e.target.closest('.window-controls') || e.target.closest('.widget-content')) return;
            
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === widget) {
                isDragging = true;
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                widget.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
            }
        }

        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        }

        // Minimize/Maximize functionality
        document.getElementById('minimizeBtn').addEventListener('click', () => {
            widget.classList.toggle('minimized');
        });

        // Double-click to restore from minimized
        widget.addEventListener('dblclick', () => {
            if (widget.classList.contains('minimized')) {
                widget.classList.remove('minimized');
            }
        });

        // Close button
        document.getElementById('closeBtn').addEventListener('click', () => {
            if (window.electronAPI) {
                window.electronAPI.closeApp();
            } else {
                window.close();
            }
        });

        // Chat functionality
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');

        function addMessage(sender, text) {
            const message = document.createElement('div');
            message.className = `message ${sender}-message`;
            message.textContent = text;
            chatArea.appendChild(message);
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        async function sendMessage() {
            const text = messageInput.value.trim();
            if (!text) return;

            addMessage('user', text);
            messageInput.value = '';

            try {
                const response = await fetch('http://localhost:8000/api/llm_think', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ prompt: text })
                });

                const result = await response.json();
                addMessage('agent', result.response || 'I had trouble processing that.');
            } catch (error) {
                addMessage('agent', 'Connection error. Make sure the backend is running.');
            }
        }

        sendBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        // Action button functionality
        document.getElementById('emailBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:8000/api/system/open_email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                const result = await response.json();
                addMessage('agent', result.message || '📧 Opening email client...');
            } catch (error) {
                addMessage('agent', '📧 Opening default email client...');
                window.open('mailto:', '_blank');
            }
        });

        document.getElementById('phoneBtn').addEventListener('click', async () => {
            try {
                const response = await fetch('http://localhost:8000/api/system/open_messaging', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ app: 'telegram' })
                });
                const result = await response.json();
                addMessage('agent', result.message || '📱 Opening Telegram...');
            } catch (error) {
                addMessage('agent', '📱 Trying to open messaging apps...');
            }
        });

        document.getElementById('webBtn').addEventListener('click', async () => {
            const query = prompt('What would you like me to search for?');
            if (query) {
                try {
                    const response = await fetch('http://localhost:8000/api/system/web_search', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query: query, engine: 'google' })
                    });
                    const result = await response.json();
                    addMessage('agent', result.message || `🌐 Searching for "${query}"...`);
                } catch (error) {
                    window.open(`https://www.google.com/search?q=${encodeURIComponent(query)}`, '_blank');
                    addMessage('agent', `🌐 Searching for "${query}"...`);
                }
            }
        });

        document.getElementById('appsBtn').addEventListener('click', async () => {
            const appName = prompt('Which app would you like me to open?\n(Examples: calculator, notepad, chrome, discord, telegram)');
            if (appName) {
                try {
                    const response = await fetch('http://localhost:8000/api/system/open_app', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ app_name: appName })
                    });
                    const result = await response.json();
                    addMessage('agent', result.message || `🚀 Opening ${appName}...`);
                } catch (error) {
                    addMessage('agent', `🚀 Trying to open ${appName}...`);
                }
            }
        });

        // Initialize
        addMessage('agent', 'Ready to assist! Try asking me to open apps, search the web, or help with tasks.');
    </script>
</body>
</html>
