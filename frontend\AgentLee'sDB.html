<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>CRM Database Dashboard</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Space+Mono:wght@400;700&display=swap">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      /* Color Scheme */
      --primary: #6C47FF;
      --primary-light: #8F75FF;
      --primary-dark: #4B31CC;
      --secondary: #00E3FF;
      --secondary-light: #47F2FF;
      --secondary-dark: #00B3CC;
      --accent-1: #FF4499;
      --accent-2: #47FFBE;
      --accent-3: #FFC107;
      --dark: #060818;
      --darker: #030410;
      --card-bg: rgba(12, 15, 35, 0.7);
      --light: #FFFFFF;
      --gray-100: #f8f9fe;
      --gray-200: #ebedf5;
      --gray-300: #CCD0E1;
      --gray-400: #9EA4BD;
      --gray-500: #73799C;
      --gray-600: #5A6081;
      --gray-700: #3B3F56;
      --gray-800: #23263B;
      --status-success: #00FFB3;
      --status-warning: #FFE14C;
      --status-error: #FF4C6F;
      
      /* Database Type Colors */
      --db-indexeddb: #6C47FF;
      --db-sqljs: #00E3FF;
      --db-duckdb: #FF4499;
      --db-vector: #47FFBE;
      --db-cache: #FFC107;
      
      /* UI Elements */
      --border-radius-sm: 10px;
      --border-radius-md: 16px;
      --border-radius-lg: 24px;
      --border-radius-xl: 32px;
      --border-radius-pill: 100px;
      --card-shadow: 0 20px 60px rgba(3, 4, 16, 0.4);
      --card-glow: 0 0 40px rgba(108, 71, 255, 0.2);
      --transition: all 0.4s cubic-bezier(0.17, 0.84, 0.44, 1);
    }
    
    /* Reset and Base Styles */
    *, *::before, *::after {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    @keyframes gradientFlow {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
    
    body {
      font-family: 'Outfit', sans-serif;
      background: var(--darker);
      color: var(--light);
      min-height: 100vh;
      overflow-x: hidden;
      line-height: 1.6;
      position: relative;
      scroll-behavior: smooth;
      perspective: 1000px;
    }
    
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      opacity: 0.3;
      z-index: -2;
    }
    
    body::after {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 20%, rgba(108, 71, 255, 0.12), transparent 40%),
        radial-gradient(circle at 80% 30%, rgba(0, 227, 255, 0.12), transparent 40%),
        radial-gradient(circle at 50% 80%, rgba(255, 68, 153, 0.12), transparent 40%);
      z-index: -1;
    }
    
    h1, h2, h3, h4, h5, h6 {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      letter-spacing: -0.03em;
      line-height: 1.2;
    }
    
    code, pre {
      font-family: 'Space Mono', monospace;
    }
    
    /* Scrollbar styling */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    ::-webkit-scrollbar-track {
      background: rgba(35, 38, 59, 0.7);
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: rgba(108, 71, 255, 0.7);
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(108, 71, 255, 0.9);
    }
    
    /* Animated 3D Grid Background */
    .grid-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      transform-style: preserve-3d;
      perspective: 1000px;
    }
    
    .grid-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        linear-gradient(rgba(6, 8, 24, 0) 95%, rgba(108, 71, 255, 0.3) 100%),
        linear-gradient(90deg, rgba(6, 8, 24, 0) 95%, rgba(0, 227, 255, 0.3) 100%);
      background-size: 40px 40px;
      transform: rotateX(60deg) translateZ(-100px) scale(3);
      transform-origin: center;
      animation: grid-move 30s linear infinite;
      opacity: 0.2;
    }
    
    @keyframes grid-move {
      0% { background-position: 0 0; }
      100% { background-position: 40px 40px; }
    }
    
    /* Floating particles */
    .particles-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      pointer-events: none;
      z-index: -1;
    }
    
    .particle {
      position: absolute;
      width: 2px;
      height: 2px;
      background: var(--primary);
      border-radius: 50%;
      filter: blur(8px);
      opacity: 0.4;
      mix-blend-mode: screen;
    }
    
    /* Main Header */
    .main-header {
      position: relative;
      z-index: 100;
      padding: 1.2rem 2rem;
      margin: 1.5rem 2rem;
      border-radius: var(--border-radius-lg);
      display: flex;
      justify-content: space-between;
      align-items: center;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      background: rgba(12, 15, 35, 0.5);
      border: 1px solid rgba(108, 71, 255, 0.2);
      box-shadow: 0 15px 40px rgba(3, 4, 16, 0.5);
      transform-style: preserve-3d;
    }
    
    .main-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: var(--border-radius-lg);
      background: linear-gradient(45deg, rgba(108, 71, 255, 0.1), rgba(0, 227, 255, 0.1));
      z-index: -1;
    }
    
    .brand {
      display: flex;
      align-items: center;
      gap: 1rem;
      transform: translateZ(20px);
    }
    
    .logo {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 800;
      font-size: 22px;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      box-shadow: 0 10px 25px rgba(108, 71, 255, 0.4);
      position: relative;
      overflow: hidden;
    }
    
    .logo::before {
      content: '';
      position: absolute;
      top: 0;
      left: -50%;
      width: 200%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
      transform: skewX(-25deg);
      animation: shine 4s infinite;
    }
    
    @keyframes shine {
      0% { left: -100%; }
      50%, 100% { left: 100%; }
    }
    
    .brand-name {
      font-weight: 800;
      font-size: 1.8rem;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      text-shadow: 0 5px 15px rgba(108, 71, 255, 0.3);
    }
    
    .brand-name::after {
      content: 'Agent Lee\'s DB';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0.3;
      filter: blur(8px);
    }
    
    .connection-badge {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0.6rem 1.2rem;
      background: rgba(6, 8, 24, 0.4);
      border-radius: var(--border-radius-pill);
      border: 1px solid rgba(108, 71, 255, 0.3);
      transition: var(--transition);
      box-shadow: 0 10px 20px rgba(3, 4, 16, 0.2);
      transform: translateZ(20px);
    }
    
    .connection-badge:hover {
      background: rgba(108, 71, 255, 0.15);
      border: 1px solid rgba(108, 71, 255, 0.5);
    }
    
    .connection-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    
    .connection-connected .connection-indicator {
      background: var(--status-success);
      box-shadow: 0 0 15px var(--status-success);
    }
    
    .connection-connecting .connection-indicator {
      background: var(--status-warning);
      box-shadow: 0 0 15px var(--status-warning);
      animation: pulse 1.5s infinite;
    }
    
    .connection-error .connection-indicator {
      background: var(--status-error);
      box-shadow: 0 0 15px var(--status-error);
    }
    
    .connection-text {
      font-size: 0.9rem;
      font-weight: 600;
    }
    
    @keyframes pulse {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.6; transform: scale(1.4); }
      100% { opacity: 1; transform: scale(1); }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      transform: translateZ(20px);
    }
    
    .theme-toggle {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-md);
      background: rgba(6, 8, 24, 0.4);
      border: 1px solid rgba(108, 71, 255, 0.3);
      color: var(--light);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      font-size: 1.2rem;
    }
    
    .theme-toggle:hover {
      background: rgba(108, 71, 255, 0.15);
      box-shadow: 0 10px 20px rgba(108, 71, 255, 0.2);
    }
    
    .header-metrics {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .metric-pill {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 0.6rem 1.2rem;
      background: rgba(6, 8, 24, 0.4);
      border-radius: var(--border-radius-pill);
      border: 1px solid rgba(108, 71, 255, 0.3);
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--gray-300);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .metric-pill:hover {
      background: rgba(108, 71, 255, 0.15);
      box-shadow: 0 10px 20px rgba(108, 71, 255, 0.2);
    }
    
    .metric-pill::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent);
      transition: 0.5s;
    }
    
    .metric-pill:hover::before {
      left: 100%;
    }
    
    .metric-pill-icon {
      font-size: 1.1rem;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .metric-pill span {
      color: var(--light);
      font-weight: 700;
    }
    
    /* Hero Section */
    .hero-section {
      padding: 4rem 2rem 2rem;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .hero-title {
      font-size: 4rem;
      font-weight: 800;
      margin-bottom: 1.5rem;
      background: linear-gradient(to right, var(--primary), var(--secondary), var(--accent-1));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      display: inline-block;
      transform: translateZ(30px);
      text-shadow: 0 10px 30px rgba(108, 71, 255, 0.3);
    }
    
    .hero-title::after {
      content: 'Multi-Database Command Center';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary), var(--accent-1));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0.2;
      filter: blur(10px);
    }
    
    .hero-subtitle {
      font-size: 1.3rem;
      color: var(--gray-300);
      max-width: 700px;
      margin: 0 auto 3rem;
      position: relative;
      transform: translateZ(20px);
    }
    
    /* Action Buttons */
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 1.5rem;
      margin-bottom: 3rem;
      flex-wrap: wrap;
      position: relative;
      transform: translateZ(20px);
    }
    
    .button {
      padding: 0.9rem 2rem;
      border-radius: var(--border-radius-pill);
      font-weight: 600;
      font-size: 1rem;
      display: inline-flex;
      align-items: center;
      gap: 0.8rem;
      cursor: pointer;
      transition: var(--transition);
      border: none;
      position: relative;
      overflow: hidden;
      transform-style: preserve-3d;
    }
    
    .button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
      opacity: 0;
      transition: var(--transition);
      z-index: 1;
    }
    
    .button:hover {
      box-shadow: 0 15px 30px rgba(108, 71, 255, 0.3);
    }
    
    .button:hover::before {
      opacity: 1;
    }
    
    .button:active {
      opacity: 0.9;
    }
    
    .button-primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      color: white;
      box-shadow: 0 10px 25px rgba(108, 71, 255, 0.4);
    }
    
    .button-primary::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, 
        rgba(255, 255, 255, 0) 30%, 
        rgba(255, 255, 255, 0.1) 50%, 
        rgba(255, 255, 255, 0) 70%);
      transform: rotate(30deg);
      animation: shine-button 6s infinite;
    }
    
    @keyframes shine-button {
      0% { transform: rotate(30deg) translateX(-300%); }
      30%, 100% { transform: rotate(30deg) translateX(300%); }
    }
    
    .button-secondary {
      background: rgba(12, 15, 35, 0.6);
      color: var(--light);
      border: 1px solid rgba(108, 71, 255, 0.3);
    }
    
    .button-secondary:hover {
      background: rgba(108, 71, 255, 0.15);
      border: 1px solid rgba(108, 71, 255, 0.5);
    }
    
    .button-icon {
      font-size: 1.2rem;
      z-index: 2;
    }
    
    .button-text {
      z-index: 2;
    }
    
    .button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    
    /* Dashboard Layout */
    .dashboard {
      padding: 0 2rem 2rem;
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 2rem;
      max-width: 1600px;
      margin: 0 auto;
      position: relative;
      perspective: 1000px;
    }
    
    .dashboard-column {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    
    .dashboard-column.left {
      grid-column: span 5;
    }
    
    .dashboard-column.right {
      grid-column: span 7;
    }
    
    /* Card Components */
    .card {
      border-radius: var(--border-radius-lg);
      background: var(--card-bg);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(108, 71, 255, 0.15);
      overflow: hidden;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      position: relative;
    }
    
    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        45deg, 
        transparent, 
        rgba(108, 71, 255, 0.03), 
        transparent);
      z-index: -1;
    }
    
    .card:hover {
      box-shadow: var(--card-shadow), var(--card-glow);
      border-color: rgba(108, 71, 255, 0.3);
    }
    
    .card-header {
      padding: 1.5rem;
      border-bottom: 1px solid rgba(108, 71, 255, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: rgba(12, 15, 35, 0.5);
    }
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 1.1rem;
      font-weight: 600;
    }
    
    .card-icon {
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--border-radius-md);
      background: rgba(108, 71, 255, 0.15);
      font-size: 1.2rem;
      color: var(--primary);
    }
    
    .card-badge {
      padding: 0.3rem 0.9rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.15);
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--gray-300);
    }
    
    .card-content {
      padding: 1.5rem;
      position: relative;
    }
    
    /* Database Systems */
    .db-systems {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 1.5rem;
    }
    
    .pentagon-container {
      margin-bottom: 2rem;
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
    }
    
    .pentagon-visualization {
      flex: 1;
      display: flex;
      justify-content: center;
    }
    
    .pentagon-svg {
      max-width: 100%;
      height: auto;
    }
    
    .cluster-node {
      cursor: pointer;
      transition: r 0.3s ease;
    }
    
    .cluster-node:hover {
      r: 22;
    }
    
    .pentagon-status {
      flex: 1;
      padding: 1rem;
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      border: 1px solid rgba(108, 71, 255, 0.15);
    }
    
    .cluster-status {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .status-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
    }
    
    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }
    
    .status-dot.llm {
      background-color: rgba(255, 68, 153, 0.8);
      box-shadow: 0 0 8px rgba(255, 68, 153, 0.6);
    }
    
    .status-dot.agent {
      background-color: rgba(108, 71, 255, 0.8);
      box-shadow: 0 0 8px rgba(108, 71, 255, 0.6);
    }
    
    .status-dot.worker {
      background-color: rgba(255, 193, 7, 0.8);
      box-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    
    .status-dot.todo {
      background-color: rgba(71, 255, 190, 0.8);
      box-shadow: 0 0 8px rgba(71, 255, 190, 0.6);
    }
    
    .status-dot.meta {
      background-color: rgba(0, 227, 255, 0.8);
      box-shadow: 0 0 8px rgba(0, 227, 255, 0.6);
    }
    
    .db-clusters {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    
    .db-cluster {
      padding: 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .db-cluster.llm-memory {
      border-top: 3px solid rgba(255, 68, 153, 0.8);
    }
    
    .db-cluster.agent-center {
      border-top: 3px solid rgba(108, 71, 255, 0.8);
    }
    
    .cluster-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.2rem;
    }
    
    .cluster-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }
    
    .cluster-icon {
      font-size: 1.3rem;
    }
    
    .cluster-title h3 {
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .cluster-badge {
      padding: 0.3rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.15);
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--gray-300);
    }
    
    .cluster-databases {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .db-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.8rem;
      background: rgba(12, 15, 35, 0.4);
      border-radius: var(--border-radius-sm);
      border: 1px solid rgba(108, 71, 255, 0.1);
      transition: var(--transition);
    }
    
    .db-item:hover {
      background: rgba(12, 15, 35, 0.6);
      border-color: rgba(108, 71, 255, 0.2);
    }
    
    .db-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.3rem;
    }
    
    .db-info {
      flex: 1;
    }
    
    .db-name {
      font-size: 0.95rem;
      font-weight: 600;
    }
    
    .db-type {
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    .db-status {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    
    .db-status.active {
      background-color: var(--status-success);
      box-shadow: 0 0 8px var(--status-success);
      animation: pulse-status 2s infinite;
    }
    
    .db-system {
      padding: 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .db-system::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: var(--db-indexeddb);
      transition: var(--transition);
    }
    
    .db-system:hover {
      background: rgba(12, 15, 35, 0.6);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 20px 40px rgba(3, 4, 16, 0.4);
    }
    
    .db-system:hover::before {
      height: 6px;
    }
    
    .db-system.indexed::before { background: var(--db-indexeddb); }
    .db-system.sql::before { background: var(--db-sqljs); }
    .db-system.duck::before { background: var(--db-duckdb); }
    .db-system.vector::before { background: var(--db-vector); }
    .db-system.cache::before { background: var(--db-cache); }
    
    .db-system-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.5rem;
    }
    
    .db-system-title h3 {
      font-size: 1.3rem;
      margin-bottom: 0.3rem;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .db-system-type {
      font-size: 0.9rem;
      color: var(--gray-400);
    }
    
    .db-system-agent {
      margin-top: 0.8rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.4rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.1);
      font-size: 0.8rem;
      font-weight: 500;
      border: 1px solid rgba(108, 71, 255, 0.2);
    }
    
    .db-system-agent span {
      opacity: 0.9;
    }
    
    .db-system-status {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      background: rgba(0, 255, 179, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border: 1px solid rgba(0, 255, 179, 0.3);
    }
    
    .db-system-status::before {
      content: '';
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: var(--status-success);
      box-shadow: 0 0 15px var(--status-success);
    }
    
    .db-system-metrics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.2rem;
      margin-top: 1.5rem;
    }
    
    .db-metric {
      padding: 1rem;
      border-radius: var(--border-radius-sm);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.1);
      transition: var(--transition);
    }
    
    .db-metric:hover {
      background: rgba(108, 71, 255, 0.1);
    }
    
    .db-metric-label {
      font-size: 0.8rem;
      color: var(--gray-400);
      margin-bottom: 0.3rem;
    }
    
    .db-metric-value {
      font-size: 1.1rem;
      font-weight: 700;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .db-system-progress {
      margin-top: 1.5rem;
    }
    
    .progress-bar {
      height: 8px;
      border-radius: 4px;
      background: rgba(12, 15, 35, 0.3);
      overflow: hidden;
      margin-top: 0.5rem;
      position: relative;
    }
    
    .progress-fill {
      height: 100%;
      border-radius: 4px;
      background: var(--db-indexeddb);
      transition: width 1s cubic-bezier(0.17, 0.67, 0.32, 1.33);
      position: relative;
    }
    
    .progress-fill::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
      animation: progress-shine 2s infinite;
    }
    
    @keyframes progress-shine {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    
    .progress-fill.indexed { background: var(--db-indexeddb); }
    .progress-fill.sql { background: var(--db-sqljs); }
    .progress-fill.duck { background: var(--db-duckdb); }
    .progress-fill.vector { background: var(--db-vector); }
    .progress-fill.cache { background: var(--db-cache); }
    
    .progress-label {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: var(--gray-400);
      margin-bottom: 0.5rem;
    }
    
    /* Performance Chart */
    .chart-container {
      width: 100%;
      height: 320px;
      position: relative;
      padding: 0.5rem;
    }
    
    /* Stats Cards */
    .stats-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }
    
    .stat-card {
      padding: 1.8rem 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
      text-align: center;
    }
    
    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      transition: var(--transition);
    }
    
    .stat-card:hover {
      background: rgba(12, 15, 35, 0.6);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 20px 40px rgba(3, 4, 16, 0.4);
    }
    
    .stat-card:hover::before {
      height: 6px;
    }
    
    .stat-value {
      font-size: 2.8rem;
      font-weight: 800;
      margin-bottom: 0.8rem;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
    }
    
    .stat-value::after {
      content: attr(data-value);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0.1;
      filter: blur(8px);
    }
    
    .stat-label {
      font-size: 0.9rem;
      color: var(--gray-400);
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 600;
      position: relative;
    }
    
    /* Database Health Gauge */
    .gauge-container {
      margin: 2.5rem auto;
      width: 220px;
      position: relative;
      transform-style: preserve-3d;
      perspective: 1000px;
    }
    
    .gauge {
      width: 220px;
      height: 110px;
      position: relative;
      overflow: hidden;
      border-radius: 110px 110px 0 0;
      background: rgba(12, 15, 35, 0.4);
      border: 1px solid rgba(108, 71, 255, 0.2);
      transform-style: preserve-3d;
      transform: translateZ(0);
    }
    
    .gauge-fill {
      position: absolute;
      width: 220px;
      height: 220px;
      border-radius: 50%;
      background: conic-gradient(
        var(--status-error) 0%,
        var(--status-warning) 30%,
        var(--status-success) 60%,
        var(--status-warning) 80%,
        var(--status-error) 100%
      );
      bottom: 0;
      transform-origin: center bottom;
      transform: rotate(0.4turn);
      transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
      box-shadow: 0 0 30px rgba(0, 255, 179, 0.2);
    }
    
    .gauge-mask {
      position: absolute;
      width: 180px;
      height: 180px;
      border-radius: 50%;
      background: var(--darker);
      bottom: -90px;
      left: 20px;
      box-shadow: inset 0 5px 15px rgba(3, 4, 16, 0.7);
    }
    
    .gauge-center {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 12px;
      height: 12px;
      background: var(--light);
      border-radius: 50%;
      transform: translateX(-50%);
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
    }
    
    .gauge-needle {
      position: absolute;
      width: 4px;
      height: 100px;
      background: var(--light);
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) rotate(0deg);
      transform-origin: bottom center;
      transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
      z-index: 2;
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
    
    .gauge-needle::before {
      content: '';
      position: absolute;
      width: 14px;
      height: 14px;
      background: linear-gradient(135deg, var(--light), var(--gray-300));
      border-radius: 50%;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.7);
    }
    
    .gauge-value {
      position: absolute;
      top: 75px;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 2rem;
      font-weight: 800;
      color: var(--light);
      text-shadow: 0 0 10px rgba(0, 255, 179, 0.5);
      transform-style: preserve-3d;
      transform: translateZ(20px);
    }
    
    .gauge-label {
      margin-top: 1.2rem;
      text-align: center;
      font-size: 0.9rem;
      color: var(--gray-400);
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 600;
    }
    
    /* System Health Status */
    .system-status {
      margin-top: 2.5rem;
      padding: 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(0, 255, 179, 0.2);
      display: flex;
      align-items: center;
      gap: 1.5rem;
      box-shadow: 0 10px 30px rgba(3, 4, 16, 0.4);
      transform-style: preserve-3d;
      transform: translateZ(0);
      transition: var(--transition);
    }
    
    .system-status:hover {
      background: rgba(0, 255, 179, 0.05);
      border-color: rgba(0, 255, 179, 0.3);
    }
    
    .status-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: rgba(0, 255, 179, 0.1);
      color: var(--status-success);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.8rem;
      border: 1px solid rgba(0, 255, 179, 0.3);
      box-shadow: 0 0 20px rgba(0, 255, 179, 0.2);
      transform: translateZ(15px);
    }
    
    .status-details {
      flex: 1;
      transform: translateZ(10px);
    }
    
    .status-title {
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 0.4rem;
      color: var(--status-success);
    }
    
    .status-message {
      font-size: 0.9rem;
      color: var(--gray-300);
    }
    
    /* Data Structure and Preview */
    .data-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }
    
    .structure-panel, .preview-panel {
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      padding: 1.5rem;
      height: 100%;
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .structure-panel:hover, .preview-panel:hover {
      background: rgba(12, 15, 35, 0.5);
      border-color: rgba(108, 71, 255, 0.25);
    }
    
    .structure-panel h3, .preview-panel h3 {
      margin-bottom: 1.2rem;
      font-size: 1.1rem;
      color: var(--gray-300);
    }
    
    .schema-list {
      display: flex;
      flex-direction: column;
      gap: 1.2rem;
    }
    
    .schema-item {
      border-radius: var(--border-radius-sm);
      overflow: hidden;
      border: 1px solid rgba(108, 71, 255, 0.1);
      background: rgba(12, 15, 35, 0.2);
      transition: var(--transition);
    }
    
    .schema-item:hover {
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 10px 20px rgba(3, 4, 16, 0.3);
      cursor: pointer;
    }
    
    .schema-item.active {
      border-color: rgba(108, 71, 255, 0.5);
      background: rgba(108, 71, 255, 0.1);
    }
    
    .schema-header {
      padding: 0.8rem 1rem;
      background: rgba(108, 71, 255, 0.1);
      font-weight: 600;
      font-size: 0.95rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .schema-badge {
      padding: 0.25rem 0.6rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.2);
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--primary-light);
    }
    
    .schema-fields {
      padding: 0.5rem 0;
    }
    
    .field-item {
      padding: 0.6rem 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(108, 71, 255, 0.05);
      transition: var(--transition);
    }
    
    .field-item:hover {
      background: rgba(108, 71, 255, 0.05);
    }
    
    .field-item:last-child {
      border-bottom: none;
    }
    
    .field-name {
      font-family: 'Space Mono', monospace;
      font-size: 0.85rem;
      color: var(--primary-light);
    }
    
    .field-type {
      font-size: 0.75rem;
      color: var(--gray-400);
      background: rgba(12, 15, 35, 0.4);
      padding: 0.25rem 0.6rem;
      border-radius: var(--border-radius-pill);
      border: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .preview-empty {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1.5rem;
      color: var(--gray-500);
    }
    
    .preview-empty-icon {
      font-size: 3.5rem;
      opacity: 0.6;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: spin 3s infinite ease-in-out;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      50% { transform: rotate(180deg); }
      100% { transform: rotate(360deg); }
    }
    
    .data-preview {
      height: 100%;
      overflow: auto;
    }
    
    .data-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 0.85rem;
    }
    
    .data-table th {
      background: rgba(108, 71, 255, 0.1);
      padding: 0.5rem 0.75rem;
      text-align: left;
      border-bottom: 1px solid rgba(108, 71, 255, 0.2);
      color: var(--primary-light);
      position: sticky;
      top: 0;
      z-index: 10;
    }
    
    .data-table td {
      padding: 0.5rem 0.75rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .data-table tr:hover {
      background: rgba(108, 71, 255, 0.05);
    }
    
    .data-table tr:nth-child(even) {
      background: rgba(12, 15, 35, 0.2);
    }
    
    /* Operation Logs */
    .logs-container {
      font-family: 'Space Mono', monospace;
      font-size: 0.8rem;
      height: 300px;
      overflow-y: auto;
      padding-right: 0.5rem;
    }
    
    .log-entry {
      padding: 0.85rem 1rem;
      border-radius: var(--border-radius-sm);
      margin-bottom: 0.7rem;
      display: flex;
      background: rgba(12, 15, 35, 0.3);
      border-left: 3px solid transparent;
      transition: var(--transition);
      transform-style: preserve-3d;
      transform: translateZ(0);
    }
    
    .log-entry:hover {
      background: rgba(12, 15, 35, 0.5);
    }
    
    .log-timestamp {
      color: var(--gray-500);
      margin-right: 1rem;
      flex-shrink: 0;
    }
    
    .log-content {
      flex: 1;
    }
    
    .log-operation {
      font-weight: 600;
      margin-right: 0.5rem;
    }
    
    .log-info {
      border-left-color: var(--tertiary);
    }
    
    .log-info .log-operation {
      color: var(--tertiary);
    }
    
    .log-success {
      border-left-color: var(--status-success);
    }
    
    .log-success .log-operation {
      color: var(--status-success);
    }
    
    .log-warning {
      border-left-color: var(--status-warning);
    }
    
    .log-warning .log-operation {
      color: var(--status-warning);
    }
    
    .log-error {
      border-left-color: var(--status-error);
    }
    
    .log-error .log-operation {
      color: var(--status-error);
    }
    
    /* System Metrics */
    .system-metrics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.2rem;
    }
    
    .system-metric {
      padding: 1.2rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .system-metric:hover {
      background: rgba(12, 15, 35, 0.5);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 10px 20px rgba(3, 4, 16, 0.3);
    }
    
    .system-metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }
    
    .system-metric-label {
      display: flex;
      align-items: center;
      gap: 0.7rem;
      font-weight: 600;
      font-size: 0.95rem;
    }
    
    .system-metric-icon {
      width: 36px;
      height: 36px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.1rem;
      color: var(--primary);
      border: 1px solid rgba(108, 71, 255, 0.2);
    }
    
    .system-metric-value {
      font-weight: 700;
      font-size: 1.1rem;
    }
    
    .metric-bar {
      height: 8px;
      border-radius: 4px;
      background: rgba(12, 15, 35, 0.3);
      overflow: hidden;
      margin-top: 0.8rem;
      position: relative;
    }
    
    .metric-fill {
      height: 100%;
      border-radius: 4px;
      transition: width 1s cubic-bezier(0.17, 0.67, 0.32, 1.33);
      position: relative;
    }
    
    .metric-fill::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
      animation: progress-shine 2s infinite;
    }
    
    .metric-fill.cpu { background: var(--primary); }
    .metric-fill.gpu { background: var(--secondary); }
    .metric-fill.ram { background: var(--tertiary); }
    .metric-fill.network { background: var(--accent-1); }
    
    .metric-details {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: var(--gray-400);
      margin-top: 0.7rem;
    }
    
    /* USB Devices */
    .usb-devices {
      margin-top: 2rem;
    }
    
    .usb-devices-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.2rem;
    }
    
    .usb-devices-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--gray-300);
    }
    
    .usb-devices-count {
      padding: 0.3rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.15);
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--primary-light);
    }
    
    .usb-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.2rem;
      perspective: 1000px;
    }
    
    .usb-device {
      padding: 1.2rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .usb-device:hover {
      background: rgba(12, 15, 35, 0.5);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 20px 40px rgba(3, 4, 16, 0.4);
    }
    
    .usb-device-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
    }
    
    .usb-device-info {
      display: flex;
      align-items: center;
      gap: 0.9rem;
    }
    
    .usb-device-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      border: 1px solid rgba(108, 71, 255, 0.2);
      color: var(--primary);
      transform: translateZ(10px);
    }
    
    .usb-device-name {
      font-weight: 600;
      font-size: 1rem;
      margin-bottom: 0.3rem;
    }
    
    .usb-device-type {
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    .usb-device-status {
      padding: 0.3rem 0.7rem;
      border-radius: var(--border-radius-pill);
      font-size: 0.75rem;
      font-weight: 600;
      transform: translateZ(15px);
    }
    
    .usb-device-status.connected {
      background: rgba(0, 255, 179, 0.1);
      color: var(--status-success);
      border: 1px solid rgba(0, 255, 179, 0.3);
      box-shadow: 0 0 15px rgba(0, 255, 179, 0.3);
      animation: pulse-status 2s infinite;
    }
    
    @keyframes pulse-status {
      0% { box-shadow: 0 0 10px rgba(0, 255, 179, 0.2); }
      50% { box-shadow: 0 0 20px rgba(0, 255, 179, 0.5); }
      100% { box-shadow: 0 0 10px rgba(0, 255, 179, 0.2); }
    }
    
    /* Diagnostics Styles */
    .diagnostics-container {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .diagnostics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .diagnostics-header h3 {
      font-size: 1.2rem;
      font-weight: 600;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .diagnostics-badge {
      padding: 0.3rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(255, 68, 153, 0.1);
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--accent-1);
      border: 1px solid rgba(255, 68, 153, 0.2);
    }
    
    .diagnostics-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1.2rem;
    }
    
    .diagnostics-metric {
      padding: 1rem;
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .diagnostics-metric:hover {
      background: rgba(12, 15, 35, 0.4);
      border-color: rgba(108, 71, 255, 0.25);
    }
    
    .metric-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }
    
    .metric-icon {
      font-size: 1.2rem;
    }
    
    .metric-title {
      font-size: 0.9rem;
      font-weight: 600;
    }
    
    .metric-value {
      font-size: 1.6rem;
      font-weight: 700;
      margin-bottom: 0.75rem;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .heartbeat-indicator {
      height: 30px;
      position: relative;
      overflow: hidden;
    }
    
    .heartbeat-line {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(to right, rgba(108, 71, 255, 0.1), rgba(108, 71, 255, 0.1));
      transform: translateY(-50%);
    }
    
    .heartbeat-line::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(0, 255, 179, 0), rgba(0, 255, 179, 1), rgba(0, 255, 179, 0));
      animation: heartbeat 1.5s infinite;
    }
    
    @keyframes heartbeat {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    
    .waveform-visualization {
      height: 30px;
      position: relative;
      overflow: hidden;
    }
    
    .waveform-line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 200'%3E%3Cpath d='M0,100 C150,0 150,200 300,100 C450,0 450,200 600,100 C750,0 750,200 900,100 C1050,0 1050,200 1200,100' stroke='white' stroke-width='2' fill='none'/%3E%3C/svg%3E");
      mask-size: 100% 100%;
      animation: waveform-move 5s linear infinite;
    }
    
    @keyframes waveform-move {
      0% { transform: translateX(0); }
      100% { transform: translateX(-50%); }
    }
    
    .drift-chart {
      height: 20px;
      background: linear-gradient(to right, 
        rgba(0, 255, 179, 0.3) 0%, 
        rgba(0, 255, 179, 0.3) 30%, 
        rgba(255, 225, 76, 0.3) 30%, 
        rgba(255, 225, 76, 0.3) 70%, 
        rgba(255, 76, 111, 0.3) 70%, 
        rgba(255, 76, 111, 0.3) 100%);
      border-radius: 10px;
      position: relative;
    }
    
    .drift-marker {
      position: absolute;
      top: 50%;
      width: 10px;
      height: 10px;
      background: white;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 0 10px white;
    }
    
    .radar-chart-container {
      margin-top: 1rem;
      position: relative;
    }
    
    .radar-legend {
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;
      margin-top: 0.5rem;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.8rem;
    }
    
    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }
    
    .legend-color.llm {
      background: rgba(255, 68, 153, 0.8);
    }
    
    .legend-color.agent {
      background: rgba(108, 71, 255, 0.8);
    }
    
    .legend-color.worker {
      background: rgba(255, 193, 7, 0.8);
    }
    
    .legend-color.todo {
      background: rgba(71, 255, 190, 0.8);
    }
    
    .legend-color.meta {
      background: rgba(0, 227, 255, 0.8);
    }
    
    /* Notepad Metrics Visualization */
    .notepad-metrics-visualization {
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(12, 15, 35, 0.2);
      border-radius: var(--border-radius-sm);
      border: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .metrics-header h4 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--gray-300);
    }
    
    .metrics-chart {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .chart-row {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .chart-label {
      width: 120px;
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    .chart-value {
      width: 60px;
      font-size: 0.8rem;
      font-weight: 600;
      text-align: right;
    }

    /* Loading animation */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: var(--darker);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      opacity: 1;
      transition: opacity 0.5s;
    }

    .loading-overlay.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(108, 71, 255, 0.3);
      border-radius: 50%;
      border-top-color: var(--primary);
      animation: spin 1s infinite linear;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Responsive styles */
    @media (max-width: 1200px) {
      .dashboard {
        grid-template-columns: 1fr;
      }
      
      .dashboard-column.left, 
      .dashboard-column.right {
        grid-column: 1 / -1;
      }
      
      .data-section {
        grid-template-columns: 1fr;
      }
      
      .stats-cards {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: 2.5rem;
      }
      
      .hero-subtitle {
        font-size: 1rem;
      }
      
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .button {
        width: 100%;
      }
      
      .main-header {
        flex-direction: column;
        gap: 1rem;
      }
      
      .header-actions {
        flex-direction: column;
        width: 100%;
      }
      
      .header-metrics {
        width: 100%;
        justify-content: space-between;
      }
      
      .stats-cards {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <!-- Grid Background -->
  <div class="grid-bg"></div>
  
  <!-- Particles Container -->
  <div class="particles-container" id="particles"></div>
  
  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay">
    <div class="spinner"></div>
  </div>
  
  <!-- Main Header -->
  <header class="main-header">
    <div class="brand">
      <div class="logo">AL</div>
      <h1 class="brand-name">Agent Lee's DB</h1>
    </div>
    
    <div class="connection-badge connection-connected">
      <span class="connection-indicator"></span>
      <span class="connection-text">Connected to LLM Center</span>
    </div>
    
    <div class="header-actions">
      <button class="theme-toggle" id="themeToggle">
        <i class="fas fa-moon"></i>
      </button>
      
      <div class="header-metrics">
        <div class="metric-pill">
          <span class="metric-pill-icon"><i class="fas fa-database"></i></span>
          <span>Databases: <span>5</span></span>
        </div>
        
        <div class="metric-pill">
          <span class="metric-pill-icon"><i class="fas fa-table"></i></span>
          <span>Tables: <span>24</span></span>
        </div>
      </div>
    </div>
  </header>
  
  <!-- Hero Section -->
  <section class="hero-section">
    <h1 class="hero-title">Multi-Database Command Center</h1>
    <p class="hero-subtitle">
      Manage and monitor all your data sources in one place with Agent Lee's Database Management System
    </p>
    
    <div class="action-buttons">
      <button class="button button-primary">
        <span class="button-icon"><i class="fas fa-database"></i></span>
        <span class="button-text">Connect Database</span>
      </button>
      
      <button class="button button-secondary">
        <span class="button-icon"><i class="fas fa-chart-line"></i></span>
        <span class="button-text">View Reports</span>
      </button>
      
      <button class="button button-secondary">
        <span class="button-icon"><i class="fas fa-cog"></i></span>
        <span class="button-text">System Settings</span>
      </button>
    </div>
  </section>
  
  <!-- Dashboard -->
  <div class="dashboard">
    <div class="dashboard-column left">
      <!-- Pentagon Database Visualization -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon"><i class="fas fa-project-diagram"></i></div>
            <span>Database Cluster</span>
          </div>
          
          <div class="card-badge">5 Nodes</div>
        </div>
        
        <div class="card-content">
          <div class="pentagon-container">
            <div class="pentagon-visualization">
              <svg width="300" height="300" viewBox="0 0 300 300" class="pentagon-svg">
                <!-- Pentagon Shape -->
                <polygon 
                  points="150,30 270,120 220,260 80,260 30,120" 
                  fill="none" 
                  stroke="rgba(108, 71, 255, 0.3)" 
                  stroke-width="2"
                />
                
                <!-- Connection Lines -->
                <line x1="150" y1="30" x2="270" y2="120" stroke="rgba(0, 227, 255, 0.2)" stroke-width="1" />
                <line x1="270" y1="120" x2="220" y2="260" stroke="rgba(0, 227, 255, 0.2)" stroke-width="1" />
                <line x1="220" y1="260" x2="80" y2="260" stroke="rgba(0, 227, 255, 0.2)" stroke-width="1" />
                <line x1="80" y1="260" x2="30" y2="120" stroke="rgba(0, 227, 255, 0.2)" stroke-width="1" />
                <line x1="30" y1="120" x2="150" y2="30" stroke="rgba(0, 227, 255, 0.2)" stroke-width="1" />
                
                <!-- Cross Lines -->
                <line x1="150" y1="30" x2="220" y2="260" stroke="rgba(0, 227, 255, 0.1)" stroke-width="1" />
                <line x1="150" y1="30" x2="80" y2="260" stroke="rgba(0, 227, 255, 0.1)" stroke-width="1" />
                <line x1="270" y1="120" x2="80" y2="260" stroke="rgba(0, 227, 255, 0.1)" stroke-width="1" />
                <line x1="270" y1="120" x2="30" y2="120" stroke="rgba(0, 227, 255, 0.1)" stroke-width="1" />
                <line x1="220" y1="260" x2="30" y2="120" stroke="rgba(0, 227, 255, 0.1)" stroke-width="1" />
                
                <!-- Nodes -->
                <circle cx="150" cy="30" r="18" fill="rgba(255, 68, 153, 0.2)" stroke="rgba(255, 68, 153, 0.8)" stroke-width="2" class="cluster-node" data-node="llm" />
                <circle cx="270" cy="120" r="18" fill="rgba(108, 71, 255, 0.2)" stroke="rgba(108, 71, 255, 0.8)" stroke-width="2" class="cluster-node" data-node="agent" />
                <circle cx="220" cy="260" r="18" fill="rgba(255, 193, 7, 0.2)" stroke="rgba(255, 193, 7, 0.8)" stroke-width="2" class="cluster-node" data-node="worker" />
                <circle cx="80" cy="260" r="18" fill="rgba(71, 255, 190, 0.2)" stroke="rgba(71, 255, 190, 0.8)" stroke-width="2" class="cluster-node" data-node="todo" />
                <circle cx="30" cy="120" r="18" fill="rgba(0, 227, 255, 0.2)" stroke="rgba(0, 227, 255, 0.8)" stroke-width="2" class="cluster-node" data-node="meta" />
                
                <!-- Node Labels -->
                <text x="150" y="35" text-anchor="middle" fill="#fff" font-size="12">LLM</text>
                <text x="270" y="125" text-anchor="middle" fill="#fff" font-size="12">AGENT</text>
                <text x="220" y="265" text-anchor="middle" fill="#fff" font-size="12">WORKER</text>
                <text x="80" y="265" text-anchor="middle" fill="#fff" font-size="12">TODO</text>
                <text x="30" y="125" text-anchor="middle" fill="#fff" font-size="12">META</text>
                
                <!-- Center Node -->
                <circle cx="150" cy="150" r="25" fill="rgba(108, 71, 255, 0.1)" stroke="rgba(108, 71, 255, 0.5)" stroke-width="2" />
                <text x="150" y="155" text-anchor="middle" fill="#fff" font-size="14">CORE</text>
              </svg>
            </div>
            
            <div class="pentagon-status">
              <h3 style="margin-bottom: 15px; font-size: 1rem;">Node Status</h3>
              
              <div class="cluster-status">
                <div class="status-item">
                  <span class="status-dot llm"></span>
                  <span>LLM Memory</span>
                </div>
                
                <div class="status-item">
                  <span class="status-dot agent"></span>
                  <span>Agent Center</span>
                </div>
                
                <div class="status-item">
                  <span class="status-dot worker"></span>
                  <span>Worker Processes</span>
                </div>
                
                <div class="status-item">
                  <span class="status-dot todo"></span>
                  <span>Task & Todo List</span>
                </div>
                
                <div class="status-item">
                  <span class="status-dot meta"></span>
                  <span>Metadata Storage</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="db-clusters">
            <div class="db-cluster llm-memory">
              <div class="cluster-header">
                <div class="cluster-title">
                  <i class="fas fa-brain cluster-icon" style="color: var(--accent-1);"></i>
                  <h3>LLM Memory Cluster</h3>
                </div>
                
                <div class="cluster-badge">2 Databases</div>
              </div>
              
              <div class="cluster-databases">
                <div class="db-item">
                  <div class="db-icon" style="color: var(--accent-1);">
                    <i class="fas fa-vector-square"></i>
                  </div>
                  
                  <div class="db-info">
                    <div class="db-name">Vector Embeddings</div>
                    <div class="db-type">VectorDB Storage</div>
                  </div>
                  
                  <div class="db-status active"></div>
                </div>
                
                <div class="db-item">
                  <div class="db-icon" style="color: var(--accent-1);">
                    <i class="fas fa-memory"></i>
                  </div>
                  
                  <div class="db-info">
                    <div class="db-name">LLM Cache</div>
                    <div class="db-type">Key-Value Store</div>
                  </div>
                  
                  <div class="db-status active"></div>
                </div>
              </div>
            </div>
            
            <div class="db-cluster agent-center">
              <div class="cluster-header">
                <div class="cluster-title">
                  <i class="fas fa-user-robot cluster-icon" style="color: var(--primary);"></i>
                  <h3>Agent Center Cluster</h3>
                </div>
                
                <div class="cluster-badge">3 Databases</div>
              </div>
              
              <div class="cluster-databases">
                <div class="db-item">
                  <div class="db-icon" style="color: var(--primary);">
                    <i class="fas fa-database"></i>
                  </div>
                  
                  <div class="db-info">
                    <div class="db-name">Agent States</div>
                    <div class="db-type">IndexedDB Storage</div>
                  </div>
                  
                  <div class="db-status active"></div>
                </div>
                
                <div class="db-item">
                  <div class="db-icon" style="color: var(--primary);">
                    <i class="fas fa-project-diagram"></i>
                  </div>
                  
                  <div class="db-info">
                    <div class="db-name">Agent Relationships</div>
                    <div class="db-type">Graph Database</div>
                  </div>
                  
                  <div class="db-status active"></div>
                </div>
                
                <div class="db-item">
                  <div class="db-icon" style="color: var(--primary);">
                    <i class="fas fa-history"></i>
                  </div>
                  
                  <div class="db-info">
                    <div class="db-name">Agent History</div>
                    <div class="db-type">Time-Series DB</div>
                  </div>
                  
                  <div class="db-status active"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Database Health -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon"><i class="fas fa-heartbeat"></i></div>
            <span>Database Health</span>
          </div>
          
          <div class="card-badge">Real-time</div>
        </div>
        
        <div class="card-content">
          <div class="gauge-container">
            <div class="gauge">
              <div class="gauge-fill" id="healthGaugeFill"></div>
              <div class="gauge-mask"></div>
              <div class="gauge-center"></div>
              <div class="gauge-needle" id="healthGaugeNeedle"></div>
              <div class="gauge-value" id="healthGaugeValue">96%</div>
            </div>
            <div class="gauge-label">OVERALL SYSTEM HEALTH</div>
          </div>
          
          <div class="system-status">
            <div class="status-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            
            <div class="status-details">
              <div class="status-title">All Systems Operational</div>
              <div class="status-message">
                All database connections are active and responding normally. Last check completed at 14:32:15.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="dashboard-column right">
      <!-- Database Systems -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon"><i class="fas fa-server"></i></div>
            <span>Database Systems</span>
          </div>
          
          <div class="card-badge">5 Systems</div>
        </div>
        
        <div class="card-content">
          <div class="db-systems">
            <div class="db-system indexed">
              <div class="db-system-header">
                <div class="db-system-title">
                  <h3>IndexedDB</h3>
                  <div class="db-system-type">Browser Storage Engine</div>
                  <div class="db-system-agent">
                    <i class="fas fa-link"></i>
                    <span>Connected to Agent Center</span>
                  </div>
                </div>
                
                <div class="db-system-status"></div>
              </div>
              
              <div class="db-system-metrics">
                <div class="db-metric">
                  <div class="db-metric-label">Total Records</div>
                  <div class="db-metric-value">48,291</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Storage Used</div>
                  <div class="db-metric-value">12.4 MB</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Tables</div>
                  <div class="db-metric-value">8</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Indexes</div>
                  <div class="db-metric-value">22</div>
                </div>
              </div>
              
              <div class="db-system-progress">
                <div class="progress-label">
                  <div>Performance</div>
                  <div>95%</div>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill indexed" style="width: 95%;"></div>
                </div>
              </div>
            </div>
            
            <div class="db-system sql">
              <div class="db-system-header">
                <div class="db-system-title">
                  <h3>SQL.js</h3>
                  <div class="db-system-type">In-Memory SQL Database</div>
                  <div class="db-system-agent">
                    <i class="fas fa-link"></i>
                    <span>Connected to Task System</span>
                  </div>
                </div>
                
                <div class="db-system-status"></div>
              </div>
              
              <div class="db-system-metrics">
                <div class="db-metric">
                  <div class="db-metric-label">Total Records</div>
                  <div class="db-metric-value">32,178</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Storage Used</div>
                  <div class="db-metric-value">8.7 MB</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Tables</div>
                  <div class="db-metric-value">12</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Indexes</div>
                  <div class="db-metric-value">18</div>
                </div>
              </div>
              
              <div class="db-system-progress">
                <div class="progress-label">
                  <div>Performance</div>
                  <div>87%</div>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill sql" style="width: 87%;"></div>
                </div>
              </div>
            </div>
            
            <div class="db-system vector">
              <div class="db-system-header">
                <div class="db-system-title">
                  <h3>Vector Database</h3>
                  <div class="db-system-type">Embeddings Storage</div>
                  <div class="db-system-agent">
                    <i class="fas fa-link"></i>
                    <span>Connected to LLM Brain</span>
                  </div>
                </div>
                
                <div class="db-system-status"></div>
              </div>
              
              <div class="db-system-metrics">
                <div class="db-metric">
                  <div class="db-metric-label">Vectors</div>
                  <div class="db-metric-value">15,621</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Dimensions</div>
                  <div class="db-metric-value">1,536</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Collections</div>
                  <div class="db-metric-value">3</div>
                </div>
                
                <div class="db-metric">
                  <div class="db-metric-label">Avg. Query Time</div>
                  <div class="db-metric-value">124ms</div>
                </div>
              </div>
              
              <div class="db-system-progress">
                <div class="progress-label">
                  <div>Performance</div>
                  <div>92%</div>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill vector" style="width: 92%;"></div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Stats Cards -->
          <div class="stats-cards" style="margin-top: 2rem;">
            <div class="stat-card">
              <div class="stat-value" data-value="42,897">42,897</div>
              <div class="stat-label">Queries Per Hour</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-value" data-value="5">5</div>
              <div class="stat-label">Active Systems</div>
            </div>
            
            <div class="stat-card">
              <div class="stat-value" data-value="98.7%">98.7%</div>
              <div class="stat-label">Uptime</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Data Structure & Preview -->
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon"><i class="fas fa-table"></i></div>
            <span>Data Explorer</span>
          </div>
          
          <div class="card-badge">Agent Lee System</div>
        </div>
        
        <div class="card-content">
          <div class="data-section">
            <div class="structure-panel">
              <h3>Database Structure</h3>
              
              <div class="schema-list">
                <div class="schema-item active">
                  <div class="schema-header">
                    <span>agent_states</span>
                    <span class="schema-badge">12 fields</span>
                  </div>
                  
                  <div class="schema-fields">
                    <div class="field-item">
                      <span class="field-name">id</span>
                      <span class="field-type">string</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">agent_name</span>
                      <span class="field-type">string</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">status</span>
                      <span class="field-type">enum</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">last_active</span>
                      <span class="field-type">timestamp</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">current_task</span>
                      <span class="field-type">object</span>
                    </div>
                  </div>
                </div>
                
                <div class="schema-item">
                  <div class="schema-header">
                    <span>tasks</span>
                    <span class="schema-badge">8 fields</span>
                  </div>
                </div>
                
                <div class="schema-item">
                  <div class="schema-header">
                    <span>memory_cache</span>
                    <span class="schema-badge">5 fields</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="preview-panel">
              <h3>Data Preview</h3>
              
              <div class="data-preview">
                <table class="data-table">
                  <thead>
                    <tr>
                      <th>id</th>
                      <th>agent_name</th>
                      <th>status</th>
                      <th>last_active</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>ag_001</td>
                      <td>Lily</td>
                      <td>active</td>
                      <td>2025-05-31 14:05:23</td>
                    </tr>
                    <tr>
                      <td>ag_002</td>
                      <td>Gabriel</td>
                      <td>active</td>
                      <td>2025-05-31 14:03:17</td>
                    </tr>
                    <tr>
                      <td>ag_003</td>
                      <td>Adam</td>
                      <td>idle</td>
                      <td>2025-05-31 13:45:12</td>
                    </tr>
                    <tr>
                      <td>ag_004</td>
                      <td>Avery</td>
                      <td>active</td>
                      <td>2025-05-31 14:07:45</td>
                    </tr>
                    <tr>
                      <td>ag_005</td>
                      <td>Emma</td>
                      <td>active</td>
                      <td>2025-05-31 14:01:38</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Create particle animation
    function createParticles() {
      const container = document.getElementById('particles');
      const particleCount = 50;
      
      for (let i = 0; i < particleCount; i++) {
        createParticle(container);
      }
      
      setInterval(() => {
        if (Math.random() > 0.5) {
          createParticle(container);
        }
      }, 500);
    }
    
    function createParticle(container) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      
      // Random position
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      
      // Random size
      const size = Math.random() * 4 + 1;
      
      // Random color
      const colors = [
        'rgba(108, 71, 255, 0.7)',
        'rgba(0, 227, 255, 0.7)',
        'rgba(255, 68, 153, 0.7)',
        'rgba(71, 255, 190, 0.7)'
      ];
      const color = colors[Math.floor(Math.random() * colors.length)];
      
      // Set styles
      particle.style.left = `${x}%`;
      particle.style.top = `${y}%`;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.background = color;
      
      // Add animation
      const duration = Math.random() * 30 + 20;
      const delay = Math.random() * 5;
      particle.style.animation = `float ${duration}s ${delay}s infinite`;
      
      // Append to container
      container.appendChild(particle);
      
      // Remove after some time to prevent overloading
      setTimeout(() => {
        particle.remove();
      }, (duration + delay) * 1000);
    }
    
    // Health gauge animation
    function animateHealthGauge() {
      const needle = document.getElementById('healthGaugeNeedle');
      const value = document.getElementById('healthGaugeValue');
      
      // Initialize position
      let health = 96;
      let angle = (health / 100) * 180 - 90;
      needle.style.transform = `translateX(-50%) rotate(${angle}deg)`;
      
      // Random fluctuations
      setInterval(() => {
        const change = Math.random() * 4 - 2; // -2 to 2
        health = Math.max(0, Math.min(100, health + change));
        angle = (health / 100) * 180 - 90;
        
        needle.style.transform = `translateX(-50%) rotate(${angle}deg)`;
        value.textContent = `${Math.round(health)}%`;
      }, 3000);
    }
    
    // Theme toggle
    function setupThemeToggle() {
      const themeToggle = document.getElementById('themeToggle');
      
      themeToggle.addEventListener('click', () => {
        document.body.classList.toggle('light-theme');
        const icon = themeToggle.querySelector('i');
        
        if (document.body.classList.contains('light-theme')) {
          icon.className = 'fas fa-sun';
        } else {
          icon.className = 'fas fa-moon';
        }
      });
    }
    
    // Pentagon Node Interaction
    function setupPentagonNodes() {
      const nodes = document.querySelectorAll('.cluster-node');
      
      nodes.forEach(node => {
        node.addEventListener('mouseover', () => {
          node.setAttribute('r', '22');
        });
        
        node.addEventListener('mouseout', () => {
          node.setAttribute('r', '18');
        });
        
        node.addEventListener('click', () => {
          const nodeName = node.getAttribute('data-node');
          alert(`${nodeName.toUpperCase()} Database Cluster selected`);
        });
      });
    }
    
    // Document Ready
    document.addEventListener('DOMContentLoaded', () => {
      // Hide loading overlay
      setTimeout(() => {
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.classList.add('hidden');
      }, 1500);
      
      // Initialize animations and interactions
      createParticles();
      animateHealthGauge();
      setupThemeToggle();
      setupPentagonNodes();
    });
  </script>
</body>
</html>