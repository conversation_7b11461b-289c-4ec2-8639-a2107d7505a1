# Agent Lee™ Complete System Startup
# Starts both backend and Electron frontend

Write-Host "🤖 Agent Lee™ Complete System Startup" -ForegroundColor Cyan
Write-Host "=" * 50

# Stop any existing backend processes on port 8000
Write-Host "🔄 Checking for existing processes on port 8000..."
$portUsed = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
if ($portUsed) {
    $processId = $portUsed.OwningProcess
    Write-Host "   Stopping existing process (PID: $processId)..."
    Stop-Process -Id $processId -Force
    Start-Sleep -Seconds 2
    Write-Host "   ✅ Process stopped" -ForegroundColor Green
} else {
    Write-Host "   ✅ Port 8000 is available" -ForegroundColor Green
}

# Check if virtual environment is activated
if (-not $env:VIRTUAL_ENV) {
    Write-Host "⚠️ Virtual environment not detected. Attempting to activate..." -ForegroundColor Yellow
    
    $venvPaths = @(".\venv\Scripts\Activate.ps1", ".\.venv\Scripts\Activate.ps1")
    $activated = $false
    
    foreach ($venvPath in $venvPaths) {
        if (Test-Path $venvPath) {
            Write-Host "🔄 Activating virtual environment: $venvPath"
            & $venvPath
            $activated = $true
            break
        }
    }
    
    if (-not $activated) {
        Write-Host "❌ No virtual environment found. Please activate manually." -ForegroundColor Red
        Write-Host "   Try: .\.venv\Scripts\Activate.ps1" -ForegroundColor Yellow
        exit 1
    }
}

# Check if Node.js is available
Write-Host "🔄 Checking Node.js installation..."
try {
    $nodeVersion = node --version 2>$null
    $npmVersion = npm --version 2>$null
    Write-Host "   ✅ Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "   ✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js from https://nodejs.org" -ForegroundColor Red
    exit 1
}

# Install Electron dependencies if needed
Write-Host "🔄 Checking Electron dependencies..."
if (-not (Test-Path "electron\node_modules")) {
    Write-Host "   Installing Electron dependencies..."
    cd electron
    npm install
    cd ..
    Write-Host "   ✅ Dependencies installed" -ForegroundColor Green
} else {
    Write-Host "   ✅ Dependencies already installed" -ForegroundColor Green
}

# Start the complete Agent Lee system
Write-Host "`n🚀 Starting Agent Lee™ Complete System..." -ForegroundColor Cyan
Write-Host "   This will start both backend and frontend automatically"
Write-Host "   Press Ctrl+C to stop the entire system"
Write-Host "-" * 50

try {
    cd electron
    npm start
} catch {
    Write-Host "❌ Failed to start Agent Lee system: $_" -ForegroundColor Red
    exit 1
} finally {
    cd ..
}

Write-Host "`n🛑 Agent Lee™ System stopped" -ForegroundColor Yellow
