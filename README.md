# Agent Lee™ System Assistant

A modular, embeddable agent system with full desktop functionality and real-time diagnostics.

## Features

* 🧠 Fully animated SPA dashboards (To-Do List, DB Manager, Agent Center, LLM Brain, Workers Center)
* ⚙️ Local backend controller that can control the real computer (browser, apps, mic, cam)
* 🧾 Full permissions handling
* 🧰 Real task processing (not mock data)
* 💬 Voice interaction, web search, and system diagnostics
* 📦 Cross-platform packaging with Electron
* 🛠 GitHub Releases setup for downloadable distribution

## Dashboard Modules

* **Agent OS Launcher** (`index.html`) - The main interface to access all modules
* **Dynamic To-Do List** (`72kh0rx6ss.html`) - Task tracking and shared notepad
* **Agent Center** (`ikuoqw85ur.html`) - AI agent management with holographic displays
* **Database Manager** (`aqylvu95i5.html`) - Multi-database command center
* **LLM Brain Center** (`jfy4eqk6tq.html`) - Neural network visualization and tool management
* **Workers Center** (`s3enpoc0b9.html`) - Background worker management and diagnostics
* **Agent Card Demo** (`30bkve4wz4.html`) - Embeddable assistant for any website

## Backend API Endpoints

### Core Diagnostic Endpoints

| Endpoint | Purpose |
|----------|---------|
| `GET /api/system_status` | Returns full readiness: LLM, DB, Notepad, Agents, Workers |
| `POST /api/speak` | Speak via TTS (fallbacks to browser if error) |
| `GET /api/check_modules` | Returns agent count, to-do queue, system metrics |
| `POST /api/assign_tasks` | Match workers to agents/tasks |
| `GET /api/version` | Return version/build string |
| `POST /api/voice_command` | Process voice commands and execute actions |

### System Control Endpoints

| Endpoint | Purpose |
|----------|---------|
| `POST /api/open_chrome` | Launch Chrome browser with URL |
| `GET /api/task_queue` | Get current task queue status |
| `POST /api/llm_think` | Simulate LLM processing |
| `POST /api/start_voice_recognition` | Start voice recognition |
| `POST /api/stop_voice_recognition` | Stop voice recognition |

## Running The Application

### Development Mode

1. Start the backend controller:
   ```bash
   cd backend
   # On Windows
   .\start_backend.ps1
   # On macOS/Linux
   ./start_backend.sh
   ```

2. Open the frontend:
   ```bash
   # Open index.html in your browser
   # Backend will be available at http://localhost:8000
   ```

3. Test the API connection:
   ```bash
   curl http://localhost:8000/api/system_status
   ```

   Expected output:
   ```json
   {
     "llm_ready": true,
     "llm_fallback": true,
     "agents_ready": true,
     "workers_ready": true,
     "db_ready": true,
     "notepad_ready": true,
     "system_health": 95.3,
     "last_update": "2023-12-07T14:30:15.123456"
   }
   ```

### Desktop Application (Electron)

1. Install dependencies:
   ```bash
   cd electron
   npm install
   ```

2. Run the application:
   ```bash
   npm start
   ```

### API Testing

Test the speech functionality:
```bash
curl -X POST http://localhost:8000/api/speak \
  -H "Content-Type: application/json" \
  -d '{"text": "All systems are operational", "rate": 1.0}'
```

Test voice command processing:
```bash
curl -X POST http://localhost:8000/api/voice_command \
  -H "Content-Type: application/json" \
  -d '{"command": "open todo list", "confidence": 0.95}'
```

## Project Structure

```
src/
├── backend/                    # FastAPI backend controller
│   ├── agentlee_controller.py  # Main API server with diagnostics
│   ├── requirements.txt        # Python dependencies
│   ├── start_backend.ps1       # Windows startup script
│   └── start_backend.sh        # Unix startup script
├── electron/                   # Electron desktop app wrapper
│   ├── main.js                 # Electron main process
│   ├── preload.js              # Secure IPC bridge
│   └── package.json            # Electron configuration
├── frontend/                   # Frontend assets for Electron
│   └── index.html              # Agent OS Launcher (Electron version)
├── js/                         # JavaScript modules
│   ├── api-client.js           # API communication layer
│   └── system-monitor.js       # Real-time system monitoring
├── .github/workflows/          # GitHub Actions for releases
│   └── release.yml             # Automated build and release
├── index.html                  # Main Agent OS Launcher
├── 72kh0rx6ss.html            # Dynamic To-Do List
├── ikuoqw85ur.html            # Agent Center
├── aqylvu95i5.html            # Database Manager
├── jfy4eqk6tq.html            # LLM Brain Center
├── s3enpoc0b9.html            # Workers Center
├── 30bkve4wz4.html            # Agent Card Demo
└── README.md                   # This file
```

## Building for Distribution

```bash
cd electron
npm run build-win   # Windows
npm run build-mac   # macOS
npm run build-linux # Linux
```

## System Requirements

* Python 3.8 or higher (for backend)
* Node.js 14 or higher (for Electron)
* Modern browser with WebGL support
* 4GB RAM minimum, 8GB recommended
* Network access for API communication

## Real-time Features

The system provides real-time monitoring through:

* **System Health Monitoring**: CPU, memory, disk usage tracking
* **Module Status**: Live status of all Agent Lee components
* **Task Queue Management**: Real-time task assignment and tracking
* **Voice Command Processing**: Natural language command interpretation
* **API Fallbacks**: Graceful degradation when backend is unavailable

## Voice Commands

Supported voice commands include:

* "Open todo list" - Opens the Dynamic To-Do List dashboard
* "Open agent center" - Opens the Agent Center dashboard
* "System status" - Reports current system health
* "Speak test" - Tests text-to-speech functionality

## License

MIT

## Download

Get the latest release for your platform:

* [Windows .exe](#)
* [macOS .dmg](#)
* [Linux .AppImage](#)