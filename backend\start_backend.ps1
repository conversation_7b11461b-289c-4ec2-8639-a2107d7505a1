# PowerShell script to start <PERSON>'s backend controller
Write-Output "Starting Agent <PERSON> Backend Controller..."

# Check if Python is installed
try {
    $pythonVersion = python --version
    Write-Output "Using $pythonVersion"
} catch {
    Write-Error "Python not found. Please install Python 3.8 or higher."
    exit 1
}

# Create and activate virtual environment if it doesn't exist
if (-not (Test-Path -Path "venv")) {
    Write-Output "Creating virtual environment..."
    python -m venv venv
}

# Activate virtual environment
Write-Output "Activating virtual environment..."
if ($PSVersionTable.PSVersion.Major -ge 6) {
    & ./venv/Scripts/Activate.ps1
} else {
    & ./venv/Scripts/Activate
}

# Install requirements
Write-Output "Installing requirements..."
pip install -r requirements.txt

# Start the backend server
Write-Output "Starting backend server with diagnostics enabled..."
uvicorn agentlee_controller:app --host 127.0.0.1 --port 8000 --reload