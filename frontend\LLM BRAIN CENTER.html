<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LLM Brain Center</title>
  <!-- Three.js for 3D brain visualization -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <!-- Three.js OrbitControls for proper 3D interaction - proper path -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/controls/OrbitControls.js"></script>
  <!-- Chart.js for better analytics visualizations -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      --dark-blue: #0a1525;
      --mid-blue: #112240;
      --light-blue: #1e3a5f;
      --accent-blue: #00b4d8;
      --glow-blue: #48cae4;
      --text-color: #e0f7fa;
      --success-color: #4caf50;
      --warning-color: #ff9800;
      --danger-color: #f44336;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    body {
      background-color: var(--dark-blue);
      color: var(--text-color);
      height: 100vh;
      overflow: hidden;
    }
    
    .container {
      display: grid;
      grid-template-columns: 300px 1fr 300px;
      grid-template-rows: auto 1fr;
      height: 100vh;
    }
    
    header {
      grid-column: 1 / 4;
      background-color: var(--mid-blue);
      padding: 15px 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--accent-blue);
      box-shadow: 0 2px 10px rgba(0, 180, 216, 0.2);
    }
    
    .logo {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .logo h1 {
      font-size: 1.5rem;
      letter-spacing: 1px;
      background: linear-gradient(to right, var(--text-color), var(--accent-blue));
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .time-date {
      text-align: right;
      font-size: 0.9rem;
      opacity: 0.8;
    }
    
    .left-panel {
      background-color: var(--mid-blue);
      padding: 20px;
      border-right: 1px solid rgba(0, 180, 216, 0.3);
      display: flex;
      flex-direction: column;
      gap: 20px;
      overflow-y: auto;
    }
    
    .panel-section {
      background-color: var(--light-blue);
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      border-bottom: 1px solid rgba(0, 180, 216, 0.3);
      padding-bottom: 8px;
    }
    
    .panel-header h3 {
      font-size: 1rem;
      color: var(--accent-blue);
    }
    
    .panel-content {
      font-size: 0.85rem;
    }
    
    .button {
      background-color: var(--accent-blue);
      color: var(--dark-blue);
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 10px;
      width: 100%;
    }
    
    .button:hover {
      background-color: var(--glow-blue);
      box-shadow: 0 0 10px var(--glow-blue);
    }
    
    .activity-log {
      height: 300px;
      overflow-y: auto;
    }
    
    .log-entry {
      padding: 8px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      gap: 10px;
    }
    
    .log-time {
      color: var(--accent-blue);
      font-size: 0.75rem;
      white-space: nowrap;
    }
    
    .log-message {
      font-size: 0.85rem;
      line-height: 1.4;
    }
    
    .main-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow: hidden;
    }
    
    #brain-canvas {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }
    
    .brain-labels {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 2;
      pointer-events: none;
    }
    
    .brain-label {
      position: absolute;
      background-color: rgba(0, 180, 216, 0.2);
      border: 1px solid var(--accent-blue);
      border-radius: 4px;
      padding: 5px 10px;
      font-size: 0.75rem;
      box-shadow: 0 0 10px var(--accent-blue);
    }
    
    .label-notepad {
      top: 20%;
      left: 30%;
    }
    
    .label-memory {
      top: 30%;
      right: 25%;
    }
    
    .label-vector {
      bottom: 30%;
      left: 25%;
    }
    
    .label-task {
      bottom: 20%;
      right: 30%;
    }
    
    .right-panel {
      background-color: var(--mid-blue);
      padding: 20px;
      border-left: 1px solid rgba(0, 180, 216, 0.3);
      display: flex;
      flex-direction: column;
      gap: 20px;
      overflow-y: auto;
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 5px;
    }
    
    .indicator-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: var(--success-color);
      box-shadow: 0 0 5px var(--success-color);
    }
    
    .chart-container {
      width: 100%;
      height: 120px;
      margin-top: 5px;
      border-radius: 4px;
      overflow: hidden;
      background-color: rgba(0, 0, 0, 0.1);
    }
    
    .metric-container {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(0, 180, 216, 0.1);
    }
    
    .metric-header {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }
    
    .metric-value {
      margin-left: 5px;
      font-weight: bold;
      color: var(--accent-blue);
    }
    
    .metric-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-left: auto;
      background-color: var(--accent-blue);
    }
    
    .log-system {
      border-left: 3px solid var(--accent-blue);
    }
    
    .log-info {
      border-left: 3px solid var(--accent-blue);
    }
    
    .log-success {
      border-left: 3px solid var(--success-color);
    }
    
    .log-warning {
      border-left: 3px solid var(--warning-color);
    }
    
    .log-error {
      border-left: 3px solid var(--danger-color);
    }
    
    .log-details {
      margin-top: 5px;
      margin-left: 15px;
    }
    
    .log-details-toggle {
      background: none;
      border: none;
      color: var(--accent-blue);
      cursor: pointer;
      font-size: 12px;
      padding: 0;
      margin-right: 5px;
    }
    
    .log-details-content {
      font-family: monospace;
      font-size: 11px;
      margin-top: 5px;
      padding: 5px;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
      max-height: 150px;
      overflow: auto;
      white-space: pre-wrap;
    }
    
    .dropdown {
      background-color: var(--light-blue);
      border: 1px solid var(--accent-blue);
      border-radius: 4px;
      padding: 8px;
      width: 100%;
      color: var(--text-color);
      cursor: pointer;
    }
    
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      margin-top: 10px;
    }
    
    .tools-container {
      max-height: 250px;
      overflow-y: auto;
      margin-top: 10px;
    }
    
    .tool-category {
      margin-bottom: 15px;
    }
    
    .category-header {
      color: var(--accent-blue);
      font-weight: bold;
      margin-bottom: 8px;
      padding: 5px 8px;
      background-color: rgba(0, 180, 216, 0.1);
      border-radius: 4px;
      border-left: 3px solid var(--accent-blue);
    }
    
    .tool-item {
      background-color: var(--light-blue);
      border: 1px solid rgba(0, 180, 216, 0.3);
      border-radius: 4px;
      padding: 6px 8px;
      margin-bottom: 4px;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 0.8rem;
      font-family: monospace;
      position: relative;
      overflow: hidden;
    }
    
    .tool-item:hover {
      background-color: var(--accent-blue);
      color: var(--dark-blue);
      box-shadow: 0 0 8px var(--accent-blue);
      transform: translateX(2px);
    }
    
    .tool-item.active {
      background-color: rgba(0, 180, 216, 0.3);
      border-color: var(--accent-blue);
      box-shadow: 0 0 5px var(--accent-blue);
    }
    
    .tool-item::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 2px;
      background-color: var(--success-color);
      transform: scaleY(0);
      transition: transform 0.2s;
    }
    
    .tool-item.active::before {
      transform: scaleY(1);
    }
    
    /* Workflow visualization styles */
    #workflow-visualization {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    
    .workflow-step {
      display: flex;
      align-items: center;
      padding: 8px;
      background-color: var(--mid-blue);
      border: 1px solid rgba(0, 180, 216, 0.3);
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      position: relative;
    }
    
    .workflow-step:hover {
      border-color: var(--accent-blue);
      box-shadow: 0 0 8px rgba(0, 180, 216, 0.3);
    }
    
    .workflow-step.active {
      border-color: var(--accent-blue);
      background-color: rgba(0, 180, 216, 0.2);
      box-shadow: 0 0 10px rgba(0, 180, 216, 0.4);
    }
    
    .workflow-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: var(--accent-blue);
      color: var(--dark-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.8rem;
      margin-right: 10px;
      flex-shrink: 0;
    }
    
    .workflow-desc {
      font-size: 0.85rem;
      flex-grow: 1;
    }
    
    .workflow-connector {
      width: 2px;
      height: 10px;
      background-color: var(--accent-blue);
      margin: 0 auto;
      opacity: 0.5;
    }
    
    .workflow-detail-header {
      font-weight: bold;
      color: var(--accent-blue);
      margin-bottom: 8px;
      padding-bottom: 5px;
      border-bottom: 1px solid rgba(0, 180, 216, 0.3);
    }
    
    .workflow-detail-content {
      line-height: 1.4;
    }
    
    .agent-map {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 5px;
    }
    
    .agent-node {
      width: 100%;
      aspect-ratio: 1;
      background-color: var(--light-blue);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.7rem;
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }
    
    .agent-node.active {
      border: 1px solid var(--accent-blue);
      box-shadow: 0 0 10px var(--accent-blue);
    }
    
    .agent-node.active::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, var(--accent-blue) 0%, transparent 70%);
      opacity: 0.3;
    }
    
    .todo-item {
      background-color: var(--light-blue);
      border-radius: 4px;
      padding: 8px;
      margin-bottom: 8px;
      border-left: 3px solid var(--accent-blue);
    }
    
    .todo-title {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .todo-desc {
      font-size: 0.8rem;
      opacity: 0.8;
    }
    
    .memory-snapshot {
      display: flex;
      justify-content: space-between;
      background-color: var(--light-blue);
      border-radius: 4px;
      padding: 8px;
      margin-bottom: 5px;
      font-size: 0.8rem;
    }
    
    .memory-snapshot-time {
      color: var(--accent-blue);
    }
    
    .glow-effect {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: radial-gradient(circle at center, var(--accent-blue) 0%, transparent 70%);
      opacity: 0.1;
      z-index: 0;
      animation: pulse 5s infinite alternate;
    }
    
    @keyframes pulse {
      0% {
        opacity: 0.1;
      }
      100% {
        opacity: 0.3;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <div class="logo">
        <div style="font-size: 24px; color: var(--accent-blue);">🧠</div>
        <h1>LLM BRAIN CENTER</h1>
      </div>
      <div class="time-date">
        <div id="current-time">14:32:15</div>
        <div id="current-date">2023-05-16</div>
      </div>
    </header>
    
    <div class="left-panel">
      <div>
        <button class="button">View Agents</button>
        <button class="button">View Logs</button>
      </div>
      
      <div class="panel-section">
        <div class="panel-header">
          <h3>Activity Log</h3>
          <span style="font-size: 0.8rem;">Live</span>
        </div>
        <div class="panel-content activity-log" id="activity-log">
          <div class="log-entry system-init">
            <div class="log-time" id="init-time"></div>
            <div class="log-message">LLM Brain Center initialized and ready</div>
          </div>
        </div>
      </div>
      
      <div class="panel-section">
        <div class="panel-header">
          <h3>ToDo Pipeline</h3>
          <span style="font-size: 0.8rem;">3 Items</span>
        </div>
        <div class="panel-content">
          <div class="todo-item">
            <div class="todo-title">Vectorize recent notes</div>
            <div class="todo-desc">Process notes from 14:20-14:30 timeframe</div>
          </div>
          <div class="todo-item">
            <div class="todo-title">Repair memory leak</div>
            <div class="todo-desc">Address 3% leakage in sector #1138</div>
          </div>
          <div class="todo-item">
            <div class="todo-title">Update agent mappings</div>
            <div class="todo-desc">Reassign tasks based on agent availability</div>
          </div>
        </div>
      </div>
      
      <div class="panel-section">
        <div class="panel-header">
          <h3>Hourly Memory Sync</h3>
          <span style="font-size: 0.8rem;">Last: 14:00</span>
        </div>
        <div class="panel-content">
          <div class="memory-snapshot">
            <span>Memory Snapshot #4382</span>
            <span class="memory-snapshot-time">14:00:00</span>
          </div>
          <div class="memory-snapshot">
            <span>Memory Snapshot #4381</span>
            <span class="memory-snapshot-time">13:00:00</span>
          </div>
          <div class="memory-snapshot">
            <span>Memory Snapshot #4380</span>
            <span class="memory-snapshot-time">12:00:00</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="main-content">
      <div class="glow-effect"></div>
      
      <!-- 3D Brain Canvas -->
      <canvas id="brain-canvas"></canvas>
      
      <div class="brain-labels">
        <div class="brain-label label-notepad">NOTEPAD</div>
        <div class="brain-label label-memory">MEMORY LEAKAGE</div>
        <div class="brain-label label-vector">VECTORIZE & MAP</div>
        <div class="brain-label label-task">TASK DISTRIBUTION & REPAIR</div>
      </div>
    </div>
    
    <div class="right-panel">
      <div class="panel-section">
        <div class="panel-header">
          <h3>LLM Identity Card</h3>
          <div class="status-indicator">
            <div class="indicator-dot"></div>
            <span style="font-size: 0.8rem;">Connected</span>
          </div>
        </div>
        <div class="panel-content">
          <div style="margin-bottom: 10px;">
            <strong>Connected LM:</strong> Gennected L^2.5 Pro
          </div>
          <div style="margin-bottom: 10px;">
            <strong>Active Runtime:</strong> <span id="active-runtime">14h 32m 15s</span>
          </div>
          <div>
            <strong>Available Memory:</strong> <span id="available-memory">97.6%</span>
          </div>
        </div>
      </div>
      
      <div class="panel-section">
        <div class="panel-header">
          <h3>Tools Library</h3>
          <span style="font-size: 0.8rem;" id="tools-count">450+ Tools</span>
        </div>
        <div class="panel-content">
          <input type="text" id="tool-search" placeholder="Search tools..." style="width: 100%; padding: 8px; margin-bottom: 10px; background: var(--light-blue); border: 1px solid var(--accent-blue); border-radius: 4px; color: var(--text-color);">
          
          <select class="dropdown" id="tool-category-select">
            <option value="">Select Tool Category</option>
            <option value="cognitive">Core Cognitive & Orchestration</option>
            <option value="web">Web & Network Operations</option>
            <option value="code">Code & Package Management</option>
            <option value="browser">Browser & DOM Operations</option>
            <option value="data">Data Processing & Management</option>
            <option value="security">Security & Compliance</option>
            <option value="performance">Performance & Monitoring</option>
            <option value="api">Specialized API/Resource Interaction</option>
            <option value="utility">Utility & Basic Actions</option>
          </select>
          
          <div class="tools-container" id="tools-container">
            <div class="tool-category" data-category="cognitive">
              <div class="category-header">🧠 Core Cognitive</div>
              <div class="tool-item" data-signature="plan_and_decompose_task(goal: string, constraints: object)" data-purpose="Breaks down a complex goal into a sequence of actionable sub-tasks and tool invocations">plan_and_decompose_task</div>
              <div class="tool-item" data-signature="semantic_router(query: string, available_agents: list)" data-purpose="Routes queries to the most appropriate specialized sub-agent or tool based on semantic understanding">semantic_router</div>
              <div class="tool-item" data-signature="summon_agent(agent_id: string, task_details: object)" data-purpose="Activates or delegates a specific task to another specialized agent">summon_agent</div>
              <div class="tool-item" data-signature="meta_reason(current_state: object, goal: string, history: list)" data-purpose="Performs self-reflection on problem-solving progress and suggests alternative strategies">meta_reason</div>
              <div class="tool-item" data-signature="counterfactual_simulate(decision_point: object, alternative_actions: list)" data-purpose="Runs parallel 'what-if' simulations for different actions to predict outcomes">counterfactual_simulate</div>
            </div>
            
            <div class="tool-category" data-category="web">
              <div class="category-header">🌐 Web & Network</div>
              <div class="tool-item" data-signature="smart_fetch(url: string, method: string, headers?: object, body?: any)" data-purpose="Advanced HTTP client with auto-retry, exponential backoff, and content type handling">smart_fetch</div>
              <div class="tool-item" data-signature="websocket_connect(url: string, protocols?: list)" data-purpose="Establishes WebSocket connection for real-time, bidirectional communication">websocket_connect</div>
              <div class="tool-item" data-signature="publish_mqtt_message(broker_url: string, topic: string, payload: any)" data-purpose="Publishes a message to an MQTT topic for IoT or distributed agent communication">publish_mqtt_message</div>
              <div class="tool-item" data-signature="cors_handler_generate(allowed_origins: list, allowed_methods: list)" data-purpose="Generates CORS preflight responses for cross-origin resource sharing">cors_handler_generate</div>
              <div class="tool-item" data-signature="rpc_optimizer(api_endpoint: string, traffic_data: object)" data-purpose="Monitors API calls and suggests switching to more efficient protocols like gRPC or WebSockets">rpc_optimizer</div>
            </div>
            
            <div class="tool-category" data-category="code">
              <div class="category-header">💻 Code Management</div>
              <div class="tool-item" data-signature="execute_code_sandbox(language: string, code: string, context?: object)" data-purpose="Executes arbitrary code in a sandboxed environment (JS, Python via Pyodide, WASM)">execute_code_sandbox</div>
              <div class="tool-item" data-signature="dependency_resolver(manifest_file_content: string, ecosystem: string)" data-purpose="Resolves package dependencies, checks for version conflicts and vulnerabilities">dependency_resolver</div>
              <div class="tool-item" data-signature="bundle_code(entry_point: string, source_code_map: object, target_format: string)" data-purpose="Bundles multiple code modules with tree-shaking and minification">bundle_code</div>
              <div class="tool-item" data-signature="transpile_code(source_code: string, source_lang: string, target_lang: string)" data-purpose="Transpiles code from one language/version to another (e.g., TypeScript to JS)">transpile_code</div>
              <div class="tool-item" data-signature="typescript_evolver(javascript_code: string, usage_context?: list)" data-purpose="Infers TypeScript types from untyped JavaScript code through static analysis">typescript_evolver</div>
            </div>
            
            <div class="tool-category" data-category="browser">
              <div class="category-header">🖥️ Browser & DOM</div>
              <div class="tool-item" data-signature="dom_element_query(selector: string, parent_element?: Element)" data-purpose="Queries the DOM for elements matching a CSS selector">dom_element_query</div>
              <div class="tool-item" data-signature="shadow_dom_interact(host_element_selector: string, inner_selector: string)" data-purpose="Interacts with elements inside a Shadow DOM, piercing shadow boundaries">shadow_dom_interact</div>
              <div class="tool-item" data-signature="accessibility_check_and_fix(html_content_or_url: string, ruleset?: string)" data-purpose="Analyzes HTML for accessibility violations and auto-corrects common issues">accessibility_check</div>
              <div class="tool-item" data-signature="render_headless_browser(url: string, actions?: list, viewport?: object)" data-purpose="Loads a URL in a headless browser instance and performs actions on the page">render_headless_browser</div>
              <div class="tool-item" data-signature="service_worker_control(action: string, script_url?: string, scope?: string)" data-purpose="Manages Service Worker registration, updates, and communication">service_worker_control</div>
            </div>
            
            <div class="tool-category" data-category="data">
              <div class="category-header">📊 Data Processing</div>
              <div class="tool-item" data-signature="parse_data(data: string, format: string, schema?: object)" data-purpose="Parses structured data (JSON, XML, CSV, YAML) into a native object with schema validation">parse_data</div>
              <div class="tool-item" data-signature="validate_data(data: any, schema: object, validator_engine?: string)" data-purpose="Validates data against a provided schema (JSON Schema, Zod, Yup)">validate_data</div>
              <div class="tool-item" data-signature="schema_breeder(sample_data: list_or_object, inference_rules?: object)" data-purpose="Auto-generates data validation schemas by inferring structure from sample data">schema_breeder</div>
              <div class="tool-item" data-signature="vector_embed_text(text: string, model_id?: string)" data-purpose="Converts text into a dense vector representation using on-device models">vector_embed_text</div>
              <div class="tool-item" data-signature="privacy_sentinel_redact(text_or_data: any, pii_types: list)" data-purpose="Detects and redacts PII from text or structured data for compliance">privacy_sentinel_redact</div>
            </div>
            
            <div class="tool-category" data-category="security">
              <div class="category-header">🔒 Security</div>
              <div class="tool-item" data-signature="encrypt_data_aes_gcm(plaintext: string_or_buffer, key: string_or_buffer)" data-purpose="Encrypts data using AES-GCM for secure storage or transmission">encrypt_data_aes_gcm</div>
              <div class="tool-item" data-signature="generate_content_security_policy(observed_sources: object)" data-purpose="Generates a Content Security Policy based on observed resource sources">generate_csp</div>
              <div class="tool-item" data-signature="compliance_audit_check(data_or_process: any, regulation_id: string)" data-purpose="Validates data or processes against regulatory requirements (GDPR, HIPAA, CCPA)">compliance_audit_check</div>
              <div class="tool-item" data-signature="authenticate_user_oauth(provider_id: string, scopes: list)" data-purpose="Initiates OAuth 2.0 flow with a provider to authenticate users or authorize API access">authenticate_user_oauth</div>
            </div>
            
            <div class="tool-category" data-category="performance">
              <div class="category-header">⚡ Performance</div>
              <div class="tool-item" data-signature="log_event(level: string, message: string, context_data?: object)" data-purpose="Records structured log events with severity levels and context">log_event</div>
              <div class="tool-item" data-signature="performance_audit_lighthouse(url_or_html: string, audits?: list)" data-purpose="Runs performance, accessibility, and SEO audits on URLs or HTML content">performance_audit</div>
              <div class="tool-item" data-signature="monitor_resource_changes(url_or_api: string, check_interval: number)" data-purpose="Monitors web resources or API endpoints for changes with callback triggers">monitor_resource_changes</div>
              <div class="tool-item" data-signature="perf_profile_code_block(code_function: function, iterations?: number)" data-purpose="Profiles execution time and memory usage of code functions">perf_profile_code_block</div>
            </div>
            
            <div class="tool-category" data-category="api">
              <div class="category-header">🔗 API Interaction</div>
              <div class="tool-item" data-signature="mdn_query(search_term: string, section?: string)" data-purpose="Queries Mozilla Developer Network for web technology documentation">mdn_query</div>
              <div class="tool-item" data-signature="webgl_shader_compile_and_run(shader_code: string, vertex_data: object)" data-purpose="Compiles and runs WebGL shaders for GPU-accelerated computation">webgl_shader_compile</div>
              <div class="tool-item" data-signature="calendar_schedule_event(title: string, start_time: datetime, end_time: datetime)" data-purpose="Interacts with calendar APIs to schedule new events">calendar_schedule_event</div>
              <div class="tool-item" data-signature="llm_response_compress(llm_output_text: string, target_token_count?: number)" data-purpose="Compresses verbose LLM text output to a more concise version with high fidelity">llm_response_compress</div>
            </div>
            
            <div class="tool-category" data-category="utility">
              <div class="category-header">🛠️ Utilities</div>
              <div class="tool-item" data-signature="generate_uuid()" data-purpose="Generates a universally unique identifier (UUID)">generate_uuid</div>
              <div class="tool-item" data-signature="text_summarize(text: string, desired_length_ratio?: number)" data-purpose="Condenses long text into shorter summaries while preserving key information">text_summarize</div>
              <div class="tool-item" data-signature="text_translate(text: string, target_language_code: string)" data-purpose="Translates text between languages with high accuracy">text_translate</div>
              <div class="tool-item" data-signature="extract_entities_from_text(text: string, entity_types?: list)" data-purpose="Identifies named entities (people, organizations, locations) from text">extract_entities_from_text</div>
            </div>
          </div>
        </div>
        
        <!-- Tool Details Panel -->
        <div id="tool-details-panel" style="display: none; margin-top: 15px; padding: 10px; background-color: var(--light-blue); border-radius: 8px; border: 1px solid var(--accent-blue);">
          <div class="panel-header" style="margin-bottom: 10px; padding-bottom: 8px; border-bottom: 1px solid rgba(0, 180, 216, 0.3);">
            <h3 id="tool-detail-name" style="color: var(--accent-blue); margin: 0; font-family: monospace;"></h3>
          </div>
          <div class="panel-content">
            <div style="margin-bottom: 8px;">
              <strong>Signature:</strong>
              <pre id="tool-detail-signature" style="margin: 5px 0; padding: 5px; background-color: rgba(0, 0, 0, 0.2); border-radius: 4px; font-size: 0.8rem; overflow-x: auto;"></pre>
            </div>
            <div style="margin-bottom: 8px;">
              <strong>Purpose:</strong>
              <p id="tool-detail-purpose" style="margin: 5px 0; font-size: 0.85rem;"></p>
            </div>
            <div style="margin-bottom: 8px;">
              <strong>Workflow Methods:</strong>
              <ul id="tool-detail-workflows" style="margin: 5px 0; font-size: 0.85rem; padding-left: 20px;"></ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="panel-section">
        <div class="panel-header">
          <h3>Live Status Panel</h3>
          <span style="font-size: 0.8rem;">All Systems</span>
        </div>
        <div class="panel-content">
          <div class="metric-container">
            <div class="metric-header">
              <strong>Workflow Efficiency:</strong> <span id="workflow-status" class="metric-value">--.--%</span>
              <div class="metric-indicator"></div>
            </div>
            <div class="chart-container">
              <canvas id="workflow-chart"></canvas>
            </div>
          </div>
          
          <div class="metric-container">
            <div class="metric-header">
              <strong>Memory Leakage:</strong> <span id="memory-leakage" class="metric-value">--.--%</span>
              <div class="metric-indicator"></div>
            </div>
            <div class="chart-container">
              <canvas id="memory-chart"></canvas>
            </div>
          </div>
          
          <div class="metric-container">
            <div class="metric-header">
              <strong>Data Leakage:</strong> <span id="data-leakage" class="metric-value">--.--%</span>
              <div class="metric-indicator"></div>
            </div>
            <div class="chart-container">
              <canvas id="data-chart"></canvas>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Workflow Visualization -->
      <div class="panel-section">
        <div class="panel-header">
          <h3>Workflow Protocols</h3>
          <span style="font-size: 0.8rem;">System Methods</span>
        </div>
        <div class="panel-content">
          <div id="workflow-visualization">
            <div class="workflow-step active" data-workflow-id="1">
              <div class="workflow-number">1</div>
              <div class="workflow-desc">Task Intake & Planning</div>
            </div>
            <div class="workflow-connector"></div>
            <div class="workflow-step" data-workflow-id="2">
              <div class="workflow-number">2</div>
              <div class="workflow-desc">Tool Invocation & Data</div>
            </div>
            <div class="workflow-connector"></div>
            <div class="workflow-step" data-workflow-id="3">
              <div class="workflow-number">3</div>
              <div class="workflow-desc">State Management</div>
            </div>
            <div class="workflow-connector"></div>
            <div class="workflow-step" data-workflow-id="4">
              <div class="workflow-number">4</div>
              <div class="workflow-desc">Error Handling</div>
            </div>
            <div class="workflow-connector"></div>
            <div class="workflow-step" data-workflow-id="5">
              <div class="workflow-number">5</div>
              <div class="workflow-desc">Monitoring & Optimization</div>
            </div>
            <div class="workflow-connector"></div>
            <div class="workflow-step" data-workflow-id="6">
              <div class="workflow-number">6</div>
              <div class="workflow-desc">Response Synthesis</div>
            </div>
          </div>
          <div id="workflow-detail" style="margin-top: 15px; font-size: 0.85rem; display: none;">
            <div class="workflow-detail-header"></div>
            <div class="workflow-detail-content"></div>
          </div>
        </div>
      </div>
      
      <!-- Agent Mapping Panel -->
      <div class="panel-section">
        <div class="panel-header">
          <h3>Agent Mapping Panel</h3>
          <span style="font-size: 0.8rem;">125 Modules</span>
        </div>
        <div class="panel-content">
          <div class="agent-map">
            <!-- 5x5 grid of agent nodes -->
            <div class="agent-node">A1</div>
            <div class="agent-node active">A2</div>
            <div class="agent-node">A3</div>
            <div class="agent-node">A4</div>
            <div class="agent-node">A5</div>
            <div class="agent-node">B1</div>
            <div class="agent-node">B2</div>
            <div class="agent-node active">B3</div>
            <div class="agent-node">B4</div>
            <div class="agent-node">B5</div>
            <div class="agent-node">C1</div>
            <div class="agent-node">C2</div>
            <div class="agent-node">C3</div>
            <div class="agent-node active">C4</div>
            <div class="agent-node">C5</div>
            <div class="agent-node">D1</div>
            <div class="agent-node active">D2</div>
            <div class="agent-node">D3</div>
            <div class="agent-node">D4</div>
            <div class="agent-node">D5</div>
            <div class="agent-node">E1</div>
            <div class="agent-node">E2</div>
            <div class="agent-node active">E3</div>
            <div class="agent-node">E4</div>
            <div class="agent-node">E5</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Global state
    window.selectedToolCategory = '';
    window.activeTool = null;
    
    // Update time and date
    function updateDateTime() {
      const now = new Date();
      
      // Format time as HH:MM:SS
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const timeString = `${hours}:${minutes}:${seconds}`;
      
      // Format date as YYYY-MM-DD
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const dateString = `${year}-${month}-${day}`;
      
      document.getElementById('current-time').textContent = timeString;
      document.getElementById('current-date').textContent = dateString;
      
      // Update runtime
      const startTime = new Date(now);
      startTime.setHours(startTime.getHours() - 14);
      startTime.setMinutes(startTime.getMinutes() - 32);
      startTime.setSeconds(startTime.getSeconds() - 15);
      
      const diffMs = now - startTime;
      const diffHrs = Math.floor(diffMs / 3600000);
      const diffMins = Math.floor((diffMs % 3600000) / 60000);
      const diffSecs = Math.floor((diffMs % 60000) / 1000);
      
      document.getElementById('active-runtime').textContent = `${diffHrs}h ${diffMins}m ${diffSecs}s`;
    }
    
    // Update time every second
    setInterval(updateDateTime, 1000);
    updateDateTime(); // Initial call
    
    // Production-ready activity logging system
    function addLogEntry(message, level = 'info', details = null) {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
      const timeString = `${hours}:${minutes}:${seconds}.${milliseconds}`;
      
      // Create entry with appropriate class based on level
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry log-${level}`;
      
      let entryHTML = `
        <div class="log-time">${timeString}</div>
        <div class="log-message">${message}</div>
      `;
      
      // Add expandable details if provided
      if (details) {
        entryHTML += `
          <div class="log-details">
            <button class="log-details-toggle">▾</button>
            <pre class="log-details-content">${JSON.stringify(details, null, 2)}</pre>
          </div>
        `;
      }
      
      logEntry.innerHTML = entryHTML;
      
      const activityLog = document.getElementById('activity-log');
      activityLog.insertBefore(logEntry, activityLog.firstChild);
      
      // Add details toggle functionality
      if (details) {
        const toggleBtn = logEntry.querySelector('.log-details-toggle');
        const detailsContent = logEntry.querySelector('.log-details-content');
        detailsContent.style.display = 'none';
        
        toggleBtn.addEventListener('click', function() {
          const isVisible = detailsContent.style.display !== 'none';
          detailsContent.style.display = isVisible ? 'none' : 'block';
          toggleBtn.textContent = isVisible ? '▾' : '▴';
        });
      }
      
      // Remove oldest entry if there are too many
      const entries = activityLog.querySelectorAll('.log-entry');
      if (entries.length > 50) { // Increased capacity for production
        activityLog.removeChild(entries[entries.length - 1]);
      }
      
      // For production, also log to console for debugging
      console[level](message, details || '');
      
      // Return entry for potential further manipulation
      return logEntry;
    }
    
    // Log system events at regular intervals based on system activity
    function initSystemLogging() {
      // Set initialization time
      document.getElementById('init-time').textContent = new Date().toLocaleTimeString();
      
      // Log system startup
      addLogEntry('System initialization complete', 'info', {
        version: '1.0.0',
        environment: 'production',
        modules: ['brain', 'tools', 'workflow', 'analytics']
      });
      
      // Log health checks regularly
      setInterval(() => {
        const memoryUsage = Math.round(90 + Math.random() * 9);
        const status = memoryUsage > 95 ? 'warning' : 'info';
        
        addLogEntry(`System health check: ${memoryUsage}% memory available`, status, {
          timestamp: Date.now(),
          checks: {
            memory: memoryUsage,
            cpu: Math.round(10 + Math.random() * 20),
            network: 'stable',
            storage: '97% available'
          }
        });
      }, 60000); // Every minute in production
    }
    
    // Production agent activity management system
    let agentRegistry = {};
    
    // Initialize agent system with proper metadata
    function initializeAgentSystem() {
      const agents = document.querySelectorAll('.agent-node');
      
      // Build proper agent registry with metadata
      agents.forEach(agent => {
        const id = agent.textContent;
        agentRegistry[id] = {
          id: id,
          status: 'idle',
          type: id.charAt(0),
          priority: parseInt(id.charAt(1)),
          lastActive: null,
          tasks: [],
          utilization: 0,
          element: agent
        };
      });
      
      addLogEntry('Agent registry initialized', 'info', {
        count: Object.keys(agentRegistry).length,
        types: [...new Set(Object.values(agentRegistry).map(a => a.type))],
        statuses: { idle: Object.keys(agentRegistry).length }
      });
    }
    
    // Update agent with proper state management
    function updateAgentActivity() {
      const agentIds = Object.keys(agentRegistry);
      if (agentIds.length === 0) return; // Safety check
      
      // Reset all agents to idle state
      agentIds.forEach(id => {
        const agent = agentRegistry[id];
        if (!agent || !agent.element) return; // Safety check
        
        agent.element.classList.remove('active');
        
        // Only change state if previously active
        if (agent.status === 'active') {
          agent.status = 'idle';
          agent.lastActive = new Date();
          
          // Log completion of work
          if (agent.tasks.length > 0) {
            const task = agent.tasks.pop();
            addLogEntry(`Agent ${id} completed task: ${task}`, 'info');
          }
        }
      });
      
      // Activate agents based on workload simulation
      const workTypes = ['query', 'compute', 'analyze', 'transform', 'route'];
      const numActiveAgents = 3 + Math.floor(Math.random() * 4);
      
      for (let i = 0; i < numActiveAgents; i++) {
        // Select agent with proper allocation strategy - prioritize idle high-priority agents
        const availableAgents = agentIds.filter(id => agentRegistry[id].status === 'idle');
        
        if (availableAgents.length === 0) continue;
        
        // Sort by priority (lower number = higher priority)
        availableAgents.sort((a, b) => agentRegistry[a].priority - agentRegistry[b].priority);
        
        // Select highest priority agent with some randomness for load balancing
        const selectedIndex = Math.floor(Math.random() * Math.min(3, availableAgents.length));
        const selectedId = availableAgents[selectedIndex];
        const agent = agentRegistry[selectedId];
        
        // Assign work and update state
        const workType = workTypes[Math.floor(Math.random() * workTypes.length)];
        const taskId = Math.floor(Math.random() * 1000) + 1;
        const task = `${workType}-${taskId}`;
        
        agent.status = 'active';
        agent.tasks.push(task);
        agent.utilization += 1;
        agent.element.classList.add('active');
        
        // Log task assignment
        addLogEntry(`Task ${task} assigned to agent ${selectedId}`, 'info');
      }
      
      // Update utilization statistics (would connect to real metrics in production)
      const activeCount = Object.values(agentRegistry).filter(a => a.status === 'active').length;
      const utilizationRate = (activeCount / agentIds.length) * 100;
      
      // In production, this would update a metrics dashboard
      if (utilizationRate > 80) {
        addLogEntry(`High agent utilization: ${utilizationRate.toFixed(1)}%`, 'warning');
      }
    }
    
    // Production update interval - more frequent for demo but would be tuned in production
    const agentUpdateInterval = setInterval(updateAgentActivity, 15000);
    
    // Production memory management system with trend analysis
    let memoryReadings = []; // Store history for trend analysis
    const memoryReadingLimit = 100; // Keep last 100 readings for analysis
    
    function updateSystemMemory() {
      try {
        // In production this would call a real memory monitoring API
        const memoryStats = getSystemMemoryStats();
        
        // Update memory display
        document.getElementById('available-memory').textContent = memoryStats.available.toFixed(1) + '%';
        
        // Record reading for trend analysis
        memoryReadings.push({
          timestamp: Date.now(),
          available: memoryStats.available,
          used: memoryStats.used,
          cached: memoryStats.cached,
          swap: memoryStats.swap
        });
        
        // Maintain history limit
        if (memoryReadings.length > memoryReadingLimit) {
          memoryReadings.shift();
        }
        
        // Analyze trends for production monitoring
        analyzeMemoryTrends();
        
      } catch (error) {
        console.error('Error updating memory stats:', error);
        addLogEntry('Memory monitoring error', 'error', { error: error.message });
      }
    }
    
    // Simulate production memory stats API
    function getSystemMemoryStats() {
      // In production, this would be replaced with actual system calls
      // or a WebSocket connection to a monitoring service
      
      // Simulate realistic memory behavior with occasional spikes
      const baseAvailable = 95;
      const now = Date.now();
      
      // Create some realistic patterns - occasional dips
      const timeComponent = Math.sin(now / 10000) * 2; // Slow wave
      const randomComponent = (Math.random() * 0.5) - 0.3; // Small random noise
      const memorySpike = Math.random() > 0.98 ? -4 : 0; // Occasional spike
      
      const available = Math.max(85, Math.min(99, baseAvailable + timeComponent + randomComponent + memorySpike));
      const used = 100 - available;
      
      return {
        available: available,
        used: used,
        cached: used * 0.4,
        swap: used > 10 ? (used - 10) * 0.2 : 0
      };
    }
    
    // Analyze memory trends for proactive management
    function analyzeMemoryTrends() {
      if (memoryReadings.length < 10) return; // Need sufficient data
      
      // Calculate trend over last 10 readings
      const recent = memoryReadings.slice(-10);
      const oldestReading = recent[0].available;
      const newestReading = recent[recent.length - 1].available;
      const trend = newestReading - oldestReading;
      
      // Detect problematic memory trends
      if (trend < -5) {
        addLogEntry('Memory usage increasing rapidly', 'warning', {
          trend: trend.toFixed(2) + '%',
          timespan: ((recent[recent.length - 1].timestamp - recent[0].timestamp) / 1000).toFixed(0) + 's'
        });
      }
      
      // Detect critical thresholds
      if (newestReading < 90) {
        addLogEntry('Memory availability below 90%', 'warning');
      }
      
      if (newestReading < 87) {
        addLogEntry('Critical memory threshold reached', 'error', {
          available: newestReading.toFixed(1) + '%',
          recommendation: 'Consider garbage collection or resource reallocation'
        });
      }
    }
    
    // Production-appropriate update interval
    const memoryMonitorInterval = setInterval(updateSystemMemory, 5000);
    
    // Production-ready real-time analytics with Chart.js
    let charts = {};
    let chartData = {
      workflow: { history: [], target: 95, alert: 85 },
      memory: { history: [], target: 0, alert: 5 },
      data: { history: [], target: 0, alert: 1 }
    };
    
    function initializeAnalytics() {
      if (!window.Chart) {
        addLogEntry('Chart.js library not loaded', 'error');
        return;
      }
      
      try {
        addLogEntry('Initializing analytics system', 'system');
        
        // Register chart update hook for production resilience
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.color = "rgba(224, 247, 250, 0.7)";
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
        
        // Production-grade configuration with performance optimizations
        const commonOptions = {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'nearest',
            intersect: false
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: 'second',
                stepSize: 5,
                displayFormats: {
                  second: 'HH:mm:ss'
                }
              },
              grid: {
                display: false
              },
              ticks: {
                maxRotation: 0,
                autoSkip: true,
                maxTicksLimit: 5
              }
            },
            y: {
              grid: {
                color: 'rgba(255, 255, 255, 0.1)'
              },
              ticks: {
                precision: 1
              }
            }
          },
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(17, 34, 64, 0.9)',
              borderColor: 'rgba(0, 180, 216, 0.5)',
              borderWidth: 1,
              caretSize: 6,
              titleFont: { weight: 'bold' },
              callbacks: {
                title: function(tooltipItems) {
                  return new Date(tooltipItems[0].parsed.x).toLocaleTimeString();
                }
              }
            }
          },
          transitions: {
            active: {
              animation: {
                duration: 300
              }
            }
          }
        };
        
        // Create workflow efficiency chart - production metrics
        const workflowCtx = document.getElementById('workflow-chart').getContext('2d');
        charts.workflow = new Chart(workflowCtx, {
          type: 'line',
          data: {
            datasets: [{
              label: 'Workflow Efficiency',
              data: [],
              borderColor: 'rgba(0, 180, 216, 1)',
              backgroundColor: 'rgba(0, 180, 216, 0.2)',
              borderWidth: 2,
              fill: true,
              tension: 0.4,
              pointRadius: 0,
              pointHoverRadius: 4
            }]
          },
          options: {
            ...commonOptions,
            scales: {
              ...commonOptions.scales,
              y: {
                ...commonOptions.scales.y,
                min: 80,
                max: 100,
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              }
            }
          }
        });
        
        // Create memory monitoring chart - production metrics
        const memoryCtx = document.getElementById('memory-chart').getContext('2d');
        charts.memory = new Chart(memoryCtx, {
          type: 'line',
          data: {
            datasets: [{
              label: 'Memory Leakage',
              data: [],
              borderColor: 'rgba(255, 152, 0, 1)',
              backgroundColor: 'rgba(255, 152, 0, 0.2)',
              borderWidth: 2,
              fill: true,
              tension: 0.4,
              pointRadius: 0,
              pointHoverRadius: 4
            }]
          },
          options: {
            ...commonOptions,
            scales: {
              ...commonOptions.scales,
              y: {
                ...commonOptions.scales.y,
                min: 0,
                max: 10,
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              }
            },
            plugins: {
              ...commonOptions.plugins,
              annotation: {
                annotations: {
                  thresholdLine: {
                    type: 'line',
                    yMin: 5,
                    yMax: 5,
                    borderColor: 'rgba(255, 50, 50, 0.5)',
                    borderWidth: 1,
                    borderDash: [5, 5]
                  }
                }
              }
            }
          }
        });
        
        // Create data security chart - production metrics
        const dataCtx = document.getElementById('data-chart').getContext('2d');
        charts.data = new Chart(dataCtx, {
          type: 'line',
          data: {
            datasets: [{
              label: 'Data Leakage',
              data: [],
              borderColor: 'rgba(76, 175, 80, 1)',
              backgroundColor: 'rgba(76, 175, 80, 0.2)',
              borderWidth: 2,
              fill: true,
              tension: 0.4,
              pointRadius: 0,
              pointHoverRadius: 4
            }]
          },
          options: {
            ...commonOptions,
            scales: {
              ...commonOptions.scales,
              y: {
                ...commonOptions.scales.y,
                min: 0,
                max: 2,
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              }
            }
          }
        });
        
        // Start real-time data collection for analytics
        startAnalyticsDataCollection();
        
        addLogEntry('Analytics system initialized', 'success');
      } catch (error) {
        console.error('Error initializing analytics:', error);
        addLogEntry('Failed to initialize analytics system', 'error', { 
          error: error.message,
          component: 'Chart.js'
        });
      }
    }
    
    // Simulate production data sources with realistic patterns
    function startAnalyticsDataCollection() {
      const dataUpdateInterval = setInterval(function() {
        try {
          const now = new Date();
          
          // Production workflow efficiency simulation with realistic patterns
          let workflowEfficiency = getWorkflowEfficiency();
          chartData.workflow.history.push(workflowEfficiency);
          charts.workflow.data.datasets[0].data.push({
            x: now,
            y: workflowEfficiency
          });
          
          // Keep last 100 points for production monitoring
          if (charts.workflow.data.datasets[0].data.length > 100) {
            charts.workflow.data.datasets[0].data.shift();
            chartData.workflow.history.shift();
          }
          
          // Memory leakage monitoring with realistic patterns
          let memoryLeakage = getMemoryLeakage();
          chartData.memory.history.push(memoryLeakage);
          charts.memory.data.datasets[0].data.push({
            x: now,
            y: memoryLeakage
          });
          
          if (charts.memory.data.datasets[0].data.length > 100) {
            charts.memory.data.datasets[0].data.shift();
            chartData.memory.history.shift();
          }
          
          // Data security monitoring with realistic patterns
          let dataLeakage = getDataLeakage();
          chartData.data.history.push(dataLeakage);
          charts.data.data.datasets[0].data.push({
            x: now,
            y: dataLeakage
          });
          
          if (charts.data.data.datasets[0].data.length > 100) {
            charts.data.data.datasets[0].data.shift();
            chartData.data.history.shift();
          }
          
          // Update charts and display values
          Object.values(charts).forEach(chart => chart.update('quiet'));
          
          document.getElementById('workflow-status').textContent = workflowEfficiency.toFixed(1) + '%';
          document.getElementById('memory-leakage').textContent = memoryLeakage.toFixed(1) + '%';
          document.getElementById('data-leakage').textContent = dataLeakage.toFixed(1) + '%';
          
          // Detect and alert on concerning trends (production monitoring)
          detectAnalyticsAnomalies();
          
        } catch (error) {
          console.error('Error updating analytics data:', error);
          addLogEntry('Analytics data update failed', 'error', { error: error.message });
        }
      }, 3000);
      
      // Return interval ID for potential cleanup in production
      return dataUpdateInterval;
    }
    
    // Production-grade metrics generators with realistic patterns
    
    // Workflow efficiency - should stay high with occasional dips
    function getWorkflowEfficiency() {
      // Base performance around 94% with trends
      const now = Date.now();
      const base = 94;
      
      // Add cyclical pattern - simulates scheduled tasks
      const hourCycle = Math.sin(now / 3600000 * Math.PI * 2) * 1.5;
      
      // Add small random noise
      const noise = (Math.random() - 0.5) * 1.0;
      
      // Occasional significant dip (simulates real-world issues)
      const dip = Math.random() > 0.95 ? -5 - Math.random() * 5 : 0;
      
      // Calculate final value with reasonable bounds
      return Math.max(80, Math.min(99, base + hourCycle + noise + dip));
    }
    
    // Memory leakage - should stay low with occasional spikes
    function getMemoryLeakage() {
      // Baseline of about 1% leakage
      const base = 1.0;
      
      // Slight upward trend over time (realistic for long-running processes)
      const upwardTrend = (chartData.memory.history.length / 5000) * 0.5;
      
      // Random fluctuation
      const fluctuation = (Math.random() - 0.3) * 0.5;
      
      // Occasional spike (simulates real memory issues)
      const spike = Math.random() > 0.97 ? 3 + Math.random() * 3 : 0;
      
      // Ensure reasonable bounds
      return Math.max(0, Math.min(10, base + upwardTrend + fluctuation + spike));
    }
    
    // Data leakage - should remain near zero with rare events
    function getDataLeakage() {
      // Baseline near zero
      const base = 0.05;
      
      // Small random fluctuation
      const fluctuation = (Math.random() - 0.7) * 0.1;
      
      // Very rare but significant spike (simulates real security events)
      const securityEvent = Math.random() > 0.99 ? 1 + Math.random() * 0.5 : 0;
      
      // Ensure reasonable bounds
      return Math.max(0, Math.min(2, base + fluctuation + securityEvent));
    }
    
    // Production anomaly detection for alerting
    function detectAnalyticsAnomalies() {
      try {
        // Only analyze if we have enough data points
        if (chartData.workflow.history.length < 10) return;
        
        // Check for sustained workflow efficiency drop
        const recentWorkflow = chartData.workflow.history.slice(-5);
        const avgWorkflow = recentWorkflow.reduce((sum, val) => sum + val, 0) / recentWorkflow.length;
        
        if (avgWorkflow < 85) {
          addLogEntry(`Low workflow efficiency detected: ${avgWorkflow.toFixed(1)}%`, 'warning', {
            threshold: '85%',
            current: avgWorkflow.toFixed(1) + '%',
            samples: recentWorkflow.length
          });
        }
        
        // Check for memory leakage trend
        const recentMemory = chartData.memory.history.slice(-10);
        const oldestMemory = recentMemory[0];
        const newestMemory = recentMemory[recentMemory.length - 1];
        const memoryTrend = newestMemory - oldestMemory;
        
        if (memoryTrend > 1.5) {
          addLogEntry(`Increasing memory leakage trend detected: +${memoryTrend.toFixed(2)}%`, 'warning', {
            trend: `+${memoryTrend.toFixed(2)}%`,
            timespan: '30 seconds',
            recommendation: 'Consider garbage collection cycle'
          });
        }
        
        // Check for data leakage spike
        const lastDataLeakage = chartData.data.history[chartData.data.history.length - 1];
        if (lastDataLeakage > 0.5) {
          addLogEntry(`Elevated data leakage detected: ${lastDataLeakage.toFixed(2)}%`, 'error', {
            threshold: '0.5%',
            current: lastDataLeakage.toFixed(2) + '%',
            action: 'Security protocols engaged'
          });
        }
        
      } catch (error) {
        console.error('Error in anomaly detection:', error);
      }
    }
    
    // Call during system initialization
    document.addEventListener('DOMContentLoaded', initializeAnalytics);
    
    // Create 3D Brain Visualization with Three.js
    let scene, camera, renderer, brain, controls;
    
    function init3DBrain() {
      // Check if Three.js is loaded
      if (typeof THREE === 'undefined') {
        console.error('Three.js is not loaded!');
        return;
      }
      
      // Get canvas element and its dimensions
      const canvas = document.getElementById('brain-canvas');
      if (!canvas) {
        console.error('Brain canvas element not found!');
        return;
      }
      
      const container = canvas.parentElement;
      const containerRect = container.getBoundingClientRect();
      const width = containerRect.width;
      const height = containerRect.height;
      
      console.log(`Canvas dimensions: ${width}x${height}`);
      
      try {
        // Scene setup - keep it simple
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x0a1525);
        
        // Camera setup - better positioned to see full brain
        camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000);
        camera.position.set(0, 0, 8);
        
        // Renderer setup
        renderer = new THREE.WebGLRenderer({ 
          canvas: canvas,
          antialias: true,
          alpha: true
        });
        renderer.setSize(width, height, false); // false to avoid setting canvas style
        renderer.setClearColor(0x0a1525, 1);
        
        console.log('3D Brain renderer initialized successfully');
      } catch (e) {
        console.error('Error initializing 3D brain:', e);
      }
      
      // Simplified controls setup
      controls = null;
      
      // Add basic mouse interaction manually
      // Use global isMouseDown
      let mouseX = 0, mouseY = 0;
      
      canvas.addEventListener('mousedown', function(e) {
        isMouseDown = true;
        mouseX = e.clientX;
        mouseY = e.clientY;
      });
      
      document.addEventListener('mouseup', function() {
        isMouseDown = false;
      });
      
      canvas.addEventListener('mousemove', function(e) {
        if (!isMouseDown || !brain) return;
        
        const deltaX = e.clientX - mouseX;
        const deltaY = e.clientY - mouseY;
        
        brain.rotation.y += deltaX * 0.01;
        brain.rotation.x += deltaY * 0.01;
        
        mouseX = e.clientX;
        mouseY = e.clientY;
      });
      
      // Add wheel zoom
      canvas.addEventListener('wheel', function(e) {
        e.preventDefault();
        const scale = e.deltaY > 0 ? 1.1 : 0.9;
        camera.position.z = Math.max(3, Math.min(15, camera.position.z * scale));
      });
      
      // Add lights
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
      scene.add(ambientLight);
      
      const pointLight1 = new THREE.PointLight(0x48cae4, 1.5);
      pointLight1.position.set(5, 5, 5);
      scene.add(pointLight1);
      
      const pointLight2 = new THREE.PointLight(0x00b4d8, 1.5);
      pointLight2.position.set(-5, -5, 5);
      scene.add(pointLight2);
      
      const pointLight3 = new THREE.PointLight(0x48cae4, 1);
      pointLight3.position.set(0, 5, -5);
      scene.add(pointLight3);
      
      // Create brain group
      brain = new THREE.Group();
      scene.add(brain);
      
      // Create more detailed brain structure
      createBrainStructure();
      
      // Event listeners
      window.addEventListener('resize', onWindowResize);
      
      // Start animation
      animate();
    }
    
    function createBrainStructure() {
      try {
        // Main brain cortex - simplified for better performance
        const cortexGeometry = new THREE.SphereGeometry(3, 64, 64);
        
        // Create brain surface with less detailed patterns for stability
        const positions = cortexGeometry.attributes.position;
        for (let i = 0; i < positions.count; i++) {
          const vertex = new THREE.Vector3();
          vertex.fromBufferAttribute(positions, i);
          
          // Simplified noise pattern
          const noise = Math.sin(vertex.x * 4) * Math.cos(vertex.y * 4) * Math.sin(vertex.z * 4) * 0.15;
          vertex.multiplyScalar(1 + noise);
          
          positions.setXYZ(i, vertex.x, vertex.y, vertex.z);
        }
        cortexGeometry.attributes.position.needsUpdate = true;
        cortexGeometry.computeVertexNormals();
      
      // Translucent cyan material matching the reference image
      const cortexMaterial = new THREE.MeshPhongMaterial({
        color: 0x00bfff,
        emissive: 0x40e0d0,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.7,
        shininess: 100,
        side: THREE.DoubleSide
      });
      
      const cortex = new THREE.Mesh(cortexGeometry, cortexMaterial);
      brain.add(cortex);
      
      // Simpler inner brain for better performance
      const innerGeometry = new THREE.SphereGeometry(2.5, 32, 32);
      const innerMaterial = new THREE.MeshBasicMaterial({
        color: 0x4682b4,
        transparent: true,
        opacity: 0.4
      });
      
      const innerBrain = new THREE.Mesh(innerGeometry, innerMaterial);
      brain.add(innerBrain);
      
      // Simpler hemisphere division
      const divisionGeometry = new THREE.PlaneGeometry(6, 5);
      const divisionMaterial = new THREE.MeshBasicMaterial({
        color: 0x40e0d0,
        transparent: true,
        opacity: 0.2,
        side: THREE.DoubleSide
      });
      
      const division = new THREE.Mesh(divisionGeometry, divisionMaterial);
      division.rotation.z = Math.PI / 2;
      brain.add(division);
      
      // Add brain region markers
      createBrainRegions();
      
      // Set initial rotation for optimal viewing
      brain.rotation.x = 0.2;
      brain.rotation.y = 0.4;
      
      // Store brain globally for access
      window.brain = brain;
      
      console.log('Brain structure created successfully');
      } catch (e) {
        console.error('Error creating brain structure:', e);
      }
    }
    
    function createNeuralNetwork() {
      const connectionMaterial = new THREE.LineBasicMaterial({
        color: 0x40e0d0,
        transparent: true,
        opacity: 0.5,
        linewidth: 2
      });
      
      // Create more complex neural connections
      for (let i = 0; i < 200; i++) {
        const points = [];
        
        // Random start point within brain volume
        const start = new THREE.Vector3(
          (Math.random() - 0.5) * 5,
          (Math.random() - 0.5) * 5,
          (Math.random() - 0.5) * 5
        );
        
        // Random end point within brain volume
        const end = new THREE.Vector3(
          (Math.random() - 0.5) * 5,
          (Math.random() - 0.5) * 5,
          (Math.random() - 0.5) * 5
        );
        
        // Only create connections if points are within brain volume
        if (start.length() < 3 && end.length() < 3) {
          // Create more complex curved connection with multiple control points
          const mid1 = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.3);
          mid1.add(new THREE.Vector3(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
          ));
          
          const mid2 = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.7);
          mid2.add(new THREE.Vector3(
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2,
            (Math.random() - 0.5) * 2
          ));
          
          // Create cubic curve for more natural connections
          const curve = new THREE.CubicBezierCurve3(start, mid1, mid2, end);
          points.push(...curve.getPoints(15));
          
          const geometry = new THREE.BufferGeometry().setFromPoints(points);
          const line = new THREE.Line(geometry, connectionMaterial.clone());
          
          // Add pulsing effect data
          line.userData = { 
            pulse: true, 
            baseOpacity: 0.3 + Math.random() * 0.4,
            pulseSpeed: 0.5 + Math.random() * 2
          };
          
          brain.add(line);
        }
      }
    }
    
    // Create neural nodes (renamed from synapses for clarity)
    function createNeuralNodes() {
      // Create nodes of varying sizes for visual interest
      const nodeSizes = [0.08, 0.12, 0.18];
      
      for (let i = 0; i < 150; i++) {
        const nodeSize = nodeSizes[Math.floor(Math.random() * nodeSizes.length)];
        const nodeGeometry = new THREE.SphereGeometry(nodeSize, 16, 16);
        
        // Use bright electric blue for nodes
        const nodeMaterial = new THREE.MeshBasicMaterial({
          color: 0x00bfff,
          transparent: true,
          opacity: 0.8
        });
        
        const node = new THREE.Mesh(nodeGeometry, nodeMaterial.clone());
        
        // Position within brain volume with better distribution
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.acos((Math.random() * 2) - 1);
        const radius = 2.5 * Math.pow(Math.random(), 1/3); // Better radial distribution
        
        node.position.set(
          radius * Math.sin(phi) * Math.cos(theta),
          radius * Math.sin(phi) * Math.sin(theta),
          radius * Math.cos(phi)
        );
        
        // Add animation parameters
        node.userData = { 
          pulsate: true, 
          originalScale: 1,
          pulsateSpeed: 0.5 + Math.random() * 3,
          pulsateAmount: 0.3 + Math.random() * 0.5
        };
        
        brain.add(node);
      }
      
      // Add some particle effects for additional visual interest
      createParticleEffects();
    }
    
    // New function for particle effects
    function createParticleEffects() {
      const particleCount = 300;
      const particles = new THREE.BufferGeometry();
      const positions = new Float32Array(particleCount * 3);
      const sizes = new Float32Array(particleCount);
      
      for (let i = 0; i < particleCount; i++) {
        // Random position within a spherical volume
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.acos((Math.random() * 2) - 1);
        const radius = 3.3 * Math.pow(Math.random(), 1/3);
        
        positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[i * 3 + 2] = radius * Math.cos(phi);
        
        sizes[i] = 2 + Math.random() * 8; // Varied particle sizes
      }
      
      particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
      
      const particleMaterial = new THREE.PointsMaterial({
        color: 0x40e0d0,
        transparent: true,
        opacity: 0.4,
        size: 0.05,
        blending: THREE.AdditiveBlending,
        sizeAttenuation: true
      });
      
      const particleSystem = new THREE.Points(particles, particleMaterial);
      particleSystem.userData = { particles: true };
      brain.add(particleSystem);
    }
    
    function createBrainRegions() {
      try {
        // Create simpler glowing regions based on the reference image
        const regionGeometry = new THREE.SphereGeometry(0.7, 16, 16);
        
        // Define brain regions with accurate positioning based on the image
        window.regions = [
          { name: 'NOTEPAD', color: 0x00ff88, position: new THREE.Vector3(-1.8, 1.2, 1.2), category: 'cognitive' },
          { name: 'MEMORY LEAKAGE', color: 0xff6600, position: new THREE.Vector3(1.8, 1, 0.8), category: 'data' },
          { name: 'VECTORIZE & MAP', color: 0x00bfff, position: new THREE.Vector3(-1.5, -1.2, 1.5), category: 'data' },
          { name: 'TASK DISTRIBUTION & REPAIR', color: 0xff00aa, position: new THREE.Vector3(1.5, -1, 1.2), category: 'cognitive' },
          { name: 'WEB & NETWORK HUB', color: 0x00ffff, position: new THREE.Vector3(0, 2, 0), category: 'web' },
          { name: 'CODE EXECUTION', color: 0xffcc00, position: new THREE.Vector3(-2, 0, 0), category: 'code' },
          { name: 'SECURITY MONITOR', color: 0xff0000, position: new THREE.Vector3(2, 0, 0), category: 'security' },
          { name: 'INTERFACE BRIDGE', color: 0xcc00ff, position: new THREE.Vector3(0, 0, 2), category: 'browser' },
          { name: 'ANALYTICS ENGINE', color: 0x00ccff, position: new THREE.Vector3(0, -2, 0), category: 'performance' }
      ];
      
      // Create regions with simplified materials and properties
      regions.forEach(region => {
        // Simple region sphere with basic material
        const regionMaterial = new THREE.MeshBasicMaterial({
          color: region.color,
          transparent: true,
          opacity: 0.6
        });
        
        const regionMesh = new THREE.Mesh(regionGeometry, regionMaterial);
        regionMesh.position.copy(region.position);
        regionMesh.userData = { 
          glow: true, 
          baseOpacity: 0.6,
          glowSpeed: 0.5,
          regionCategory: region.category,
          color: region.color
        };
        brain.add(regionMesh);
        
        // Add just a few particles for visual effect, but much fewer
        const particleCount = 10; // Reduced count
        const particleGeometry = new THREE.BufferGeometry();
        const particlePositions = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
          // Simpler positioning directly around the region
          particlePositions[i * 3] = region.position.x + (Math.random() - 0.5) * 0.8;
          particlePositions[i * 3 + 1] = region.position.y + (Math.random() - 0.5) * 0.8;
          particlePositions[i * 3 + 2] = region.position.z + (Math.random() - 0.5) * 0.8;
        }
        
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
          color: region.color,
          size: 0.08,
          transparent: true,
          opacity: 0.7
        });
        
        const particles = new THREE.Points(particleGeometry, particleMaterial);
        particles.userData = { 
          regionParticles: true,
          regionName: region.name,
          pulseSpeed: 0.5
        };
        brain.add(particles);
      });
      
      console.log('Brain regions created successfully');
      window.brain = brain; // Store brain globally for access
    } catch (e) {
      console.error('Error creating brain regions:', e);
    }
  }
    
  function onWindowResize() {
      const canvas = document.getElementById('brain-canvas');
      const container = canvas.parentElement;
      const containerRect = container.getBoundingClientRect();
      
      camera.aspect = containerRect.width / containerRect.height;
      camera.updateProjectionMatrix();
      renderer.setSize(containerRect.width, containerRect.height);
    }
    
    function animate() {
      // Request next frame first
      requestAnimationFrame(animate);
      
      // Safety check - make sure we have necessary objects
      if (!brain || !renderer || !scene || !camera) return;
      
      const time = Date.now() * 0.001;
      
      try {
        // Animate brain components with simplified effects
        brain.children.forEach(child => {
          if (!child) return;
          
          if (child.userData.pulsate && child.scale) {
            const pulseFactor = child.userData.pulsateAmount || 0.3;
            const scale = 1 + Math.sin(time * 2) * pulseFactor;
            child.scale.set(scale, scale, scale);
          }
          
          if (child.material) {
            if (child.userData.pulse) {
              const pulseSpeed = child.userData.pulseSpeed || 2;
              child.material.opacity = 0.5 + Math.sin(time * pulseSpeed) * 0.2;
            }
            
            if (child.userData.glow) {
              child.material.opacity = 0.6 + Math.sin(time * 1.5) * 0.2;
              
              // Highlight brain region when its corresponding tool category is selected
              if (child.userData.regionCategory && child.material.emissive) {
                const selectedCategory = window.selectedToolCategory || '';
                if (selectedCategory === child.userData.regionCategory) {
                  // Set emissive color for highlighting
                  child.material.emissive.setHex(child.userData.color || 0x00bfff);
                  child.material.emissiveIntensity = 0.5 + Math.sin(time * 3) * 0.2;
                } else {
                  child.material.emissiveIntensity = 0.2;
                }
              }
            }
          }
        });
        
        // Add gentle auto-rotation if not being controlled by user
        if (!isMouseDown && brain) {
          brain.rotation.y += 0.002;
        }
        
        // Render the scene
        renderer.render(scene, camera);
      } catch (e) {
        console.error('Error in animation loop:', e);
      }
      }
      
      // Variable to track mouse state globally
      let isMouseDown = false;
    
    // Tool interaction functionality
    function initializeToolInteractions() {
      const toolCategorySelect = document.getElementById('tool-category-select');
      const toolSearch = document.getElementById('tool-search');
      const toolItems = document.querySelectorAll('.tool-item');
      const toolCategories = document.querySelectorAll('.tool-category');
      const toolDetailsPanel = document.getElementById('tool-details-panel');
      
      // Tool workflows mapping
      const toolWorkflows = {
        // Core Cognitive
        "plan_and_decompose_task": [1, 3],
        "semantic_router": [1, 2],
        "summon_agent": [2, 3],
        "meta_reason": [1, 4, 5],
        "counterfactual_simulate": [1, 4],
        
        // Web & Network
        "smart_fetch": [2, 4],
        "websocket_connect": [2, 3],
        "publish_mqtt_message": [2, 6],
        "cors_handler_generate": [2, 5],
        "rpc_optimizer": [2, 5],
        
        // Code Management
        "execute_code_sandbox": [2, 4],
        "dependency_resolver": [1, 2],
        "bundle_code": [2, 3],
        "transpile_code": [2],
        "typescript_evolver": [2, 5],
        
        // Browser & DOM
        "dom_element_query": [2],
        "shadow_dom_interact": [2, 4],
        "accessibility_check": [2, 5],
        "render_headless_browser": [2, 4],
        "service_worker_control": [2, 3],
        
        // Data Processing
        "parse_data": [2],
        "validate_data": [2, 4],
        "schema_breeder": [1, 2, 5],
        "vector_embed_text": [2, 3],
        "privacy_sentinel_redact": [2, 4],
        
        // Security
        "encrypt_data_aes_gcm": [2],
        "generate_csp": [5],
        "compliance_audit_check": [4, 5],
        "authenticate_user_oauth": [2, 6],
        
        // Performance
        "log_event": [3, 4, 5],
        "performance_audit": [5],
        "monitor_resource_changes": [5],
        "perf_profile_code_block": [2, 5],
        
        // API Interaction
        "mdn_query": [1, 2],
        "webgl_shader_compile": [2],
        "calendar_schedule_event": [2, 6],
        "llm_response_compress": [6],
        
        // Utilities
        "generate_uuid": [2],
        "text_summarize": [2, 6],
        "text_translate": [2, 6],
        "extract_entities_from_text": [1, 2]
      };
      
      // Workflow descriptions
      const workflowDescriptions = {
        1: "Task Intake & Planning",
        2: "Tool Invocation & Data Handling",
        3: "State Management & Orchestration",
        4: "Error Handling & Recovery",
        5: "Monitoring & Optimization",
        6: "Response Synthesis & Delivery"
      };
      
      // Category filter
      toolCategorySelect.addEventListener('change', function() {
        const selectedCategory = this.value;
        window.selectedToolCategory = selectedCategory;
        
        toolCategories.forEach(category => {
          if (!selectedCategory || category.dataset.category === selectedCategory) {
            category.style.display = 'block';
          } else {
            category.style.display = 'none';
          }
        });
        
        // Update tool count
        const visibleTools = Array.from(toolItems).filter(item => 
          !selectedCategory || item.closest('.tool-category').dataset.category === selectedCategory
        );
        document.getElementById('tools-count').textContent = `${visibleTools.length} Tools`;
        
        // Hide tool details panel when changing categories
        toolDetailsPanel.style.display = 'none';
      });
      
      // Search functionality
      toolSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        toolCategories.forEach(category => {
          const categoryTools = category.querySelectorAll('.tool-item');
          const visibleTools = Array.from(categoryTools).filter(tool => {
            const match = tool.textContent.toLowerCase().includes(searchTerm);
            tool.style.display = match ? 'block' : 'none';
            return match;
          });
          
          // Hide category if no tools match
          category.style.display = visibleTools.length > 0 ? 'block' : 'none';
        });
        
        // Hide tool details panel when searching
        if (searchTerm) {
          toolDetailsPanel.style.display = 'none';
        }
      });
      
      // Tool selection
      toolItems.forEach(tool => {
        tool.addEventListener('click', function() {
          // Remove active state from all tools
          toolItems.forEach(t => t.classList.remove('active'));
          
          // Add active state to clicked tool
          this.classList.add('active');
          
          const toolName = this.textContent.trim();
          window.activeTool = toolName;
          
          // Show tool details
          document.getElementById('tool-detail-name').textContent = toolName;
          document.getElementById('tool-detail-signature').textContent = this.dataset.signature || 'No signature available';
          document.getElementById('tool-detail-purpose').textContent = this.dataset.purpose || 'No description available';
          
          // Generate workflow list
          const workflowsList = document.getElementById('tool-detail-workflows');
          workflowsList.innerHTML = '';
          
          const workflows = toolWorkflows[toolName] || [];
          workflows.forEach(wfId => {
            const li = document.createElement('li');
            li.textContent = workflowDescriptions[wfId];
            workflowsList.appendChild(li);
          });
          
          // Show tool details panel
          toolDetailsPanel.style.display = 'block';
          
          // Add to activity log
          addLogEntry(`Tool activated: ${toolName}`);
          
          // Flash the corresponding brain region
          const category = this.closest('.tool-category').dataset.category;
          flashBrainRegion(category);
        });
      });
    }
    
    function flashBrainRegion(category) {
      // This will cause the brain region to flash when a tool is selected
      window.selectedToolCategory = category;
      
      // Add to activity log
      addLogEntry(`Brain region activated: ${category.toUpperCase()}`);
      
      // Find the corresponding brain region and highlight it
      if (window.brain && window.regions) {
        const matchingRegions = window.regions.filter(r => r.category === category);
        
        if (matchingRegions.length > 0) {
          // Highlight the matching regions in the brain visualization
          window.brain.children.forEach(child => {
            if (child.userData.regionCategory === category) {
              // Increase emission intensity temporarily
              if (child.material) {
                child.material.emissiveIntensity = 1.0;
                
                // Reset after animation
                setTimeout(() => {
                  if (child.material) {
                    child.material.emissiveIntensity = 0.2;
                  }
                }, 2000);
              }
            }
          });
        }
      }
    }
    
    function addLogEntry(message) {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const timeString = `${hours}:${minutes}:${seconds}`;
      
      const logEntry = document.createElement('div');
      logEntry.className = 'log-entry';
      logEntry.innerHTML = `
        <div class="log-time">${timeString}</div>
        <div class="log-message">${message}</div>
      `;
      
      const activityLog = document.querySelector('.activity-log');
      activityLog.insertBefore(logEntry, activityLog.firstChild);
      
      // Remove oldest entry if there are too many
      const entries = activityLog.querySelectorAll('.log-entry');
      if (entries.length > 10) {
        activityLog.removeChild(entries[entries.length - 1]);
      }
    }

    // Workflow interaction functionality
    function initializeWorkflowInteractions() {
      const workflowSteps = document.querySelectorAll('.workflow-step');
      const workflowDetail = document.getElementById('workflow-detail');
      const workflowDetailHeader = document.querySelector('.workflow-detail-header');
      const workflowDetailContent = document.querySelector('.workflow-detail-content');
      
      const workflowDetails = {
        1: {
          title: "Method 1: Task Intake, Semantic Deconstruction & Strategic Planning",
          content: "Understands requests, breaks them down into high-level plans, and identifies resource needs. Uses semantic_router, plan_and_decompose_task, meta_reason, and context gathering tools."
        },
        2: {
          title: "Method 2: Dynamic Tool Invocation & Data Handling Protocol",
          content: "Securely executes specific tools with correct data transformation. Handles network operations, code execution, browser/DOM operations, and data security tools."
        },
        3: {
          title: "Method 3: State Management & Inter-Step Orchestration",
          content: "Updates task state, manages intermediate data, and triggers next steps. Uses memory storage, execution graph updates, and conditional logic execution."
        },
        4: {
          title: "Method 4: Error Handling, Retry & Escalation Protocol",
          content: "Attempts recovery from errors or graceful failure. Includes error capture, immediate retry, root cause analysis, and automated correction attempts."
        },
        5: {
          title: "Method 5: Continuous Monitoring, Learning & Optimization",
          content: "Improves system performance over time. Aggregates logs, integrates feedback, optimizes tools and plans, and augments knowledge bases."
        },
        6: {
          title: "Method 6: Final Response Synthesis & Delivery Protocol",
          content: "Compiles results into coherent responses. Handles result aggregation, response composition, output formatting, and final delivery."
        }
      };
      
      workflowSteps.forEach(step => {
        step.addEventListener('click', function() {
          const workflowId = parseInt(this.dataset.workflowId);
          
          // Update active state
          workflowSteps.forEach(s => s.classList.remove('active'));
          this.classList.add('active');
          
          // Show workflow details
          const details = workflowDetails[workflowId];
          workflowDetailHeader.textContent = details.title;
          workflowDetailContent.textContent = details.content;
          workflowDetail.style.display = 'block';
          
          // Add to activity log
          addLogEntry(`Workflow protocol examined: Method ${workflowId}`);
          
          // Animate workflow in brain (if applicable)
          animateWorkflowInBrain(workflowId);
        });
      });
      
      // Set default active workflow
      if (workflowSteps.length > 0) {
        workflowSteps[0].click();
      }
    }
    
    function animateWorkflowInBrain(workflowId) {
      // This could be used to highlight specific brain regions based on the workflow
      console.log(`Animating workflow ${workflowId} in brain`);
    }

    // Production-ready system initialization
    function startBrainCenter() {
      try {
        console.log('LLM Brain Center initialization sequence starting...');
        
        // Initialize core subsystems in the correct dependency order
        addLogEntry('Initializing 3D neural visualization engine', 'system');
        init3DBrain();
        
        addLogEntry('Registering tools and capabilities', 'system');
        initializeToolInteractions();
        
        addLogEntry('Configuring workflow protocols', 'system');
        initializeWorkflowInteractions();
        
        addLogEntry('Building agent registry', 'system');
        initializeAgentSystem();
        
        addLogEntry('Starting system monitoring', 'system');
        initSystemLogging();
        
        // Start core animation loop
        addLogEntry('Neural activity simulation engaged', 'system');
        animate();
        
        // Register offline and error handlers for production resilience
        window.addEventListener('offline', handleConnectionLoss);
        window.addEventListener('error', handleSystemError);
        
        addLogEntry('LLM Brain Center fully operational', 'success');
      } catch (error) {
        console.error('Critical initialization error:', error);
        handleSystemError(error);
      }
    }
    
    // Connection loss handler for production resilience
    function handleConnectionLoss() {
      addLogEntry('Network connectivity lost - entering offline mode', 'error');
      // In production: would initiate local-only operation mode
    }
    
    // System error handler for production resilience
    function handleSystemError(error) {
      addLogEntry(`System error encountered: ${error.message || 'Unknown error'}`, 'error', {
        stack: error.stack,
        component: error.component || 'unknown',
        recoverable: error.recoverable !== false
      });
      
      // In production: would attempt recovery or graceful degradation
    }
    
    // Initialize system when page loads with proper error handling
    window.addEventListener('load', function() {
      // Delay initialization to ensure DOM is fully ready
      setTimeout(startBrainCenter, 200);
    });
    
    // Add resize event listener to ensure proper rendering
    window.addEventListener('resize', function() {
      // Allow a short delay for DOM to update
      setTimeout(onWindowResize, 100);
    });
  </script>
</body>
</html>