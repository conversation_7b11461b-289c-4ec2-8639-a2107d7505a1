const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * Secure context bridge for safely exposing Electron APIs to renderer
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // Backend related functions
  checkBackendStatus: () => ipcRenderer.invoke('check-backend'),
  restartBackend: () => ipcRenderer.invoke('restart-backend'),
  
  // System-related functions for the Agent Lee interface
  getAppVersion: () => process.env.npm_package_version || '1.0.0',
  getPlatform: () => process.platform,
  
  // API interface for communicating with backend
  apiRequest: async (endpoint, method = 'GET', data = null) => {
    try {
      const baseUrl = 'http://localhost:8000';
      const url = `${baseUrl}${endpoint}`;
      
      const options = {
        method,
        headers: {
          'Content-Type': 'application/json'
        }
      };
      
      if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
      }
      
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
});