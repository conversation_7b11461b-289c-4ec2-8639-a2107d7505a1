📁 D:\Agentleefreshidea\.qodo


📁 D:\Agentleefreshidea\.venv
    - .gitignore [71 bytes]     - pyvenv.cfg [229 bytes]

📁 D:\Agentleefreshidea\backend
    - .env.example [826 bytes]     - agentlee_controller_v2.py [16081 bytes]     - agentlee_controller.py [16447 bytes]     - config.py [5267 bytes]     - ENHANCEMENTS.md [6813 bytes]     - llm_service.py [9375 bytes]     - models.py [3130 bytes]     - requirements.txt [262 bytes]     - start_backend.ps1 [968 bytes]     - start_backend.sh [936 bytes]     - start_enhanced.py [6147 bytes]     - test_enhancements.py [4756 bytes]

📁 D:\Agentleefreshidea\electron
    - main.js [5098 bytes]     - package.json [3037 bytes]     - preload.js [1288 bytes]

📁 D:\Agentleefreshidea\frontend
    - <PERSON> Lee's Agent Center.html [42974 bytes]     - <PERSON>'s Dynamic To-Do List.html [65992 bytes]     - <PERSON>'s Integrated Workers Center.html [96222 bytes]     - <PERSON><PERSON><PERSON>'sDB.html [77089 bytes]     - AgetleeAvatar.png [1869131 bytes]     - LLM BRAIN CENTER.html [91242 bytes]

📁 D:\Agentleefreshidea\js
    - api-client.js [11178 bytes]     - system-monitor.js [7499 bytes]

📁 D:\Agentleefreshidea\.venv\Include


📁 D:\Agentleefreshidea\.venv\Lib


📁 D:\Agentleefreshidea\.venv\Scripts
    - activate [2172 bytes]     - activate.bat [1004 bytes]     - activate.fish [2263 bytes]     - Activate.ps1 [27969 bytes]     - alembic.exe [108394 bytes]     - clear_comtypes_cache.exe [108400 bytes]     - deactivate.bat [393 bytes]     - dotenv.exe [108393 bytes]     - fastapi.exe [108391 bytes]     - mako-render.exe [108394 bytes]     - normalizer.exe [108407 bytes]     - pip.exe [108402 bytes]     - pip3.13.exe [108402 bytes]     - pip3.exe [108402 bytes]     - py.test.exe [108402 bytes]     - pyrsa-decrypt.exe [108393 bytes]     - pyrsa-encrypt.exe [108393 bytes]     - pyrsa-keygen.exe [108391 bytes]     - pyrsa-priv2pub.exe [108414 bytes]     - pyrsa-sign.exe [108387 bytes]     - pyrsa-verify.exe [108391 bytes]     - pytest.exe [108402 bytes]     - python.exe [254832 bytes]     - pythonw.exe [251248 bytes]     - pywin32_postinstall.exe [108413 bytes]     - pywin32_postinstall.py [25736 bytes]     - pywin32_testall.exe [108409 bytes]     - pywin32_testall.py [3847 bytes]     - tqdm.exe [108388 bytes]     - uvicorn.exe [108392 bytes]

📁 D:\Agentleefreshidea\.venv\Include\site


📁 D:\Agentleefreshidea\.venv\Include\site\python3.13


📁 D:\Agentleefreshidea\.venv\Include\site\python3.13\greenlet
    - greenlet.h [4755 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages
    - google_auth_httplib2.py [10211 bytes]     - google_generativeai-0.8.5-py3.13-nspkg.pth [467 bytes]     - py.py [329 bytes]     - pythoncom.py [143 bytes]     - PyWin32.chm [2639774 bytes]     - pywin32.pth [185 bytes]     - pywin32.version.txt [5 bytes]     - typing_extensions.py [172654 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\__pycache__
    - google_auth_httplib2.cpython-313.pyc [10997 bytes]     - py.cpython-313.pyc [472 bytes]     - pythoncom.cpython-313.pyc [299 bytes]     - typing_extensions.cpython-313.pyc [178179 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest
    - __init__.py [391 bytes]     - _argcomplete.py [3776 bytes]     - _version.py [511 bytes]     - cacheprovider.py [22373 bytes]     - capture.py [35330 bytes]     - compat.py [11467 bytes]     - debugging.py [13260 bytes]     - deprecated.py [3147 bytes]     - doctest.py [26255 bytes]     - faulthandler.py [3674 bytes]     - fixtures.py [73550 bytes]     - freeze_support.py [1291 bytes]     - helpconfig.py [8895 bytes]     - hookspec.py [42831 bytes]     - junitxml.py [25574 bytes]     - legacypath.py [16588 bytes]     - logging.py [35124 bytes]     - main.py [37416 bytes]     - monkeypatch.py [14598 bytes]     - nodes.py [26483 bytes]     - outcomes.py [10532 bytes]     - pastebin.py [3978 bytes]     - pathlib.py [37569 bytes]     - py.typed [0 bytes]     - pytester_assertions.py [2244 bytes]     - pytester.py [61552 bytes]     - python_api.py [40122 bytes]     - python_path.py [745 bytes]     - python.py [64851 bytes]     - recwarn.py [13227 bytes]     - reports.py [21331 bytes]     - runner.py [19436 bytes]     - scope.py [2798 bytes]     - setuponly.py [3306 bytes]     - setupplan.py [1184 bytes]     - skipping.py [10217 bytes]     - stash.py [3090 bytes]     - stepwise.py [4596 bytes]     - terminal.py [57393 bytes]     - threadexception.py [3005 bytes]     - timing.py [413 bytes]     - tmpdir.py [11375 bytes]     - unittest.py [15614 bytes]     - unraisableexception.py [3252 bytes]     - warning_types.py [4388 bytes]     - warnings.py [5211 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\adodbapi
    - __init__.py [2731 bytes]     - ado_consts.py [9873 bytes]     - adodbapi.py [48928 bytes]     - apibase.py [27130 bytes]     - is64bit.py [1025 bytes]     - license.txt [26925 bytes]     - process_connect_string.py [5420 bytes]     - readme.txt [4782 bytes]     - schema_table.py [438 bytes]     - setup.py [2194 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\aifc
    - __init__.py [34479 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic
    - __init__.py [63 bytes]     - __main__.py [78 bytes]     - command.py [24855 bytes]     - config.py [32897 bytes]     - context.py [195 bytes]     - context.pyi [31773 bytes]     - environment.py [43 bytes]     - migration.py [41 bytes]     - op.py [167 bytes]     - op.pyi [50711 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic-1.16.1.dist-info
    - entry_points.txt [48 bytes]     - INSTALLER [4 bytes]     - METADATA [7265 bytes]     - RECORD [11608 bytes]     - REQUESTED [0 bytes]     - top_level.txt [8 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\annotated_types
    - __init__.py [13819 bytes]     - py.typed [0 bytes]     - test_cases.py [6421 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\annotated_types-0.7.0.dist-info
    - INSTALLER [4 bytes]     - METADATA [15046 bytes]     - RECORD [802 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio
    - __init__.py [4993 bytes]     - from_thread.py [17478 bytes]     - lowlevel.py [4169 bytes]     - py.typed [0 bytes]     - pytest_plugin.py [9375 bytes]     - to_interpreter.py [6527 bytes]     - to_process.py [9595 bytes]     - to_thread.py [2396 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio-4.9.0.dist-info
    - entry_points.txt [39 bytes]     - INSTALLER [4 bytes]     - LICENSE [1081 bytes]     - METADATA [4682 bytes]     - RECORD [5964 bytes]     - top_level.txt [6 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\apiclient
    - __init__.py [746 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\audioop
    - __init__.py [818 bytes]     - __init__.pyi [2182 bytes]     - _audioop.pyd [49152 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\audioop_lts-0.2.1.dist-info
    - INSTALLER [4 bytes]     - LICENSE [14215 bytes]     - METADATA [1701 bytes]     - RECORD [856 bytes]     - top_level.txt [8 bytes]     - WHEEL [100 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\cachetools
    - __init__.py [21803 bytes]     - _decorators.py [3832 bytes]     - func.py [3719 bytes]     - keys.py [1777 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\cachetools-5.5.2.dist-info
    - INSTALLER [4 bytes]     - LICENSE [1085 bytes]     - METADATA [5379 bytes]     - RECORD [1012 bytes]     - top_level.txt [11 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\certifi
    - __init__.py [94 bytes]     - __main__.py [243 bytes]     - cacert.pem [283771 bytes]     - core.py [4426 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\certifi-2025.4.26.dist-info
    - INSTALLER [4 bytes]     - METADATA [2473 bytes]     - RECORD [1023 bytes]     - top_level.txt [8 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\charset_normalizer
    - __init__.py [1638 bytes]     - __main__.py [115 bytes]     - api.py [23285 bytes]     - cd.py [12917 bytes]     - constant.py [44728 bytes]     - legacy.py [2351 bytes]     - md__mypyc.cp313-win_amd64.pyd [125440 bytes]     - md.cp313-win_amd64.pyd [10752 bytes]     - md.py [20780 bytes]     - models.py [12754 bytes]     - py.typed [0 bytes]     - utils.py [12584 bytes]     - version.py [123 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\charset_normalizer-3.4.2.dist-info
    - entry_points.txt [65 bytes]     - INSTALLER [4 bytes]     - METADATA [36474 bytes]     - RECORD [2775 bytes]     - top_level.txt [19 bytes]     - WHEEL [101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\chunk
    - __init__.py [5769 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\click
    - __init__.py [4473 bytes]     - _compat.py [18693 bytes]     - _termui_impl.py [26712 bytes]     - _textwrap.py [1400 bytes]     - _winconsole.py [8465 bytes]     - core.py [117343 bytes]     - decorators.py [18461 bytes]     - exceptions.py [9891 bytes]     - formatting.py [9726 bytes]     - globals.py [1923 bytes]     - parser.py [18979 bytes]     - py.typed [0 bytes]     - shell_completion.py [19857 bytes]     - termui.py [30847 bytes]     - testing.py [18702 bytes]     - types.py [38389 bytes]     - utils.py [20245 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\click-8.2.1.dist-info
    - INSTALLER [4 bytes]     - METADATA [2471 bytes]     - RECORD [2415 bytes]     - WHEEL [82 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\colorama
    - __init__.py [266 bytes]     - ansi.py [2522 bytes]     - ansitowin32.py [11128 bytes]     - initialise.py [3325 bytes]     - win32.py [6181 bytes]     - winterm.py [7134 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\colorama-0.4.6.dist-info
    - INSTALLER [4 bytes]     - METADATA [17158 bytes]     - RECORD [2174 bytes]     - WHEEL [105 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes
    - __init__.py [9010 bytes]     - _comobject.py [19644 bytes]     - _memberspec.py [24888 bytes]     - _meta.py [3916 bytes]     - _npsupport.py [5244 bytes]     - _safearray.py [4558 bytes]     - _tlib_version_checker.py [616 bytes]     - _vtbl.py [16069 bytes]     - automation.py [35295 bytes]     - clear_cache.py [1759 bytes]     - connectionpoints.py [4669 bytes]     - errorinfo.py [5929 bytes]     - git.py [2750 bytes]     - GUID.py [3579 bytes]     - hints.pyi [11358 bytes]     - hresult.py [2997 bytes]     - logutil.py [2014 bytes]     - messageloop.py [1664 bytes]     - patcher.py [2046 bytes]     - persist.py [8899 bytes]     - safearray.py [17922 bytes]     - shelllink.py [10933 bytes]     - stream.py [2573 bytes]     - typeinfo.py [45144 bytes]     - util.py [3007 bytes]     - viewobject.py [6502 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes-1.4.11.dist-info
    - entry_points.txt [67 bytes]     - INSTALLER [4 bytes]     - METADATA [7183 bytes]     - RECORD [19348 bytes]     - top_level.txt [9 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\dotenv
    - __init__.py [1292 bytes]     - __main__.py [129 bytes]     - cli.py [5759 bytes]     - ipython.py [1303 bytes]     - main.py [12388 bytes]     - parser.py [5186 bytes]     - py.typed [26 bytes]     - variables.py [2348 bytes]     - version.py [22 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi
    - __init__.py [1082 bytes]     - __main__.py [37 bytes]     - _compat.py [23953 bytes]     - applications.py [176316 bytes]     - background.py [1768 bytes]     - cli.py [418 bytes]     - concurrency.py [1424 bytes]     - datastructures.py [5766 bytes]     - encoders.py [11068 bytes]     - exception_handlers.py [1332 bytes]     - exceptions.py [4969 bytes]     - logger.py [54 bytes]     - param_functions.py [64019 bytes]     - params.py [28237 bytes]     - py.typed [0 bytes]     - requests.py [142 bytes]     - responses.py [1761 bytes]     - routing.py [176216 bytes]     - staticfiles.py [69 bytes]     - templating.py [76 bytes]     - testclient.py [66 bytes]     - types.py [383 bytes]     - utils.py [7948 bytes]     - websockets.py [222 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi-0.115.12.dist-info
    - entry_points.txt [61 bytes]     - INSTALLER [4 bytes]     - METADATA [27671 bytes]     - RECORD [6667 bytes]     - REQUESTED [0 bytes]     - WHEEL [90 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_ai_generativelanguage-0.6.15.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11358 bytes]     - METADATA [5708 bytes]     - RECORD [80128 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_api_core-2.24.2.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11358 bytes]     - METADATA [3004 bytes]     - RECORD [10245 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_api_python_client-2.170.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11357 bytes]     - METADATA [6698 bytes]     - RECORD [70328 bytes]     - top_level.txt [58 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_auth_httplib2-0.2.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11357 bytes]     - METADATA [2179 bytes]     - RECORD [693 bytes]     - top_level.txt [21 bytes]     - WHEEL [110 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_auth-2.40.2.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11357 bytes]     - METADATA [6203 bytes]     - RECORD [11275 bytes]     - top_level.txt [23 bytes]     - WHEEL [109 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_generativeai-0.8.5.dist-info
    - INSTALLER [4 bytes]     - METADATA [3937 bytes]     - namespace_packages.txt [7 bytes]     - RECORD [12897 bytes]     - REQUESTED [0 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\googleapiclient
    - __init__.py [904 bytes]     - _auth.py [5736 bytes]     - _helpers.py [6723 bytes]     - channel.py [11054 bytes]     - discovery.py [66341 bytes]     - errors.py [5460 bytes]     - http.py [68241 bytes]     - mimeparse.py [6530 bytes]     - model.py [14085 bytes]     - sample_tools.py [4315 bytes]     - schema.py [10414 bytes]     - version.py [599 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\googleapis_common_protos-1.70.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11358 bytes]     - METADATA [9293 bytes]     - RECORD [21073 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet
    - __init__.py [1723 bytes]     - _greenlet.cp313-win_amd64.pyd [219136 bytes]     - CObjects.cpp [3508 bytes]     - greenlet_allocator.hpp [1582 bytes]     - greenlet_compiler_compat.hpp [4346 bytes]     - greenlet_cpython_compat.hpp [4068 bytes]     - greenlet_exceptions.hpp [4503 bytes]     - greenlet_internal.hpp [2709 bytes]     - greenlet_refs.hpp [34436 bytes]     - greenlet_slp_switch.hpp [3198 bytes]     - greenlet_thread_support.hpp [867 bytes]     - greenlet.cpp [10996 bytes]     - greenlet.h [4755 bytes]     - PyGreenlet.cpp [23441 bytes]     - PyGreenlet.hpp [1463 bytes]     - PyGreenletUnswitchable.cpp [4375 bytes]     - PyModule.cpp [8587 bytes]     - slp_platformselect.h [3841 bytes]     - TBrokenGreenlet.cpp [1021 bytes]     - TExceptionState.cpp [1359 bytes]     - TGreenlet.cpp [25731 bytes]     - TGreenlet.hpp [28043 bytes]     - TGreenletGlobals.cpp [3264 bytes]     - TMainGreenlet.cpp [3276 bytes]     - TPythonState.cpp [15779 bytes]     - TStackState.cpp [7381 bytes]     - TThreadState.hpp [19131 bytes]     - TThreadStateCreator.hpp [2610 bytes]     - TThreadStateDestroy.cpp [8169 bytes]     - TUserGreenlet.cpp [23553 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet-3.2.2.dist-info
    - INSTALLER [4 bytes]     - METADATA [4193 bytes]     - RECORD [10215 bytes]     - top_level.txt [9 bytes]     - WHEEL [101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc
    - __init__.py [84681 bytes]     - _auth.py [2715 bytes]     - _channel.py [83613 bytes]     - _common.py [6967 bytes]     - _compression.py [2054 bytes]     - _grpcio_metadata.py [26 bytes]     - _interceptor.py [26675 bytes]     - _observability.py [10716 bytes]     - _plugin_wrapping.py [4518 bytes]     - _runtime_protos.py [5970 bytes]     - _server.py [52413 bytes]     - _simple_stubs.py [25198 bytes]     - _typing.py [2853 bytes]     - _utilities.py [7265 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc_status
    - __init__.py [580 bytes]     - _async.py [2004 bytes]     - _common.py [959 bytes]     - rpc_status.py [2992 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpcio_status-1.71.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [29687 bytes]     - METADATA [1064 bytes]     - RECORD [1050 bytes]     - top_level.txt [12 bytes]     - WHEEL [92 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpcio-1.71.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [30297 bytes]     - METADATA [3967 bytes]     - RECORD [8604 bytes]     - top_level.txt [5 bytes]     - WHEEL [102 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\h11
    - __init__.py [1507 bytes]     - _abnf.py [4815 bytes]     - _connection.py [26863 bytes]     - _events.py [11792 bytes]     - _headers.py [10412 bytes]     - _readers.py [8590 bytes]     - _receivebuffer.py [5252 bytes]     - _state.py [13231 bytes]     - _util.py [4888 bytes]     - _version.py [686 bytes]     - _writers.py [5081 bytes]     - py.typed [7 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\h11-0.16.0.dist-info
    - INSTALLER [4 bytes]     - METADATA [8348 bytes]     - RECORD [1830 bytes]     - top_level.txt [4 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\httplib2
    - __init__.py [69396 bytes]     - auth.py [2158 bytes]     - cacerts.txt [137365 bytes]     - certs.py [971 bytes]     - error.py [954 bytes]     - iri2uri.py [4153 bytes]     - socks.py [19701 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\httplib2-0.22.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [1086 bytes]     - METADATA [2618 bytes]     - RECORD [1304 bytes]     - top_level.txt [9 bytes]     - WHEEL [92 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\idna
    - __init__.py [868 bytes]     - codec.py [3422 bytes]     - compat.py [316 bytes]     - core.py [13239 bytes]     - idnadata.py [78306 bytes]     - intranges.py [1898 bytes]     - package_data.py [21 bytes]     - py.typed [0 bytes]     - uts46data.py [239289 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\idna-3.10.dist-info
    - INSTALLER [4 bytes]     - LICENSE.md [1541 bytes]     - METADATA [10158 bytes]     - RECORD [1384 bytes]     - WHEEL [81 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\iniconfig
    - __init__.py [5462 bytes]     - _parse.py [2436 bytes]     - _version.py [511 bytes]     - exceptions.py [490 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\iniconfig-2.1.0.dist-info
    - INSTALLER [4 bytes]     - METADATA [2651 bytes]     - RECORD [992 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi
    - __init__.py [1267 bytes]     - install.py [28057 bytes]     - isapicon.py [4236 bytes]     - PyISAPI_loader.dll [69120 bytes]     - README.txt [330 bytes]     - simple.py [2566 bytes]     - threaded_extension.py [7526 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako
    - __init__.py [243 bytes]     - _ast_util.py [20247 bytes]     - ast.py [6642 bytes]     - cache.py [7680 bytes]     - cmd.py [2813 bytes]     - codegen.py [47736 bytes]     - compat.py [1820 bytes]     - exceptions.py [12530 bytes]     - filters.py [4658 bytes]     - lexer.py [16321 bytes]     - lookup.py [12428 bytes]     - parsetree.py [19021 bytes]     - pygen.py [10416 bytes]     - pyparser.py [7558 bytes]     - runtime.py [27804 bytes]     - template.py [23563 bytes]     - util.py [10638 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako-1.3.10.dist-info
    - entry_points.txt [512 bytes]     - INSTALLER [4 bytes]     - METADATA [2919 bytes]     - RECORD [4769 bytes]     - top_level.txt [5 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\markupsafe
    - __init__.py [13609 bytes]     - _native.py [218 bytes]     - _speedups.c [4353 bytes]     - _speedups.cp313-win_amd64.pyd [13312 bytes]     - _speedups.pyi [42 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\MarkupSafe-3.0.2.dist-info
    - INSTALLER [4 bytes]     - LICENSE.txt [1503 bytes]     - METADATA [4067 bytes]     - RECORD [1095 bytes]     - top_level.txt [11 bytes]     - WHEEL [101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\multipart
    - __init__.py [935 bytes]     - decoders.py [40 bytes]     - exceptions.py [42 bytes]     - multipart.py [41 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\packaging
    - __init__.py [494 bytes]     - _elffile.py [3286 bytes]     - _manylinux.py [9596 bytes]     - _musllinux.py [2694 bytes]     - _parser.py [10221 bytes]     - _structures.py [1431 bytes]     - _tokenizer.py [5310 bytes]     - markers.py [12049 bytes]     - metadata.py [34739 bytes]     - py.typed [0 bytes]     - requirements.py [2947 bytes]     - specifiers.py [40055 bytes]     - tags.py [22745 bytes]     - utils.py [5050 bytes]     - version.py [16676 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\packaging-25.0.dist-info
    - INSTALLER [4 bytes]     - METADATA [3281 bytes]     - RECORD [2792 bytes]     - WHEEL [82 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip
    - __init__.py [357 bytes]     - __main__.py [854 bytes]     - __pip-runner__.py [1450 bytes]     - py.typed [286 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip-24.3.1.dist-info
    - AUTHORS.txt [10925 bytes]     - entry_points.txt [87 bytes]     - INSTALLER [4 bytes]     - LICENSE.txt [1093 bytes]     - METADATA [3677 bytes]     - RECORD [65622 bytes]     - REQUESTED [0 bytes]     - top_level.txt [4 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pluggy
    - __init__.py [811 bytes]     - _callers.py [5991 bytes]     - _hooks.py [25218 bytes]     - _manager.py [20219 bytes]     - _result.py [3098 bytes]     - _tracing.py [2073 bytes]     - _version.py [511 bytes]     - _warnings.py [828 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pluggy-1.6.0.dist-info
    - INSTALLER [4 bytes]     - METADATA [4811 bytes]     - RECORD [1530 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto
    - __init__.py [1712 bytes]     - _file_info.py [7914 bytes]     - _package_info.py [1906 bytes]     - datetime_helpers.py [7371 bytes]     - enums.py [5961 bytes]     - fields.py [5372 bytes]     - message.py [38971 bytes]     - modules.py [1585 bytes]     - primitives.py [1000 bytes]     - utils.py [1651 bytes]     - version.py [599 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto_plus-1.26.1.dist-info
    - INSTALLER [4 bytes]     - LICENSE [11358 bytes]     - METADATA [2170 bytes]     - RECORD [4059 bytes]     - top_level.txt [6 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\protobuf-5.29.5.dist-info
    - INSTALLER [4 bytes]     - LICENSE [1732 bytes]     - METADATA [592 bytes]     - RECORD [9017 bytes]     - WHEEL [100 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\psutil
    - __init__.py [89075 bytes]     - _common.py [29592 bytes]     - _psaix.py [18817 bytes]     - _psbsd.py [32727 bytes]     - _pslinux.py [88323 bytes]     - _psosx.py [16421 bytes]     - _psposix.py [7349 bytes]     - _pssunos.py [25654 bytes]     - _psutil_windows.pyd [67072 bytes]     - _pswindows.py [37052 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\psutil-7.0.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [1577 bytes]     - METADATA [23136 bytes]     - RECORD [4447 bytes]     - REQUESTED [0 bytes]     - top_level.txt [7 bytes]     - WHEEL [100 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1
    - __init__.py [66 bytes]     - debug.py [3494 bytes]     - error.py [3258 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1_modules
    - __init__.py [65 bytes]     - pem.py [1821 bytes]     - rfc1155.py [2683 bytes]     - rfc1157.py [3554 bytes]     - rfc1901.py [646 bytes]     - rfc1902.py [3705 bytes]     - rfc1905.py [4831 bytes]     - rfc2251.py [26931 bytes]     - rfc2314.py [1313 bytes]     - rfc2315.py [9666 bytes]     - rfc2437.py [2623 bytes]     - rfc2459.py [50002 bytes]     - rfc2511.py [10350 bytes]     - rfc2560.py [8406 bytes]     - rfc2631.py [1219 bytes]     - rfc2634.py [9425 bytes]     - rfc2876.py [1438 bytes]     - rfc2985.py [14359 bytes]     - rfc2986.py [1896 bytes]     - rfc3058.py [992 bytes]     - rfc3114.py [1961 bytes]     - rfc3125.py [16707 bytes]     - rfc3161.py [4260 bytes]     - rfc3274.py [1670 bytes]     - rfc3279.py [6807 bytes]     - rfc3280.py [46620 bytes]     - rfc3281.py [9866 bytes]     - rfc3370.py [2811 bytes]     - rfc3412.py [1956 bytes]     - rfc3414.py [1167 bytes]     - rfc3447.py [1605 bytes]     - rfc3537.py [796 bytes]     - rfc3560.py [1818 bytes]     - rfc3565.py [1438 bytes]     - rfc3657.py [1746 bytes]     - rfc3709.py [6469 bytes]     - rfc3739.py [5122 bytes]     - rfc3770.py [1743 bytes]     - rfc3779.py [3260 bytes]     - rfc3820.py [1478 bytes]     - rfc3852.py [20101 bytes]     - rfc4010.py [1228 bytes]     - rfc4043.py [1067 bytes]     - rfc4055.py [10392 bytes]     - rfc4073.py [1636 bytes]     - rfc4108.py [10598 bytes]     - rfc4210.py [28469 bytes]     - rfc4211.py [12110 bytes]     - rfc4334.py [1586 bytes]     - rfc4357.py [15036 bytes]     - rfc4387.py [441 bytes]     - rfc4476.py [1960 bytes]     - rfc4490.py [3401 bytes]     - rfc4491.py [1054 bytes]     - rfc4683.py [1839 bytes]     - rfc4985.py [961 bytes]     - rfc5035.py [4523 bytes]     - rfc5083.py [1888 bytes]     - rfc5084.py [2855 bytes]     - rfc5126.py [15780 bytes]     - rfc5208.py [1432 bytes]     - rfc5275.py [11605 bytes]     - rfc5280.py [51236 bytes]     - rfc5480.py [4834 bytes]     - rfc5636.py [2324 bytes]     - rfc5639.py [1025 bytes]     - rfc5649.py [830 bytes]     - rfc5652.py [21451 bytes]     - rfc5697.py [1702 bytes]     - rfc5751.py [3198 bytes]     - rfc5752.py [1431 bytes]     - rfc5753.py [4534 bytes]     - rfc5755.py [12081 bytes]     - rfc5913.py [1161 bytes]     - rfc5914.py [3714 bytes]     - rfc5915.py [1056 bytes]     - rfc5916.py [800 bytes]     - rfc5917.py [1511 bytes]     - rfc5924.py [425 bytes]     - rfc5934.py [23798 bytes]     - rfc5940.py [1613 bytes]     - rfc5958.py [2650 bytes]     - rfc5990.py [5505 bytes]     - rfc6010.py [2347 bytes]     - rfc6019.py [1086 bytes]     - rfc6031.py [12137 bytes]     - rfc6032.py [1950 bytes]     - rfc6120.py [818 bytes]     - rfc6170.py [409 bytes]     - rfc6187.py [489 bytes]     - rfc6210.py [1052 bytes]     - rfc6211.py [2257 bytes]     - rfc6402.py [17148 bytes]     - rfc6482.py [2085 bytes]     - rfc6486.py [1916 bytes]     - rfc6487.py [472 bytes]     - rfc6664.py [4270 bytes]     - rfc6955.py [2814 bytes]     - rfc6960.py [7913 bytes]     - rfc7030.py [1441 bytes]     - rfc7191.py [7062 bytes]     - rfc7229.py [743 bytes]     - rfc7292.py [8478 bytes]     - rfc7296.py [885 bytes]     - rfc7508.py [2182 bytes]     - rfc7585.py [1076 bytes]     - rfc7633.py [841 bytes]     - rfc7773.py [1315 bytes]     - rfc7894.py [2769 bytes]     - rfc7906.py [18921 bytes]     - rfc7914.py [1493 bytes]     - rfc8017.py [4178 bytes]     - rfc8018.py [6166 bytes]     - rfc8103.py [1017 bytes]     - rfc8209.py [393 bytes]     - rfc8226.py [4291 bytes]     - rfc8358.py [1136 bytes]     - rfc8360.py [1075 bytes]     - rfc8398.py [1192 bytes]     - rfc8410.py [971 bytes]     - rfc8418.py [1109 bytes]     - rfc8419.py [1704 bytes]     - rfc8479.py [1142 bytes]     - rfc8494.py [2363 bytes]     - rfc8520.py [1496 bytes]     - rfc8619.py [1136 bytes]     - rfc8649.py [982 bytes]     - rfc8692.py [2098 bytes]     - rfc8696.py [3479 bytes]     - rfc8702.py [2739 bytes]     - rfc8708.py [978 bytes]     - rfc8769.py [441 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1_modules-0.4.2.dist-info
    - INSTALLER [4 bytes]     - METADATA [3484 bytes]     - RECORD [18705 bytes]     - top_level.txt [15 bytes]     - WHEEL [91 bytes]     - zip-safe [1 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1-0.6.1.dist-info
    - INSTALLER [4 bytes]     - LICENSE.rst [1334 bytes]     - METADATA [8383 bytes]     - RECORD [4857 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]     - zip-safe [1 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic
    - __init__.py [15395 bytes]     - _migration.py [11907 bytes]     - alias_generators.py [2124 bytes]     - aliases.py [4937 bytes]     - annotated_handlers.py [4407 bytes]     - class_validators.py [148 bytes]     - color.py [21481 bytes]     - config.py [42048 bytes]     - dataclasses.py [16215 bytes]     - datetime_parse.py [150 bytes]     - decorator.py [145 bytes]     - env_settings.py [148 bytes]     - error_wrappers.py [150 bytes]     - errors.py [6002 bytes]     - fields.py [64276 bytes]     - functional_serializers.py [17102 bytes]     - functional_validators.py [29560 bytes]     - generics.py [144 bytes]     - json_schema.py [115430 bytes]     - json.py [140 bytes]     - main.py [81012 bytes]     - mypy.py [59265 bytes]     - networks.py [41446 bytes]     - parse.py [141 bytes]     - py.typed [0 bytes]     - root_model.py [6279 bytes]     - schema.py [142 bytes]     - tools.py [141 bytes]     - type_adapter.py [31171 bytes]     - types.py [104781 bytes]     - typing.py [138 bytes]     - utils.py [141 bytes]     - validate_call_decorator.py [4389 bytes]     - validators.py [146 bytes]     - version.py [2710 bytes]     - warnings.py [3772 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic_core
    - __init__.py [4547 bytes]     - _pydantic_core.cp313-win_amd64.pyd [5309440 bytes]     - _pydantic_core.pyi [44398 bytes]     - core_schema.py [153980 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic_core-2.33.2.dist-info
    - INSTALLER [4 bytes]     - METADATA [6883 bytes]     - RECORD [989 bytes]     - WHEEL [96 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic-2.11.5.dist-info
    - INSTALLER [4 bytes]     - METADATA [67219 bytes]     - RECORD [15376 bytes]     - REQUESTED [0 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing
    - __init__.py [9038 bytes]     - actions.py [7265 bytes]     - common.py [13673 bytes]     - core.py [234772 bytes]     - exceptions.py [9892 bytes]     - helpers.py [40155 bytes]     - py.typed [0 bytes]     - results.py [26254 bytes]     - testing.py [14682 bytes]     - unicode.py [10614 bytes]     - util.py [14344 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing-3.2.3.dist-info
    - INSTALLER [4 bytes]     - LICENSE [1023 bytes]     - METADATA [5042 bytes]     - RECORD [2198 bytes]     - WHEEL [81 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pypiwin32-223.dist-info
    - DESCRIPTION.rst [10 bytes]     - INSTALLER [4 bytes]     - METADATA [236 bytes]     - metadata.json [321 bytes]     - RECORD [577 bytes]     - top_level.txt [1 bytes]     - WHEEL [97 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest
    - __init__.py [5169 bytes]     - __main__.py [154 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest_asyncio
    - __init__.py [236 bytes]     - _version.py [511 bytes]     - plugin.py [32098 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest_asyncio-1.0.0.dist-info
    - entry_points.txt [43 bytes]     - INSTALLER [4 bytes]     - METADATA [3956 bytes]     - RECORD [1219 bytes]     - REQUESTED [0 bytes]     - top_level.txt [15 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest-8.3.5.dist-info
    - AUTHORS [7052 bytes]     - entry_points.txt [77 bytes]     - INSTALLER [4 bytes]     - LICENSE [1091 bytes]     - METADATA [7593 bytes]     - RECORD [10264 bytes]     - REQUESTED [0 bytes]     - top_level.txt [18 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\python_dotenv-1.1.0.dist-info
    - entry_points.txt [47 bytes]     - INSTALLER [4 bytes]     - METADATA [24100 bytes]     - RECORD [1833 bytes]     - REQUESTED [0 bytes]     - top_level.txt [7 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\python_multipart
    - __init__.py [512 bytes]     - decoders.py [6669 bytes]     - exceptions.py [992 bytes]     - multipart.py [76427 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\python_multipart-0.0.20.dist-info
    - INSTALLER [4 bytes]     - METADATA [1817 bytes]     - RECORD [1718 bytes]     - REQUESTED [0 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin
    - dde.pyd [97792 bytes]     - license.txt [1540 bytes]     - mfc140u.dll [5653576 bytes]     - Pythonwin.exe [58368 bytes]     - scintilla.dll [1565184 bytes]     - start_pythonwin.pyw [589 bytes]     - win32ui.pyd [1044992 bytes]     - win32uiole.pyd [74240 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyttsx3
    - __init__.py [782 bytes]     - driver.py [6771 bytes]     - engine.py [7004 bytes]     - six.py [28688 bytes]     - voice.py [417 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyttsx3-2.98.dist-info
    - INSTALLER [4 bytes]     - LICENSE [16725 bytes]     - METADATA [3838 bytes]     - RECORD [1980 bytes]     - REQUESTED [0 bytes]     - top_level.txt [8 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pywin32_system32
    - pythoncom313.dll [680960 bytes]     - pywintypes313.dll [136192 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pywin32-310.dist-info
    - entry_points.txt [132 bytes]     - INSTALLER [4 bytes]     - METADATA [9362 bytes]     - RECORD [81484 bytes]     - top_level.txt [1335 bytes]     - WHEEL [101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\requests
    - __init__.py [5072 bytes]     - __version__.py [435 bytes]     - _internal_utils.py [1495 bytes]     - adapters.py [27451 bytes]     - api.py [6449 bytes]     - auth.py [10186 bytes]     - certs.py [429 bytes]     - compat.py [1817 bytes]     - cookies.py [18590 bytes]     - exceptions.py [4260 bytes]     - help.py [3875 bytes]     - hooks.py [733 bytes]     - models.py [35418 bytes]     - packages.py [904 bytes]     - sessions.py [30495 bytes]     - status_codes.py [4322 bytes]     - structures.py [2912 bytes]     - utils.py [33619 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\requests-2.32.3.dist-info
    - INSTALLER [4 bytes]     - LICENSE [10142 bytes]     - METADATA [4610 bytes]     - RECORD [2851 bytes]     - REQUESTED [0 bytes]     - top_level.txt [9 bytes]     - WHEEL [92 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\rsa
    - __init__.py [1607 bytes]     - asn1.py [1740 bytes]     - cli.py [9862 bytes]     - common.py [4679 bytes]     - core.py [1661 bytes]     - key.py [27427 bytes]     - parallel.py [2309 bytes]     - pem.py [3989 bytes]     - pkcs1_v2.py [3449 bytes]     - pkcs1.py [16205 bytes]     - prime.py [5106 bytes]     - py.typed [63 bytes]     - randnum.py [2657 bytes]     - transform.py [2200 bytes]     - util.py [2993 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\rsa-4.9.1.dist-info
    - entry_points.txt [201 bytes]     - INSTALLER [4 bytes]     - LICENSE [577 bytes]     - METADATA [5590 bytes]     - RECORD [2638 bytes]     - WHEEL [88 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sniffio
    - __init__.py [335 bytes]     - _impl.py [2843 bytes]     - _version.py [89 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sniffio-1.3.1.dist-info
    - INSTALLER [4 bytes]     - LICENSE [185 bytes]     - LICENSE.APACHE2 [11358 bytes]     - LICENSE.MIT [1046 bytes]     - METADATA [3875 bytes]     - RECORD [1388 bytes]     - top_level.txt [8 bytes]     - WHEEL [92 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition
    - __init__.py [77324 bytes]     - __main__.py [833 bytes]     - audio.py [15155 bytes]     - exceptions.py [273 bytes]     - flac-linux-x86 [1899154 bytes]     - flac-linux-x86_64 [2396644 bytes]     - flac-mac [451168 bytes]     - flac-win32.exe [738816 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speechrecognition-3.14.3.dist-info
    - INSTALLER [4 bytes]     - METADATA [30010 bytes]     - RECORD [5885 bytes]     - REQUESTED [0 bytes]     - top_level.txt [25 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy
    - __init__.py [12942 bytes]     - events.py [542 bytes]     - exc.py [24810 bytes]     - inspection.py [5237 bytes]     - log.py [8895 bytes]     - py.typed [0 bytes]     - schema.py [3324 bytes]     - types.py [3244 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy-2.0.41.dist-info
    - INSTALLER [4 bytes]     - METADATA [9820 bytes]     - RECORD [40482 bytes]     - REQUESTED [0 bytes]     - top_level.txt [11 bytes]     - WHEEL [101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\standard_aifc-3.13.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [2569 bytes]     - METADATA [969 bytes]     - RECORD [639 bytes]     - top_level.txt [5 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\standard_chunk-3.13.0.dist-info
    - INSTALLER [4 bytes]     - LICENSE [2569 bytes]     - METADATA [860 bytes]     - RECORD [646 bytes]     - top_level.txt [6 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\starlette
    - __init__.py [23 bytes]     - _exception_handler.py [2219 bytes]     - _utils.py [2764 bytes]     - applications.py [10678 bytes]     - authentication.py [4948 bytes]     - background.py [1257 bytes]     - concurrency.py [1746 bytes]     - config.py [4445 bytes]     - convertors.py [2302 bytes]     - datastructures.py [22262 bytes]     - endpoints.py [5098 bytes]     - exceptions.py [1066 bytes]     - formparsers.py [11045 bytes]     - py.typed [0 bytes]     - requests.py [11693 bytes]     - responses.py [20226 bytes]     - routing.py [34550 bytes]     - schemas.py [5181 bytes]     - staticfiles.py [8474 bytes]     - status.py [2820 bytes]     - templating.py [8408 bytes]     - testclient.py [27987 bytes]     - types.py [1048 bytes]     - websockets.py [8332 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\starlette-0.46.2.dist-info
    - INSTALLER [4 bytes]     - METADATA [6167 bytes]     - RECORD [5190 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tests
    - __init__.py [133 bytes]     - test_audio.py [8784 bytes]     - test_recognition.py [4932 bytes]     - test_special_features.py [1502 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tqdm
    - __init__.py [1572 bytes]     - __main__.py [30 bytes]     - _dist_ver.py [23 bytes]     - _main.py [283 bytes]     - _monitor.py [3699 bytes]     - _tqdm_gui.py [287 bytes]     - _tqdm_notebook.py [307 bytes]     - _tqdm_pandas.py [888 bytes]     - _tqdm.py [283 bytes]     - _utils.py [553 bytes]     - asyncio.py [2757 bytes]     - auto.py [871 bytes]     - autonotebook.py [956 bytes]     - cli.py [11010 bytes]     - completion.sh [946 bytes]     - dask.py [1319 bytes]     - gui.py [5479 bytes]     - keras.py [4373 bytes]     - notebook.py [10895 bytes]     - rich.py [5021 bytes]     - std.py [57461 bytes]     - tk.py [6701 bytes]     - tqdm.1 [7889 bytes]     - utils.py [11821 bytes]     - version.py [333 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tqdm-4.67.1.dist-info
    - entry_points.txt [39 bytes]     - INSTALLER [4 bytes]     - LICENCE [1985 bytes]     - METADATA [57675 bytes]     - RECORD [4660 bytes]     - top_level.txt [5 bytes]     - WHEEL [91 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\typing_extensions-4.13.2.dist-info
    - INSTALLER [4 bytes]     - METADATA [2994 bytes]     - RECORD [580 bytes]     - WHEEL [82 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\typing_inspection
    - __init__.py [0 bytes]     - introspection.py [22534 bytes]     - py.typed [0 bytes]     - typing_objects.py [16912 bytes]     - typing_objects.pyi [9179 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\typing_inspection-0.4.1.dist-info
    - INSTALLER [4 bytes]     - METADATA [2552 bytes]     - RECORD [1076 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uritemplate
    - __init__.py [816 bytes]     - api.py [2305 bytes]     - orderedset.py [3367 bytes]     - py.typed [0 bytes]     - template.py [4774 bytes]     - variable.py [12870 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uritemplate-4.1.1.dist-info
    - INSTALLER [4 bytes]     - LICENSE [196 bytes]     - METADATA [2882 bytes]     - RECORD [1237 bytes]     - top_level.txt [12 bytes]     - WHEEL [110 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3
    - __init__.py [6979 bytes]     - _base_connection.py [5568 bytes]     - _collections.py [17295 bytes]     - _request_methods.py [9931 bytes]     - _version.py [511 bytes]     - connection.py [39875 bytes]     - connectionpool.py [43371 bytes]     - exceptions.py [9938 bytes]     - fields.py [10829 bytes]     - filepost.py [2388 bytes]     - poolmanager.py [22913 bytes]     - py.typed [93 bytes]     - response.py [45190 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3-2.4.0.dist-info
    - INSTALLER [4 bytes]     - METADATA [6461 bytes]     - RECORD [5527 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn
    - __init__.py [147 bytes]     - __main__.py [62 bytes]     - _subprocess.py [2766 bytes]     - _types.py [7775 bytes]     - config.py [20894 bytes]     - importer.py [1128 bytes]     - logging.py [4235 bytes]     - main.py [17234 bytes]     - py.typed [1 bytes]     - server.py [12879 bytes]     - workers.py [3895 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn-0.34.2.dist-info
    - entry_points.txt [46 bytes]     - INSTALLER [4 bytes]     - METADATA [6549 bytes]     - RECORD [6169 bytes]     - REQUESTED [0 bytes]     - WHEEL [87 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32
    - _win32sysloader.pyd [14848 bytes]     - _winxptheme.pyd [25600 bytes]     - license.txt [1540 bytes]     - mmapfile.pyd [22016 bytes]     - odbc.pyd [41984 bytes]     - perfmon.pyd [30208 bytes]     - perfmondata.dll [19456 bytes]     - pythonservice.exe [20992 bytes]     - servicemanager.pyd [41472 bytes]     - timer.pyd [16896 bytes]     - win32api.pyd [133632 bytes]     - win32clipboard.pyd [28160 bytes]     - win32console.pyd [60416 bytes]     - win32cred.pyd [38912 bytes]     - win32crypt.pyd [125440 bytes]     - win32event.pyd [28672 bytes]     - win32evtlog.pyd [73728 bytes]     - win32file.pyd [144896 bytes]     - win32gui.pyd [221184 bytes]     - win32help.pyd [54784 bytes]     - win32inet.pyd [53248 bytes]     - win32job.pyd [28672 bytes]     - win32lz.pyd [16896 bytes]     - win32net.pyd [92672 bytes]     - win32pdh.pyd [35328 bytes]     - win32pipe.pyd [28672 bytes]     - win32print.pyd [76288 bytes]     - win32process.pyd [53760 bytes]     - win32profile.pyd [27136 bytes]     - win32ras.pyd [34304 bytes]     - win32security.pyd [137728 bytes]     - win32service.pyd [59392 bytes]     - win32trace.pyd [24064 bytes]     - win32transaction.pyd [19456 bytes]     - win32ts.pyd [33280 bytes]     - win32wnet.pyd [37888 bytes]     - winxpgui.py [409 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com
    - __init__.py [5020 bytes]     - License.txt [1569 bytes]     - olectl.py [2692 bytes]     - readme.html [3766 bytes]     - storagecon.py [3174 bytes]     - universal.py [8761 bytes]     - util.py [1064 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\adodbapi\__pycache__
    - __init__.cpython-313.pyc [3231 bytes]     - ado_consts.cpython-313.pyc [6636 bytes]     - adodbapi.cpython-313.pyc [50461 bytes]     - apibase.cpython-313.pyc [28759 bytes]     - is64bit.cpython-313.pyc [1356 bytes]     - process_connect_string.cpython-313.pyc [4650 bytes]     - schema_table.cpython-313.pyc [915 bytes]     - setup.cpython-313.pyc [2567 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\adodbapi\examples
    - db_print.py [2288 bytes]     - db_table_names.py [526 bytes]     - xls_read.py [1131 bytes]     - xls_write.py [1463 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\adodbapi\test
    - adodbapitest.py [56194 bytes]     - adodbapitestconfig.py [6517 bytes]     - dbapi20.py [33349 bytes]     - is64bit.py [1013 bytes]     - setuptestframework.py [3017 bytes]     - test_adodbapi_dbapi20.py [5948 bytes]     - tryconnection.py [1027 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\adodbapi\examples\__pycache__
    - db_print.cpython-313.pyc [2812 bytes]     - db_table_names.cpython-313.pyc [870 bytes]     - xls_read.cpython-313.pyc [1614 bytes]     - xls_write.cpython-313.pyc [1855 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\adodbapi\test\__pycache__
    - adodbapitest.cpython-313.pyc [72455 bytes]     - adodbapitestconfig.cpython-313.pyc [5661 bytes]     - dbapi20.cpython-313.pyc [39431 bytes]     - is64bit.cpython-313.pyc [1334 bytes]     - setuptestframework.cpython-313.pyc [4527 bytes]     - test_adodbapi_dbapi20.cpython-313.pyc [7995 bytes]     - tryconnection.cpython-313.pyc [1471 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\aifc\__pycache__
    - __init__.cpython-313.pyc [44037 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\__pycache__
    - __init__.cpython-313.pyc [252 bytes]     - __main__.cpython-313.pyc [283 bytes]     - command.cpython-313.pyc [29478 bytes]     - config.cpython-313.pyc [36499 bytes]     - context.cpython-313.pyc [349 bytes]     - environment.cpython-313.pyc [205 bytes]     - migration.cpython-313.pyc [201 bytes]     - op.cpython-313.pyc [332 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\autogenerate
    - __init__.py [543 bytes]     - api.py [22219 bytes]     - compare.py [46021 bytes]     - render.py [36489 bytes]     - rewriter.py [7846 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\ddl
    - __init__.py [152 bytes]     - _autogen.py [9275 bytes]     - base.py [10478 bytes]     - impl.py [30439 bytes]     - mssql.py [14216 bytes]     - mysql.py [17358 bytes]     - oracle.py [6243 bytes]     - postgresql.py [29950 bytes]     - sqlite.py [8006 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\operations
    - __init__.py [318 bytes]     - base.py [75157 bytes]     - batch.py [26923 bytes]     - ops.py [96276 bytes]     - schemaobj.py [9468 bytes]     - toimpl.py [7503 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\runtime
    - __init__.py [0 bytes]     - environment.py [41524 bytes]     - migration.py [49874 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\script
    - __init__.py [100 bytes]     - base.py [36873 bytes]     - revision.py [62307 bytes]     - write_hooks.py [5015 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\testing
    - __init__.py [1249 bytes]     - assertions.py [5302 bytes]     - env.py [11551 bytes]     - fixtures.py [9920 bytes]     - requirements.py [4180 bytes]     - schemacompare.py [4838 bytes]     - util.py [3350 bytes]     - warnings.py [831 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\util
    - __init__.py [1519 bytes]     - compat.py [4248 bytes]     - editor.py [2546 bytes]     - exc.py [564 bytes]     - langhelpers.py [10026 bytes]     - messaging.py [3294 bytes]     - pyfiles.py [4609 bytes]     - sqla_compat.py [14785 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\autogenerate\__pycache__
    - __init__.cpython-313.pyc [600 bytes]     - api.cpython-313.pyc [22029 bytes]     - compare.cpython-313.pyc [48875 bytes]     - render.cpython-313.pyc [47946 bytes]     - rewriter.cpython-313.pyc [9254 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\ddl\__pycache__
    - __init__.cpython-313.pyc [362 bytes]     - _autogen.cpython-313.pyc [15580 bytes]     - base.cpython-313.pyc [16479 bytes]     - impl.cpython-313.pyc [35623 bytes]     - mssql.cpython-313.pyc [16399 bytes]     - mysql.cpython-313.pyc [16429 bytes]     - oracle.cpython-313.pyc [8604 bytes]     - postgresql.cpython-313.pyc [33955 bytes]     - sqlite.cpython-313.pyc [8250 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\operations\__pycache__
    - __init__.cpython-313.pyc [462 bytes]     - base.cpython-313.pyc [68160 bytes]     - batch.cpython-313.pyc [31987 bytes]     - ops.cpython-313.pyc [106377 bytes]     - schemaobj.cpython-313.pyc [12027 bytes]     - toimpl.cpython-313.pyc [11641 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\runtime\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - environment.cpython-313.pyc [39597 bytes]     - migration.cpython-313.pyc [59242 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\script\__pycache__
    - __init__.cpython-313.pyc [282 bytes]     - base.cpython-313.pyc [43193 bytes]     - revision.cpython-313.pyc [62933 bytes]     - write_hooks.cpython-313.pyc [6399 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\async
    - alembic.ini.mako [4684 bytes]     - env.py [2389 bytes]     - README [58 bytes]     - script.py.mako [689 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\generic
    - alembic.ini.mako [4684 bytes]     - env.py [2103 bytes]     - README [38 bytes]     - script.py.mako [689 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\multidb
    - alembic.ini.mako [5010 bytes]     - env.py [4230 bytes]     - README [606 bytes]     - script.py.mako [1220 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\pyproject
    - alembic.ini.mako [782 bytes]     - env.py [2103 bytes]     - pyproject.toml.mako [2645 bytes]     - README [60 bytes]     - script.py.mako [689 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\async\__pycache__
    - env.cpython-313.pyc [3325 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\generic\__pycache__
    - env.cpython-313.pyc [2533 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\multidb\__pycache__
    - env.cpython-313.pyc [4770 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\templates\pyproject\__pycache__
    - env.cpython-313.pyc [2535 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\testing\__pycache__
    - __init__.cpython-313.pyc [1363 bytes]     - assertions.cpython-313.pyc [7482 bytes]     - env.cpython-313.pyc [15902 bytes]     - fixtures.cpython-313.pyc [16798 bytes]     - requirements.cpython-313.pyc [9642 bytes]     - schemacompare.cpython-313.pyc [9530 bytes]     - util.cpython-313.pyc [5022 bytes]     - warnings.cpython-313.pyc [1052 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\testing\plugin
    - __init__.py [0 bytes]     - bootstrap.py [50 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\testing\suite
    - __init__.py [288 bytes]     - _autogen_fixtures.py [13480 bytes]     - test_autogen_comments.py [6283 bytes]     - test_autogen_computed.py [4126 bytes]     - test_autogen_diffs.py [8394 bytes]     - test_autogen_fks.py [32927 bytes]     - test_autogen_identity.py [5824 bytes]     - test_environment.py [11877 bytes]     - test_op.py [1343 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\testing\plugin\__pycache__
    - __init__.cpython-313.pyc [176 bytes]     - bootstrap.cpython-313.pyc [237 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\testing\suite\__pycache__
    - __init__.cpython-313.pyc [407 bytes]     - _autogen_fixtures.cpython-313.pyc [16604 bytes]     - test_autogen_comments.cpython-313.pyc [6525 bytes]     - test_autogen_computed.cpython-313.pyc [7247 bytes]     - test_autogen_diffs.cpython-313.pyc [12097 bytes]     - test_autogen_fks.cpython-313.pyc [34082 bytes]     - test_autogen_identity.cpython-313.pyc [9614 bytes]     - test_environment.cpython-313.pyc [18871 bytes]     - test_op.cpython-313.pyc [2914 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic\util\__pycache__
    - __init__.cpython-313.pyc [1274 bytes]     - compat.cpython-313.pyc [5676 bytes]     - editor.cpython-313.pyc [3230 bytes]     - exc.cpython-313.pyc [1317 bytes]     - langhelpers.cpython-313.pyc [14078 bytes]     - messaging.cpython-313.pyc [5250 bytes]     - pyfiles.cpython-313.pyc [6418 bytes]     - sqla_compat.cpython-313.pyc [20891 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\alembic-1.16.1.dist-info\licenses
    - LICENSE [1059 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\annotated_types\__pycache__
    - __init__.cpython-313.pyc [18895 bytes]     - test_cases.cpython-313.pyc [13263 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\annotated_types-0.7.0.dist-info\licenses
    - LICENSE [1083 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\__pycache__
    - __init__.cpython-313.pyc [3576 bytes]     - from_thread.cpython-313.pyc [23736 bytes]     - lowlevel.cpython-313.pyc [6983 bytes]     - pytest_plugin.cpython-313.pyc [13707 bytes]     - to_interpreter.cpython-313.pyc [9175 bytes]     - to_process.cpython-313.pyc [11951 bytes]     - to_thread.cpython-313.pyc [2825 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\_backends
    - __init__.py [0 bytes]     - _asyncio.py [93455 bytes]     - _trio.py [40429 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\_core
    - __init__.py [0 bytes]     - _asyncio_selector_thread.py [5626 bytes]     - _eventloop.py [4695 bytes]     - _exceptions.py [3503 bytes]     - _fileio.py [23340 bytes]     - _resources.py [435 bytes]     - _signals.py [905 bytes]     - _sockets.py [27150 bytes]     - _streams.py [1804 bytes]     - _subprocesses.py [8047 bytes]     - _synchronization.py [20320 bytes]     - _tasks.py [4757 bytes]     - _tempfile.py [19696 bytes]     - _testing.py [2118 bytes]     - _typedattr.py [2508 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\abc
    - __init__.py [2652 bytes]     - _eventloop.py [9682 bytes]     - _resources.py [783 bytes]     - _sockets.py [6262 bytes]     - _streams.py [6598 bytes]     - _subprocesses.py [2067 bytes]     - _tasks.py [3080 bytes]     - _testing.py [1821 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\streams
    - __init__.py [0 bytes]     - buffered.py [4500 bytes]     - file.py [4383 bytes]     - memory.py [10560 bytes]     - stapled.py [4302 bytes]     - text.py [5094 bytes]     - tls.py [13199 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\abc\__pycache__
    - __init__.cpython-313.pyc [2219 bytes]     - _eventloop.cpython-313.pyc [14730 bytes]     - _resources.cpython-313.pyc [1646 bytes]     - _sockets.cpython-313.pyc [9753 bytes]     - _streams.cpython-313.pyc [8321 bytes]     - _subprocesses.cpython-313.pyc [3135 bytes]     - _tasks.cpython-313.pyc [4393 bytes]     - _testing.cpython-313.pyc [2750 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\streams\__pycache__
    - __init__.cpython-313.pyc [167 bytes]     - buffered.cpython-313.pyc [6071 bytes]     - file.cpython-313.pyc [7419 bytes]     - memory.cpython-313.pyc [14839 bytes]     - stapled.cpython-313.pyc [7538 bytes]     - text.cpython-313.pyc [8231 bytes]     - tls.cpython-313.pyc [17383 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\_backends\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - _asyncio.cpython-313.pyc [136470 bytes]     - _trio.cpython-313.pyc [71883 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\anyio\_core\__pycache__
    - __init__.cpython-313.pyc [165 bytes]     - _asyncio_selector_thread.cpython-313.pyc [8613 bytes]     - _eventloop.cpython-313.pyc [6276 bytes]     - _exceptions.cpython-313.pyc [6248 bytes]     - _fileio.cpython-313.pyc [41493 bytes]     - _resources.cpython-313.pyc [901 bytes]     - _signals.cpython-313.pyc [1213 bytes]     - _sockets.cpython-313.pyc [30964 bytes]     - _streams.cpython-313.pyc [2352 bytes]     - _subprocesses.cpython-313.pyc [9424 bytes]     - _synchronization.cpython-313.pyc [32194 bytes]     - _tasks.cpython-313.pyc [6849 bytes]     - _tempfile.cpython-313.pyc [27881 bytes]     - _testing.cpython-313.pyc [3573 bytes]     - _typedattr.cpython-313.pyc [3755 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\apiclient\__pycache__
    - __init__.cpython-313.pyc [838 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\audioop\__pycache__
    - __init__.cpython-313.pyc [710 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\cachetools\__pycache__
    - __init__.cpython-313.pyc [37361 bytes]     - _decorators.cpython-313.pyc [6619 bytes]     - func.cpython-313.pyc [5423 bytes]     - keys.cpython-313.pyc [3237 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\certifi\__pycache__
    - __init__.cpython-313.pyc [288 bytes]     - __main__.cpython-313.pyc [605 bytes]     - core.cpython-313.pyc [3180 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\certifi-2025.4.26.dist-info\licenses
    - LICENSE [989 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\charset_normalizer\__pycache__
    - __init__.cpython-313.pyc [1749 bytes]     - __main__.cpython-313.pyc [331 bytes]     - api.cpython-313.pyc [18688 bytes]     - cd.cpython-313.pyc [13370 bytes]     - constant.cpython-313.pyc [40794 bytes]     - legacy.cpython-313.pyc [2856 bytes]     - md.cpython-313.pyc [25411 bytes]     - models.cpython-313.pyc [17275 bytes]     - utils.cpython-313.pyc [14030 bytes]     - version.cpython-313.pyc [356 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\charset_normalizer\cli
    - __init__.py [144 bytes]     - __main__.py [13027 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\charset_normalizer\cli\__pycache__
    - __init__.cpython-313.pyc [317 bytes]     - __main__.cpython-313.pyc [14494 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\charset_normalizer-3.4.2.dist-info\licenses
    - LICENSE [1092 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\chunk\__pycache__
    - __init__.cpython-313.pyc [7723 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\click\__pycache__
    - __init__.cpython-313.pyc [4031 bytes]     - _compat.cpython-313.pyc [24731 bytes]     - _termui_impl.cpython-313.pyc [32459 bytes]     - _textwrap.cpython-313.pyc [2450 bytes]     - _winconsole.cpython-313.pyc [12048 bytes]     - core.cpython-313.pyc [126388 bytes]     - decorators.cpython-313.pyc [21970 bytes]     - exceptions.cpython-313.pyc [15053 bytes]     - formatting.cpython-313.pyc [13535 bytes]     - globals.cpython-313.pyc [2928 bytes]     - parser.cpython-313.pyc [20412 bytes]     - shell_completion.cpython-313.pyc [23067 bytes]     - termui.cpython-313.pyc [33754 bytes]     - testing.cpython-313.pyc [26696 bytes]     - types.cpython-313.pyc [49095 bytes]     - utils.cpython-313.pyc [24717 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\click-8.2.1.dist-info\licenses
    - LICENSE.txt [1475 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\colorama\__pycache__
    - __init__.cpython-313.pyc [462 bytes]     - ansi.cpython-313.pyc [4100 bytes]     - ansitowin32.cpython-313.pyc [16566 bytes]     - initialise.cpython-313.pyc [3596 bytes]     - win32.cpython-313.pyc [8196 bytes]     - winterm.cpython-313.pyc [9306 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\colorama\tests
    - __init__.py [75 bytes]     - ansi_test.py [2839 bytes]     - ansitowin32_test.py [10678 bytes]     - initialise_test.py [6741 bytes]     - isatty_test.py [1866 bytes]     - utils.py [1079 bytes]     - winterm_test.py [3709 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\colorama\tests\__pycache__
    - __init__.cpython-313.pyc [168 bytes]     - ansi_test.cpython-313.pyc [5497 bytes]     - ansitowin32_test.cpython-313.pyc [17790 bytes]     - initialise_test.cpython-313.pyc [11766 bytes]     - isatty_test.cpython-313.pyc [4921 bytes]     - utils.cpython-313.pyc [2532 bytes]     - winterm_test.cpython-313.pyc [6622 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\colorama-0.4.6.dist-info\licenses
    - LICENSE.txt [1491 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\__pycache__
    - __init__.cpython-313.pyc [6767 bytes]     - _comobject.cpython-313.pyc [20595 bytes]     - _memberspec.cpython-313.pyc [30722 bytes]     - _meta.cpython-313.pyc [2313 bytes]     - _npsupport.cpython-313.pyc [5277 bytes]     - _safearray.cpython-313.pyc [4652 bytes]     - _tlib_version_checker.cpython-313.pyc [1157 bytes]     - _vtbl.cpython-313.pyc [18853 bytes]     - automation.cpython-313.pyc [37142 bytes]     - clear_cache.cpython-313.pyc [2397 bytes]     - connectionpoints.cpython-313.pyc [6890 bytes]     - errorinfo.cpython-313.pyc [8266 bytes]     - git.cpython-313.pyc [3386 bytes]     - GUID.cpython-313.pyc [5208 bytes]     - hresult.cpython-313.pyc [2450 bytes]     - logutil.cpython-313.pyc [3118 bytes]     - messageloop.cpython-313.pyc [3119 bytes]     - patcher.cpython-313.pyc [2697 bytes]     - persist.cpython-313.pyc [9302 bytes]     - safearray.cpython-313.pyc [19940 bytes]     - shelllink.cpython-313.pyc [13811 bytes]     - stream.cpython-313.pyc [2274 bytes]     - typeinfo.cpython-313.pyc [47366 bytes]     - util.cpython-313.pyc [3359 bytes]     - viewobject.cpython-313.pyc [7158 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\_post_coinit
    - __init__.py [727 bytes]     - _cominterface_meta_patcher.py [5883 bytes]     - bstr.py [1288 bytes]     - instancemethod.py [369 bytes]     - misc.py [11682 bytes]     - unknwn.py [17435 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\client
    - __init__.py [1223 bytes]     - _activeobj.py [1679 bytes]     - _code_cache.py [5405 bytes]     - _constants.py [4577 bytes]     - _create.py [5644 bytes]     - _events.py [13883 bytes]     - _generate.py [11941 bytes]     - _managing.py [4266 bytes]     - dynamic.py [6030 bytes]     - lazybind.py [8924 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\server
    - __init__.py [3156 bytes]     - automation.py [2885 bytes]     - connectionpoints.py [6838 bytes]     - inprocserver.py [5026 bytes]     - localserver.py [3218 bytes]     - register.py [17241 bytes]     - w_getopt.py [1571 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\test
    - __init__.py [8162 bytes]     - find_memleak.py [2157 bytes]     - mylib.idl [1664 bytes]     - mylib.tlb [3080 bytes]     - mytypelib.idl [2590 bytes]     - runtests.py [144 bytes]     - setup.py [172 bytes]     - test_agilent.py [4399 bytes]     - test_avmc.py [1368 bytes]     - test_basic.py [4695 bytes]     - test_BSTR.py [1208 bytes]     - test_casesensitivity.py [1366 bytes]     - test_clear_cache.py [818 bytes]     - test_client_dynamic.py [2905 bytes]     - test_client_regenerate_modules.py [7515 bytes]     - test_client.py [13295 bytes]     - test_collections.py [5481 bytes]     - test_comobject.py [5779 bytes]     - test_comserver.py [12376 bytes]     - test_createwrappers.py [4098 bytes]     - test_dict.py [3290 bytes]     - test_dispifc_records.py [4277 bytes]     - test_dispifc_safearrays.py [3973 bytes]     - test_dispinterface.py [5109 bytes]     - test_DISPPARAMS.py [1156 bytes]     - test_dyndispatch.py [3143 bytes]     - test_errorinfo.py [4763 bytes]     - test_excel.py [5083 bytes]     - test_findgendir.py [3032 bytes]     - test_getactiveobj.py [1997 bytes]     - test_GUID.py [1720 bytes]     - test_hresult.py [1771 bytes]     - test_ie.py [3839 bytes]     - test_ienum.py [887 bytes]     - test_imfattributes.py [1411 bytes]     - test_inout_args.py [21040 bytes]     - test_jscript.js [404 bytes]     - test_midl_safearray_create.py [4141 bytes]     - test_monikers.py [3218 bytes]     - test_msscript.py [2879 bytes]     - test_npsupport.py [12209 bytes]     - test_outparam.py [3031 bytes]     - test_persist.py [1270 bytes]     - test_pump_events.py [379 bytes]     - test_QueryService.py [836 bytes]     - test_recordinfo.py [2723 bytes]     - test_safearray.py [6935 bytes]     - test_sapi.py [1162 bytes]     - test_server_register.py [20039 bytes]     - test_server.py [10765 bytes]     - test_shelllink.py [5113 bytes]     - test_showevents.py [1134 bytes]     - test_storage.py [5572 bytes]     - test_stream.py [4975 bytes]     - test_subinterface.py [383 bytes]     - test_typeannotator.py [6674 bytes]     - test_typeinfo.py [4691 bytes]     - test_urlhistory.py [1854 bytes]     - test_variant.py [10260 bytes]     - test_w_getopt.py [1014 bytes]     - test_win32com_interop.py [5853 bytes]     - test_wmi.py [2314 bytes]     - test_word.py [2372 bytes]     - TestComServer.idl [2487 bytes]     - TestComServer.py [5161 bytes]     - TestComServer.tlb [3560 bytes]     - TestDispServer.idl [1835 bytes]     - TestDispServer.py [3776 bytes]     - TestDispServer.tlb [2992 bytes]     - urlhist.tlb [6480 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\tools
    - __init__.py [30 bytes]     - tlbparser.py [30808 bytes]     - typedesc_base.py [6609 bytes]     - typedesc.py [6927 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\client\__pycache__
    - __init__.cpython-313.pyc [1381 bytes]     - _activeobj.cpython-313.pyc [2255 bytes]     - _code_cache.cpython-313.pyc [7187 bytes]     - _constants.cpython-313.pyc [6963 bytes]     - _create.cpython-313.pyc [6213 bytes]     - _events.cpython-313.pyc [15987 bytes]     - _generate.cpython-313.pyc [15443 bytes]     - _managing.cpython-313.pyc [5120 bytes]     - dynamic.cpython-313.pyc [9259 bytes]     - lazybind.cpython-313.pyc [11141 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\server\__pycache__
    - __init__.cpython-313.pyc [3879 bytes]     - automation.cpython-313.pyc [4261 bytes]     - connectionpoints.cpython-313.pyc [9248 bytes]     - inprocserver.cpython-313.pyc [7529 bytes]     - localserver.cpython-313.pyc [5343 bytes]     - register.cpython-313.pyc [22598 bytes]     - w_getopt.cpython-313.pyc [2095 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\test\__pycache__
    - __init__.cpython-313.pyc [12490 bytes]     - find_memleak.cpython-313.pyc [2961 bytes]     - runtests.cpython-313.pyc [593 bytes]     - setup.cpython-313.pyc [348 bytes]     - test_agilent.cpython-313.pyc [3368 bytes]     - test_avmc.cpython-313.pyc [2443 bytes]     - test_basic.cpython-313.pyc [7996 bytes]     - test_BSTR.cpython-313.pyc [3067 bytes]     - test_casesensitivity.cpython-313.pyc [1712 bytes]     - test_clear_cache.cpython-313.pyc [1526 bytes]     - test_client_dynamic.cpython-313.pyc [5722 bytes]     - test_client_regenerate_modules.cpython-313.pyc [11513 bytes]     - test_client.cpython-313.pyc [21424 bytes]     - test_collections.cpython-313.pyc [9571 bytes]     - test_comobject.cpython-313.pyc [12505 bytes]     - test_comserver.cpython-313.pyc [20275 bytes]     - test_createwrappers.cpython-313.pyc [4779 bytes]     - test_dict.cpython-313.pyc [4731 bytes]     - test_dispifc_records.cpython-313.pyc [4681 bytes]     - test_dispifc_safearrays.cpython-313.pyc [5152 bytes]     - test_dispinterface.cpython-313.pyc [8635 bytes]     - test_DISPPARAMS.cpython-313.pyc [2139 bytes]     - test_dyndispatch.cpython-313.pyc [5885 bytes]     - test_errorinfo.cpython-313.pyc [8519 bytes]     - test_excel.cpython-313.pyc [7391 bytes]     - test_findgendir.cpython-313.pyc [5546 bytes]     - test_getactiveobj.cpython-313.pyc [3178 bytes]     - test_GUID.cpython-313.pyc [3487 bytes]     - test_hresult.cpython-313.pyc [2527 bytes]     - test_ie.cpython-313.pyc [6315 bytes]     - test_ienum.cpython-313.pyc [1755 bytes]     - test_imfattributes.cpython-313.pyc [2149 bytes]     - test_inout_args.cpython-313.pyc [20723 bytes]     - test_midl_safearray_create.cpython-313.pyc [6543 bytes]     - test_monikers.cpython-313.pyc [5599 bytes]     - test_msscript.cpython-313.pyc [2436 bytes]     - test_npsupport.cpython-313.pyc [20032 bytes]     - test_outparam.cpython-313.pyc [3774 bytes]     - test_persist.cpython-313.pyc [3260 bytes]     - test_pump_events.cpython-313.pyc [1114 bytes]     - test_QueryService.cpython-313.pyc [2009 bytes]     - test_recordinfo.cpython-313.pyc [5131 bytes]     - test_safearray.cpython-313.pyc [11681 bytes]     - test_sapi.cpython-313.pyc [1924 bytes]     - test_server_register.cpython-313.pyc [31782 bytes]     - test_server.cpython-313.pyc [781 bytes]     - test_shelllink.cpython-313.pyc [10294 bytes]     - test_showevents.cpython-313.pyc [1866 bytes]     - test_storage.cpython-313.pyc [9520 bytes]     - test_stream.cpython-313.pyc [9119 bytes]     - test_subinterface.cpython-313.pyc [1454 bytes]     - test_typeannotator.cpython-313.pyc [7747 bytes]     - test_typeinfo.cpython-313.pyc [8411 bytes]     - test_urlhistory.cpython-313.pyc [3143 bytes]     - test_variant.cpython-313.pyc [16425 bytes]     - test_w_getopt.cpython-313.pyc [2160 bytes]     - test_win32com_interop.cpython-313.pyc [6745 bytes]     - test_wmi.cpython-313.pyc [2530 bytes]     - test_word.cpython-313.pyc [4154 bytes]     - TestComServer.cpython-313.pyc [4773 bytes]     - TestDispServer.cpython-313.pyc [4285 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\tools\__pycache__
    - __init__.cpython-313.pyc [168 bytes]     - tlbparser.cpython-313.pyc [36783 bytes]     - typedesc_base.cpython-313.pyc [13071 bytes]     - typedesc.cpython-313.pyc [11777 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\tools\codegenerator
    - __init__.py [207 bytes]     - codegenerator.py [30228 bytes]     - comments.py [3623 bytes]     - heads.py [8427 bytes]     - helpers.py [12922 bytes]     - modulenamer.py [762 bytes]     - namespaces.py [10782 bytes]     - packing.py [2458 bytes]     - typeannotator.py [14456 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\tools\codegenerator\__pycache__
    - __init__.cpython-313.pyc [407 bytes]     - codegenerator.cpython-313.pyc [46233 bytes]     - comments.cpython-313.pyc [4913 bytes]     - heads.cpython-313.pyc [13086 bytes]     - helpers.cpython-313.pyc [17103 bytes]     - modulenamer.cpython-313.pyc [1482 bytes]     - namespaces.cpython-313.pyc [13895 bytes]     - packing.cpython-313.pyc [3396 bytes]     - typeannotator.cpython-313.pyc [22246 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes\_post_coinit\__pycache__
    - __init__.cpython-313.pyc [909 bytes]     - _cominterface_meta_patcher.cpython-313.pyc [6413 bytes]     - bstr.cpython-313.pyc [2029 bytes]     - instancemethod.cpython-313.pyc [727 bytes]     - misc.cpython-313.pyc [13782 bytes]     - unknwn.cpython-313.pyc [15059 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\comtypes-1.4.11.dist-info\licenses
    - LICENSE.txt [1273 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\dotenv\__pycache__
    - __init__.cpython-313.pyc [1717 bytes]     - __main__.cpython-313.pyc [335 bytes]     - cli.cpython-313.pyc [9544 bytes]     - ipython.cpython-313.pyc [1974 bytes]     - main.cpython-313.pyc [17073 bytes]     - parser.cpython-313.pyc [10221 bytes]     - variables.cpython-313.pyc [5125 bytes]     - version.cpython-313.pyc [185 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\__pycache__
    - __init__.cpython-313.pyc [1077 bytes]     - __main__.cpython-313.pyc [223 bytes]     - _compat.cpython-313.pyc [28712 bytes]     - applications.cpython-313.pyc [83041 bytes]     - background.cpython-313.pyc [2309 bytes]     - cli.cpython-313.pyc [653 bytes]     - concurrency.cpython-313.pyc [1646 bytes]     - datastructures.cpython-313.pyc [8064 bytes]     - encoders.cpython-313.pyc [11041 bytes]     - exception_handlers.cpython-313.pyc [2043 bytes]     - exceptions.cpython-313.pyc [7163 bytes]     - logger.cpython-313.pyc [261 bytes]     - param_functions.cpython-313.pyc [34906 bytes]     - params.cpython-313.pyc [25615 bytes]     - requests.cpython-313.pyc [252 bytes]     - responses.cpython-313.pyc [2428 bytes]     - routing.cpython-313.pyc [81948 bytes]     - staticfiles.cpython-313.pyc [224 bytes]     - templating.cpython-313.pyc [226 bytes]     - testclient.cpython-313.pyc [221 bytes]     - types.cpython-313.pyc [726 bytes]     - utils.cpython-313.pyc [8528 bytes]     - websockets.cpython-313.pyc [301 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\dependencies
    - __init__.py [0 bytes]     - models.py [1507 bytes]     - utils.py [35971 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\middleware
    - __init__.py [58 bytes]     - cors.py [79 bytes]     - gzip.py [79 bytes]     - httpsredirect.py [115 bytes]     - trustedhost.py [109 bytes]     - wsgi.py [79 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\openapi
    - __init__.py [0 bytes]     - constants.py [153 bytes]     - docs.py [10348 bytes]     - models.py [15397 bytes]     - utils.py [23964 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\security
    - __init__.py [881 bytes]     - api_key.py [9094 bytes]     - base.py [141 bytes]     - http.py [13606 bytes]     - oauth2.py [21589 bytes]     - open_id_connect_url.py [2722 bytes]     - utils.py [293 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\dependencies\__pycache__
    - __init__.cpython-313.pyc [174 bytes]     - models.cpython-313.pyc [2769 bytes]     - utils.cpython-313.pyc [39344 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\middleware\__pycache__
    - __init__.cpython-313.pyc [230 bytes]     - cors.cpython-313.pyc [235 bytes]     - gzip.cpython-313.pyc [235 bytes]     - httpsredirect.cpython-313.pyc [264 bytes]     - trustedhost.cpython-313.pyc [258 bytes]     - wsgi.cpython-313.pyc [235 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\openapi\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - constants.cpython-313.pyc [339 bytes]     - docs.cpython-313.pyc [10767 bytes]     - models.cpython-313.pyc [24020 bytes]     - utils.cpython-313.pyc [21768 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi\security\__pycache__
    - __init__.cpython-313.pyc [826 bytes]     - api_key.cpython-313.pyc [9625 bytes]     - base.cpython-313.pyc [546 bytes]     - http.cpython-313.pyc [13621 bytes]     - oauth2.cpython-313.pyc [18157 bytes]     - open_id_connect_url.cpython-313.pyc [3273 bytes]     - utils.cpython-313.pyc [585 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\fastapi-0.115.12.dist-info\licenses
    - LICENSE [1086 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\_upb
    - _message.pyd [713235 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api
    - annotations_pb2.py [2182 bytes]     - annotations_pb2.pyi [887 bytes]     - annotations.proto [1045 bytes]     - auth_pb2.py [3601 bytes]     - auth_pb2.pyi [4324 bytes]     - auth.proto [9257 bytes]     - backend_pb2.py [3774 bytes]     - backend_pb2.pyi [3855 bytes]     - backend.proto [7014 bytes]     - billing_pb2.py [2314 bytes]     - billing_pb2.pyi [1871 bytes]     - billing.proto [3062 bytes]     - client_pb2.py [11033 bytes]     - client_pb2.pyi [16046 bytes]     - client.proto [17312 bytes]     - config_change_pb2.py [2693 bytes]     - config_change_pb2.pyi [2384 bytes]     - config_change.proto [3166 bytes]     - consumer_pb2.py [2574 bytes]     - consumer_pb2.pyi [2349 bytes]     - consumer.proto [2717 bytes]     - context_pb2.py [2376 bytes]     - context_pb2.pyi [2339 bytes]     - context.proto [3229 bytes]     - control_pb2.py [2177 bytes]     - control_pb2.pyi [1546 bytes]     - control.proto [1436 bytes]     - distribution_pb2.py [4484 bytes]     - distribution_pb2.pyi [5768 bytes]     - distribution.proto [8660 bytes]     - documentation_pb2.py [2843 bytes]     - documentation_pb2.pyi [3062 bytes]     - documentation.proto [6925 bytes]     - endpoint_pb2.py [2108 bytes]     - endpoint_pb2.pyi [1479 bytes]     - endpoint.proto [2891 bytes]     - error_reason_pb2.py [3509 bytes]     - error_reason_pb2.pyi [3784 bytes]     - error_reason.proto [23628 bytes]     - field_behavior_pb2.py [2680 bytes]     - field_behavior_pb2.pyi [1680 bytes]     - field_behavior.proto [4306 bytes]     - field_info_pb2.py [2817 bytes]     - field_info_pb2.pyi [2368 bytes]     - field_info.proto [4456 bytes]     - http_pb2.py [2869 bytes]     - http_pb2.pyi [3147 bytes]     - http.proto [15059 bytes]     - httpbody_pb2.py [2190 bytes]     - httpbody_pb2.pyi [1569 bytes]     - httpbody.proto [2661 bytes]     - label_pb2.py [2326 bytes]     - label_pb2.pyi [1778 bytes]     - label.proto [1357 bytes]     - launch_stage_pb2.py [2211 bytes]     - launch_stage_pb2.pyi [1381 bytes]     - launch_stage.proto [3083 bytes]     - log_pb2.py [2237 bytes]     - log_pb2.pyi [1728 bytes]     - log.proto [2043 bytes]     - logging_pb2.py [2395 bytes]     - logging_pb2.pyi [2178 bytes]     - logging.proto [3174 bytes]     - metric_pb2.py [5467 bytes]     - metric_pb2.pyi [7729 bytes]     - metric.proto [11166 bytes]     - monitored_resource_pb2.py [4146 bytes]     - monitored_resource_pb2.pyi [3644 bytes]     - monitored_resource.proto [5888 bytes]     - monitoring_pb2.py [2450 bytes]     - monitoring_pb2.pyi [2220 bytes]     - monitoring.proto [4457 bytes]     - policy_pb2.py [2685 bytes]     - policy_pb2.pyi [2169 bytes]     - policy.proto [3110 bytes]     - quota_pb2.py [3612 bytes]     - quota_pb2.pyi [3876 bytes]     - quota.proto [7180 bytes]     - resource_pb2.py [3534 bytes]     - resource_pb2.pyi [3588 bytes]     - resource.proto [8994 bytes]     - routing_pb2.py [2488 bytes]     - routing_pb2.pyi [1821 bytes]     - routing.proto [14918 bytes]     - service_pb2.py [5857 bytes]     - service_pb2.pyi [7164 bytes]     - service.proto [6762 bytes]     - source_info_pb2.py [2154 bytes]     - source_info_pb2.pyi [1343 bytes]     - source_info.proto [1091 bytes]     - system_parameter_pb2.py [2610 bytes]     - system_parameter_pb2.pyi [2217 bytes]     - system_parameter.proto [3475 bytes]     - usage_pb2.py [2353 bytes]     - usage_pb2.pyi [2175 bytes]     - usage.proto [3787 bytes]     - visibility_pb2.py [3113 bytes]     - visibility_pb2.pyi [2213 bytes]     - visibility.proto [3767 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core
    - __init__.py [782 bytes]     - _rest_streaming_base.py [4351 bytes]     - bidi.py [28043 bytes]     - client_info.py [3802 bytes]     - client_logging.py [5023 bytes]     - client_options.py [6566 bytes]     - datetime_helpers.py [9034 bytes]     - exceptions.py [21150 bytes]     - extended_operation.py [8632 bytes]     - general_helpers.py [681 bytes]     - grpc_helpers_async.py [12966 bytes]     - grpc_helpers.py [24787 bytes]     - iam.py [13213 bytes]     - operation_async.py [8046 bytes]     - operation.py [13198 bytes]     - page_iterator_async.py [10294 bytes]     - page_iterator.py [20330 bytes]     - path_template.py [11685 bytes]     - protobuf_helpers.py [12448 bytes]     - py.typed [78 bytes]     - rest_helpers.py [3529 bytes]     - rest_streaming_async.py [3340 bytes]     - rest_streaming.py [2209 bytes]     - retry_async.py [1514 bytes]     - timeout.py [10279 bytes]     - universe.py [2952 bytes]     - version_header.py [1046 bytes]     - version.py [598 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth
    - __init__.py [1639 bytes]     - _cloud_sdk.py [5212 bytes]     - _credentials_async.py [6802 bytes]     - _credentials_base.py [2692 bytes]     - _default_async.py [11575 bytes]     - _default.py [28687 bytes]     - _exponential_backoff.py [5372 bytes]     - _helpers.py [16584 bytes]     - _jwt_async.py [5972 bytes]     - _oauth2client.py [5855 bytes]     - _refresh_worker.py [3375 bytes]     - _service_account_info.py [2816 bytes]     - api_key.py [2583 bytes]     - app_engine.py [6121 bytes]     - aws.py [34568 bytes]     - credentials.py [18700 bytes]     - downscoped.py [21793 bytes]     - environment_vars.py [3297 bytes]     - exceptions.py [3193 bytes]     - external_account_authorized_user.py [13987 bytes]     - external_account.py [25977 bytes]     - iam.py [4674 bytes]     - identity_pool.py [22554 bytes]     - impersonated_credentials.py [24971 bytes]     - jwt.py [31096 bytes]     - metrics.py [5614 bytes]     - pluggable.py [17322 bytes]     - py.typed [74 bytes]     - version.py [598 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\cloud
    - extended_operations_pb2.py [2815 bytes]     - extended_operations_pb2.pyi [1889 bytes]     - extended_operations.proto [6307 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\gapic


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai
    - __init__.py [2549 bytes]     - answer.py [14058 bytes]     - caching.py [10795 bytes]     - client.py [15071 bytes]     - embedding.py [12102 bytes]     - files.py [4122 bytes]     - generative_models.py [33828 bytes]     - models.py [15915 bytes]     - operations.py [5053 bytes]     - permission.py [6367 bytes]     - protos.py [2738 bytes]     - py.typed [41 bytes]     - responder.py [19972 bytes]     - retriever.py [8907 bytes]     - string_utils.py [2441 bytes]     - utils.py [1090 bytes]     - version.py [656 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\logging


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\longrunning
    - operations_grpc_pb2.py [531 bytes]     - operations_grpc.py [818 bytes]     - operations_pb2_grpc.py [14463 bytes]     - operations_pb2.py [1569 bytes]     - operations_proto_pb2.py [7231 bytes]     - operations_proto_pb2.pyi [4490 bytes]     - operations_proto.proto [10041 bytes]     - operations_proto.py [243 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\oauth2
    - __init__.py [1196 bytes]     - _client_async.py [10129 bytes]     - _client.py [17338 bytes]     - _credentials_async.py [4474 bytes]     - _id_token_async.py [10115 bytes]     - _reauth_async.py [11696 bytes]     - _service_account_async.py [5131 bytes]     - challenges.py [10404 bytes]     - credentials.py [24913 bytes]     - gdch_credentials.py [9007 bytes]     - id_token.py [13497 bytes]     - py.typed [76 bytes]     - reauth.py [12845 bytes]     - service_account.py [32232 bytes]     - sts.py [6699 bytes]     - utils.py [6315 bytes]     - webauthn_handler_factory.py [429 bytes]     - webauthn_handler.py [2743 bytes]     - webauthn_types.py [5386 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf
    - __init__.py [346 bytes]     - any_pb2.py [1725 bytes]     - any.py [975 bytes]     - api_pb2.py [3145 bytes]     - descriptor_database.py [5444 bytes]     - descriptor_pb2.py [343365 bytes]     - descriptor_pool.py [48430 bytes]     - descriptor.py [52253 bytes]     - duration_pb2.py [1805 bytes]     - duration.py [2672 bytes]     - empty_pb2.py [1669 bytes]     - field_mask_pb2.py [1765 bytes]     - json_format.py [37260 bytes]     - message_factory.py [8262 bytes]     - message.py [14042 bytes]     - proto_builder.py [4203 bytes]     - proto_json.py [3094 bytes]     - proto.py [3500 bytes]     - reflection.py [2893 bytes]     - runtime_version.py [3911 bytes]     - service_reflection.py [10058 bytes]     - service.py [8059 bytes]     - source_context_pb2.py [1791 bytes]     - struct_pb2.py [3061 bytes]     - symbol_database.py [6709 bytes]     - text_encoding.py [3621 bytes]     - text_format.py [63477 bytes]     - timestamp_pb2.py [1815 bytes]     - timestamp.py [3133 bytes]     - type_pb2.py [5438 bytes]     - unknown_fields.py [3127 bytes]     - wrappers_pb2.py [3037 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\rpc
    - code_pb2.py [2479 bytes]     - code_pb2.pyi [1771 bytes]     - code.proto [7138 bytes]     - error_details_pb2.py [6457 bytes]     - error_details_pb2.pyi [8436 bytes]     - error_details.proto [14599 bytes]     - http_pb2.py [2568 bytes]     - http_pb2.pyi [2467 bytes]     - http.proto [1940 bytes]     - status_pb2.py [2186 bytes]     - status_pb2.pyi [1531 bytes]     - status.proto [1934 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\type
    - calendar_period_pb2.py [2285 bytes]     - calendar_period_pb2.pyi [1400 bytes]     - calendar_period.proto [1762 bytes]     - color_pb2.py [2233 bytes]     - color_pb2.pyi [1492 bytes]     - color.proto [6376 bytes]     - date_pb2.py [2036 bytes]     - date_pb2.pyi [1187 bytes]     - date.proto [1955 bytes]     - datetime_pb2.py [2712 bytes]     - datetime_pb2.pyi [2420 bytes]     - datetime.proto [3905 bytes]     - dayofweek_pb2.py [2224 bytes]     - dayofweek_pb2.pyi [1335 bytes]     - dayofweek.proto [1204 bytes]     - decimal_pb2.py [2009 bytes]     - decimal_pb2.pyi [980 bytes]     - decimal.proto [4213 bytes]     - expr_pb2.py [2051 bytes]     - expr_pb2.pyi [1355 bytes]     - expr.proto [2730 bytes]     - fraction_pb2.py [2042 bytes]     - fraction_pb2.pyi [1126 bytes]     - fraction.proto [1156 bytes]     - interval_pb2.py [2239 bytes]     - interval_pb2.pyi [1389 bytes]     - interval.proto [1667 bytes]     - latlng_pb2.py [2028 bytes]     - latlng_pb2.pyi [1120 bytes]     - latlng.proto [1447 bytes]     - localized_text_pb2.py [2111 bytes]     - localized_text_pb2.pyi [1119 bytes]     - localized_text.proto [1303 bytes]     - money_pb2.py [2042 bytes]     - money_pb2.pyi [1232 bytes]     - money.proto [1603 bytes]     - month_pb2.py [2304 bytes]     - month_pb2.pyi [1458 bytes]     - month.proto [1479 bytes]     - phone_number_pb2.py [2440 bytes]     - phone_number_pb2.pyi [1745 bytes]     - phone_number.proto [4744 bytes]     - postal_address_pb2.py [2495 bytes]     - postal_address_pb2.pyi [2605 bytes]     - postal_address.proto [6235 bytes]     - quaternion_pb2.py [2123 bytes]     - quaternion_pb2.pyi [1257 bytes]     - quaternion.proto [3791 bytes]     - timeofday_pb2.py [2135 bytes]     - timeofday_pb2.pyi [1320 bytes]     - timeofday.proto [1667 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage
    - __init__.py [11838 bytes]     - gapic_version.py [653 bytes]     - py.typed [89 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1
    - __init__.py [2529 bytes]     - gapic_metadata.json [3669 bytes]     - gapic_version.py [653 bytes]     - py.typed [89 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha
    - __init__.py [10834 bytes]     - gapic_metadata.json [24648 bytes]     - gapic_version.py [653 bytes]     - py.typed [89 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta
    - __init__.py [9935 bytes]     - gapic_metadata.json [24241 bytes]     - gapic_version.py [653 bytes]     - py.typed [89 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2
    - __init__.py [2426 bytes]     - gapic_metadata.json [3627 bytes]     - gapic_version.py [653 bytes]     - py.typed [89 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3
    - __init__.py [4188 bytes]     - gapic_metadata.json [8993 bytes]     - gapic_version.py [653 bytes]     - py.typed [89 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage\__pycache__
    - __init__.cpython-313.pyc [9043 bytes]     - gapic_version.cpython-313.pyc [216 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\__pycache__
    - __init__.cpython-313.pyc [1768 bytes]     - gapic_version.cpython-313.pyc [219 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services
    - __init__.py [600 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\types
    - __init__.py [2059 bytes]     - citation.py [2895 bytes]     - content.py [3922 bytes]     - generative_service.py [39332 bytes]     - model_service.py [3086 bytes]     - model.py [5376 bytes]     - safety.py [6508 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\__pycache__
    - __init__.cpython-313.pyc [194 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\generative_service
    - __init__.py [781 bytes]     - async_client.py [49358 bytes]     - client.py [64263 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\model_service
    - __init__.py [761 bytes]     - async_client.py [30142 bytes]     - client.py [45255 bytes]     - pagers.py [7695 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\generative_service\__pycache__
    - __init__.cpython-313.pyc [370 bytes]     - async_client.cpython-313.pyc [43590 bytes]     - client.cpython-313.pyc [58007 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\generative_service\transports
    - __init__.py [1442 bytes]     - base.py [11449 bytes]     - grpc_asyncio.py [28840 bytes]     - grpc.py [24588 bytes]     - rest_base.py [17287 bytes]     - rest.py [66084 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\generative_service\transports\__pycache__
    - __init__.cpython-313.pyc [796 bytes]     - base.cpython-313.pyc [11216 bytes]     - grpc_asyncio.cpython-313.pyc [26522 bytes]     - grpc.cpython-313.pyc [22747 bytes]     - rest_base.cpython-313.pyc [19974 bytes]     - rest.cpython-313.pyc [58037 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\model_service\__pycache__
    - __init__.cpython-313.pyc [355 bytes]     - async_client.cpython-313.pyc [27980 bytes]     - client.cpython-313.pyc [43148 bytes]     - pagers.cpython-313.pyc [9763 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\model_service\transports
    - __init__.py [1372 bytes]     - base.py [7990 bytes]     - grpc_asyncio.py [21801 bytes]     - grpc.py [19932 bytes]     - rest_base.py [9847 bytes]     - rest.py [40348 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\services\model_service\transports\__pycache__
    - __init__.cpython-313.pyc [766 bytes]     - base.cpython-313.pyc [8458 bytes]     - grpc_asyncio.cpython-313.pyc [21057 bytes]     - grpc.cpython-313.pyc [18786 bytes]     - rest_base.cpython-313.pyc [11727 bytes]     - rest.cpython-313.pyc [37065 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1\types\__pycache__
    - __init__.cpython-313.pyc [1357 bytes]     - citation.cpython-313.pyc [3089 bytes]     - content.cpython-313.pyc [4382 bytes]     - generative_service.cpython-313.pyc [43524 bytes]     - model_service.cpython-313.pyc [3702 bytes]     - model.cpython-313.pyc [5534 bytes]     - safety.cpython-313.pyc [7008 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\__pycache__
    - __init__.cpython-313.pyc [7794 bytes]     - gapic_version.cpython-313.pyc [224 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services
    - __init__.py [600 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\types
    - __init__.py [9219 bytes]     - cache_service.py [4790 bytes]     - cached_content.py [6177 bytes]     - citation.py [2905 bytes]     - content.py [25928 bytes]     - discuss_service.py [11613 bytes]     - file_service.py [3560 bytes]     - file.py [5442 bytes]     - generative_service.py [77627 bytes]     - model_service.py [9628 bytes]     - model.py [5381 bytes]     - permission_service.py [6367 bytes]     - permission.py [4533 bytes]     - prediction_service.py [2353 bytes]     - retriever_service.py [24490 bytes]     - retriever.py [13711 bytes]     - safety.py [8949 bytes]     - text_service.py [14391 bytes]     - tuned_model.py [17319 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\__pycache__
    - __init__.cpython-313.pyc [199 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\cache_service
    - __init__.py [761 bytes]     - async_client.py [40596 bytes]     - client.py [55760 bytes]     - pagers.py [8031 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\discuss_service
    - __init__.py [769 bytes]     - async_client.py [31141 bytes]     - client.py [46347 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\file_service
    - __init__.py [757 bytes]     - async_client.py [32608 bytes]     - client.py [47583 bytes]     - pagers.py [7694 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\generative_service
    - __init__.py [781 bytes]     - async_client.py [59462 bytes]     - client.py [74480 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\model_service
    - __init__.py [761 bytes]     - async_client.py [53765 bytes]     - client.py [68751 bytes]     - pagers.py [14317 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\permission_service
    - __init__.py [781 bytes]     - async_client.py [48853 bytes]     - client.py [63877 bytes]     - pagers.py [7970 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\prediction_service
    - __init__.py [781 bytes]     - async_client.py [22704 bytes]     - client.py [38056 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\retriever_service
    - __init__.py [777 bytes]     - async_client.py [106467 bytes]     - client.py [120919 bytes]     - pagers.py [20788 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\text_service
    - __init__.py [757 bytes]     - async_client.py [42359 bytes]     - client.py [57344 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\cache_service\__pycache__
    - __init__.cpython-313.pyc [360 bytes]     - async_client.cpython-313.pyc [36164 bytes]     - client.cpython-313.pyc [51518 bytes]     - pagers.cpython-313.pyc [10101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\cache_service\transports
    - __init__.py [1372 bytes]     - base.py [9453 bytes]     - grpc_asyncio.py [25381 bytes]     - grpc.py [23003 bytes]     - rest_base.py [14464 bytes]     - rest.py [57535 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\cache_service\transports\__pycache__
    - __init__.cpython-313.pyc [771 bytes]     - base.cpython-313.pyc [9848 bytes]     - grpc_asyncio.cpython-313.pyc [23997 bytes]     - grpc.cpython-313.pyc [21390 bytes]     - rest_base.cpython-313.pyc [16749 bytes]     - rest.cpython-313.pyc [50035 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\discuss_service\__pycache__
    - __init__.cpython-313.pyc [366 bytes]     - async_client.cpython-313.pyc [28702 bytes]     - client.cpython-313.pyc [44010 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\discuss_service\transports
    - __init__.py [1400 bytes]     - base.py [7833 bytes]     - grpc_asyncio.py [20935 bytes]     - grpc.py [19248 bytes]     - rest_base.py [9865 bytes]     - rest.py [36632 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\discuss_service\transports\__pycache__
    - __init__.cpython-313.pyc [783 bytes]     - base.cpython-313.pyc [8112 bytes]     - grpc_asyncio.cpython-313.pyc [20246 bytes]     - grpc.cpython-313.pyc [18099 bytes]     - rest_base.cpython-313.pyc [11531 bytes]     - rest.cpython-313.pyc [33369 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\file_service\__pycache__
    - __init__.cpython-313.pyc [357 bytes]     - async_client.cpython-313.pyc [30111 bytes]     - client.cpython-313.pyc [45024 bytes]     - pagers.cpython-313.pyc [9777 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\file_service\transports
    - __init__.py [1358 bytes]     - base.py [8519 bytes]     - grpc_asyncio.py [22950 bytes]     - grpc.py [20876 bytes]     - rest_base.py [11666 bytes]     - rest.py [47360 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\file_service\transports\__pycache__
    - __init__.cpython-313.pyc [765 bytes]     - base.cpython-313.pyc [9105 bytes]     - grpc_asyncio.cpython-313.pyc [22176 bytes]     - grpc.cpython-313.pyc [19729 bytes]     - rest_base.cpython-313.pyc [13647 bytes]     - rest.cpython-313.pyc [42707 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\generative_service\__pycache__
    - __init__.cpython-313.pyc [375 bytes]     - async_client.cpython-313.pyc [52157 bytes]     - client.cpython-313.pyc [66840 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\generative_service\transports
    - __init__.py [1442 bytes]     - base.py [10459 bytes]     - grpc_asyncio.py [29263 bytes]     - grpc.py [26511 bytes]     - rest_base.py [18206 bytes]     - rest.py [69695 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\generative_service\transports\__pycache__
    - __init__.cpython-313.pyc [801 bytes]     - base.cpython-313.pyc [10918 bytes]     - grpc_asyncio.cpython-313.pyc [27372 bytes]     - grpc.cpython-313.pyc [24443 bytes]     - rest_base.cpython-313.pyc [21121 bytes]     - rest.cpython-313.pyc [61267 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\model_service\__pycache__
    - __init__.cpython-313.pyc [360 bytes]     - async_client.cpython-313.pyc [46393 bytes]     - client.cpython-313.pyc [61285 bytes]     - pagers.cpython-313.pyc [17881 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\model_service\transports
    - __init__.py [1372 bytes]     - base.py [10290 bytes]     - grpc_asyncio.py [28759 bytes]     - grpc.py [25954 bytes]     - rest_base.py [17063 bytes]     - rest.py [72809 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\model_service\transports\__pycache__
    - __init__.cpython-313.pyc [771 bytes]     - base.cpython-313.pyc [11124 bytes]     - grpc_asyncio.cpython-313.pyc [27174 bytes]     - grpc.cpython-313.pyc [24244 bytes]     - rest_base.cpython-313.pyc [19692 bytes]     - rest.cpython-313.pyc [63484 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\permission_service\__pycache__
    - __init__.cpython-313.pyc [375 bytes]     - async_client.cpython-313.pyc [42802 bytes]     - client.cpython-313.pyc [57557 bytes]     - pagers.cpython-313.pyc [10005 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\permission_service\transports
    - __init__.py [1442 bytes]     - base.py [9822 bytes]     - grpc_asyncio.py [26584 bytes]     - grpc.py [24014 bytes]     - rest_base.py [17870 bytes]     - rest.py [67716 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\permission_service\transports\__pycache__
    - __init__.cpython-313.pyc [801 bytes]     - base.cpython-313.pyc [10411 bytes]     - grpc_asyncio.cpython-313.pyc [25021 bytes]     - grpc.cpython-313.pyc [22239 bytes]     - rest_base.cpython-313.pyc [20169 bytes]     - rest.cpython-313.pyc [58276 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\prediction_service\__pycache__
    - __init__.cpython-313.pyc [375 bytes]     - async_client.cpython-313.pyc [21893 bytes]     - client.cpython-313.pyc [37491 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\prediction_service\transports
    - __init__.py [1442 bytes]     - base.py [7275 bytes]     - grpc_asyncio.py [19169 bytes]     - grpc.py [17705 bytes]     - rest_base.py [7903 bytes]     - rest.py [28191 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\prediction_service\transports\__pycache__
    - __init__.cpython-313.pyc [801 bytes]     - base.cpython-313.pyc [7539 bytes]     - grpc_asyncio.cpython-313.pyc [18785 bytes]     - grpc.cpython-313.pyc [16779 bytes]     - rest_base.cpython-313.pyc [9275 bytes]     - rest.cpython-313.pyc [26492 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\retriever_service\__pycache__
    - __init__.cpython-313.pyc [372 bytes]     - async_client.cpython-313.pyc [89492 bytes]     - client.cpython-313.pyc [102896 bytes]     - pagers.cpython-313.pyc [25703 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\retriever_service\transports
    - __init__.py [1428 bytes]     - base.py [15799 bytes]     - grpc_asyncio.py [44578 bytes]     - grpc.py [39136 bytes]     - rest_base.py [41654 bytes]     - rest.py [164167 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\retriever_service\transports\__pycache__
    - __init__.cpython-313.pyc [795 bytes]     - base.cpython-313.pyc [17843 bytes]     - grpc_asyncio.cpython-313.pyc [40660 bytes]     - grpc.cpython-313.pyc [35576 bytes]     - rest_base.cpython-313.pyc [46687 bytes]     - rest.cpython-313.pyc [136136 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\text_service\__pycache__
    - __init__.cpython-313.pyc [357 bytes]     - async_client.cpython-313.pyc [37791 bytes]     - client.cpython-313.pyc [52563 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\text_service\transports
    - __init__.py [1358 bytes]     - base.py [8711 bytes]     - grpc_asyncio.py [23545 bytes]     - grpc.py [21435 bytes]     - rest_base.py [13837 bytes]     - rest.py [51329 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\services\text_service\transports\__pycache__
    - __init__.cpython-313.pyc [765 bytes]     - base.cpython-313.pyc [9132 bytes]     - grpc_asyncio.cpython-313.pyc [22542 bytes]     - grpc.cpython-313.pyc [20094 bytes]     - rest_base.cpython-313.pyc [15862 bytes]     - rest.cpython-313.pyc [45350 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1alpha\types\__pycache__
    - __init__.cpython-313.pyc [6480 bytes]     - cache_service.cpython-313.pyc [5826 bytes]     - cached_content.cpython-313.pyc [6521 bytes]     - citation.cpython-313.pyc [3104 bytes]     - content.cpython-313.pyc [30195 bytes]     - discuss_service.cpython-313.pyc [12739 bytes]     - file_service.cpython-313.pyc [4620 bytes]     - file.cpython-313.pyc [6016 bytes]     - generative_service.cpython-313.pyc [84946 bytes]     - model_service.cpython-313.pyc [11756 bytes]     - model.cpython-313.pyc [5544 bytes]     - permission_service.cpython-313.pyc [7914 bytes]     - permission.cpython-313.pyc [4697 bytes]     - prediction_service.cpython-313.pyc [2631 bytes]     - retriever_service.cpython-313.pyc [29590 bytes]     - retriever.cpython-313.pyc [15376 bytes]     - safety.cpython-313.pyc [9976 bytes]     - text_service.cpython-313.pyc [16479 bytes]     - tuned_model.cpython-313.pyc [19617 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\__pycache__
    - __init__.cpython-313.pyc [7210 bytes]     - gapic_version.cpython-313.pyc [223 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services
    - __init__.py [600 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\types
    - __init__.py [8321 bytes]     - cache_service.py [4785 bytes]     - cached_content.py [6170 bytes]     - citation.py [2903 bytes]     - content.py [25899 bytes]     - discuss_service.py [11601 bytes]     - file_service.py [3555 bytes]     - file.py [5439 bytes]     - generative_service.py [62951 bytes]     - model_service.py [9620 bytes]     - model.py [5380 bytes]     - permission_service.py [6362 bytes]     - permission.py [4530 bytes]     - prediction_service.py [2351 bytes]     - retriever_service.py [24469 bytes]     - retriever.py [13703 bytes]     - safety.py [8941 bytes]     - text_service.py [14378 bytes]     - tuned_model.py [14112 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\__pycache__
    - __init__.cpython-313.pyc [198 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\cache_service
    - __init__.py [761 bytes]     - async_client.py [40561 bytes]     - client.py [55725 bytes]     - pagers.py [8022 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\discuss_service
    - __init__.py [769 bytes]     - async_client.py [31122 bytes]     - client.py [46328 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\file_service
    - __init__.py [757 bytes]     - async_client.py [32583 bytes]     - client.py [47558 bytes]     - pagers.py [7685 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service
    - __init__.py [781 bytes]     - async_client.py [55482 bytes]     - client.py [70623 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\model_service
    - __init__.py [761 bytes]     - async_client.py [53721 bytes]     - client.py [68707 bytes]     - pagers.py [14300 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\permission_service
    - __init__.py [781 bytes]     - async_client.py [48814 bytes]     - client.py [63838 bytes]     - pagers.py [7961 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\prediction_service
    - __init__.py [781 bytes]     - async_client.py [22692 bytes]     - client.py [38044 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\retriever_service
    - __init__.py [777 bytes]     - async_client.py [106354 bytes]     - client.py [120806 bytes]     - pagers.py [20763 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\text_service
    - __init__.py [757 bytes]     - async_client.py [42330 bytes]     - client.py [57315 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\cache_service\__pycache__
    - __init__.cpython-313.pyc [359 bytes]     - async_client.cpython-313.pyc [36132 bytes]     - client.cpython-313.pyc [51486 bytes]     - pagers.cpython-313.pyc [10091 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\cache_service\transports
    - __init__.py [1372 bytes]     - base.py [9449 bytes]     - grpc_asyncio.py [25371 bytes]     - grpc.py [22993 bytes]     - rest_base.py [14451 bytes]     - rest.py [57506 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\cache_service\transports\__pycache__
    - __init__.cpython-313.pyc [770 bytes]     - base.cpython-313.pyc [9845 bytes]     - grpc_asyncio.cpython-313.pyc [23989 bytes]     - grpc.cpython-313.pyc [21382 bytes]     - rest_base.cpython-313.pyc [16739 bytes]     - rest.cpython-313.pyc [50019 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\discuss_service\__pycache__
    - __init__.cpython-313.pyc [365 bytes]     - async_client.cpython-313.pyc [28683 bytes]     - client.cpython-313.pyc [43991 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\discuss_service\transports
    - __init__.py [1400 bytes]     - base.py [8515 bytes]     - grpc_asyncio.py [21624 bytes]     - grpc.py [19243 bytes]     - rest_base.py [9857 bytes]     - rest.py [36615 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\discuss_service\transports\__pycache__
    - __init__.cpython-313.pyc [782 bytes]     - base.cpython-313.pyc [8613 bytes]     - grpc_asyncio.cpython-313.pyc [20754 bytes]     - grpc.cpython-313.pyc [18094 bytes]     - rest_base.cpython-313.pyc [11522 bytes]     - rest.cpython-313.pyc [33358 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\file_service\__pycache__
    - __init__.cpython-313.pyc [356 bytes]     - async_client.cpython-313.pyc [30086 bytes]     - client.cpython-313.pyc [44999 bytes]     - pagers.cpython-313.pyc [9767 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\file_service\transports
    - __init__.py [1358 bytes]     - base.py [8517 bytes]     - grpc_asyncio.py [22943 bytes]     - grpc.py [20869 bytes]     - rest_base.py [11656 bytes]     - rest.py [47337 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\file_service\transports\__pycache__
    - __init__.cpython-313.pyc [764 bytes]     - base.cpython-313.pyc [9102 bytes]     - grpc_asyncio.cpython-313.pyc [22169 bytes]     - grpc.cpython-313.pyc [19722 bytes]     - rest_base.cpython-313.pyc [13638 bytes]     - rest.cpython-313.pyc [42693 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\__pycache__
    - __init__.cpython-313.pyc [374 bytes]     - async_client.cpython-313.pyc [48604 bytes]     - client.cpython-313.pyc [63443 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\transports
    - __init__.py [1442 bytes]     - base.py [11954 bytes]     - grpc_asyncio.py [29694 bytes]     - grpc.py [25075 bytes]     - rest_base.py [18035 bytes]     - rest.py [68459 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\transports\__pycache__
    - __init__.cpython-313.pyc [800 bytes]     - base.cpython-313.pyc [11498 bytes]     - grpc_asyncio.cpython-313.pyc [27157 bytes]     - grpc.cpython-313.pyc [23192 bytes]     - rest_base.cpython-313.pyc [20671 bytes]     - rest.cpython-313.pyc [59535 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\model_service\__pycache__
    - __init__.cpython-313.pyc [359 bytes]     - async_client.cpython-313.pyc [46351 bytes]     - client.cpython-313.pyc [61243 bytes]     - pagers.cpython-313.pyc [17863 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\model_service\transports
    - __init__.py [1372 bytes]     - base.py [12680 bytes]     - grpc_asyncio.py [31176 bytes]     - grpc.py [25942 bytes]     - rest_base.py [17048 bytes]     - rest.py [72766 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\model_service\transports\__pycache__
    - __init__.cpython-313.pyc [770 bytes]     - base.cpython-313.pyc [12450 bytes]     - grpc_asyncio.cpython-313.pyc [28512 bytes]     - grpc.cpython-313.pyc [24234 bytes]     - rest_base.cpython-313.pyc [19680 bytes]     - rest.cpython-313.pyc [63458 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\permission_service\__pycache__
    - __init__.cpython-313.pyc [374 bytes]     - async_client.cpython-313.pyc [42765 bytes]     - client.cpython-313.pyc [57520 bytes]     - pagers.cpython-313.pyc [9995 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\permission_service\transports
    - __init__.py [1442 bytes]     - base.py [11528 bytes]     - grpc_asyncio.py [28308 bytes]     - grpc.py [24003 bytes]     - rest_base.py [17851 bytes]     - rest.py [67683 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\permission_service\transports\__pycache__
    - __init__.cpython-313.pyc [800 bytes]     - base.cpython-313.pyc [11407 bytes]     - grpc_asyncio.cpython-313.pyc [26026 bytes]     - grpc.cpython-313.pyc [22230 bytes]     - rest_base.cpython-313.pyc [20155 bytes]     - rest.cpython-313.pyc [58258 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\prediction_service\__pycache__
    - __init__.cpython-313.pyc [374 bytes]     - async_client.cpython-313.pyc [21881 bytes]     - client.cpython-313.pyc [37479 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\prediction_service\transports
    - __init__.py [1442 bytes]     - base.py [7273 bytes]     - grpc_asyncio.py [19165 bytes]     - grpc.py [17701 bytes]     - rest_base.py [7896 bytes]     - rest.py [28177 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\prediction_service\transports\__pycache__
    - __init__.cpython-313.pyc [800 bytes]     - base.cpython-313.pyc [7536 bytes]     - grpc_asyncio.cpython-313.pyc [18781 bytes]     - grpc.cpython-313.pyc [16775 bytes]     - rest_base.cpython-313.pyc [9267 bytes]     - rest.cpython-313.pyc [26482 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\retriever_service\__pycache__
    - __init__.cpython-313.pyc [371 bytes]     - async_client.cpython-313.pyc [89379 bytes]     - client.cpython-313.pyc [102783 bytes]     - pagers.cpython-313.pyc [25677 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\retriever_service\transports
    - __init__.py [1428 bytes]     - base.py [21269 bytes]     - grpc_asyncio.py [50107 bytes]     - grpc.py [39113 bytes]     - rest_base.py [41628 bytes]     - rest.py [164086 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\retriever_service\transports\__pycache__
    - __init__.cpython-313.pyc [794 bytes]     - base.cpython-313.pyc [20660 bytes]     - grpc_asyncio.cpython-313.pyc [43494 bytes]     - grpc.cpython-313.pyc [35553 bytes]     - rest_base.cpython-313.pyc [46666 bytes]     - rest.cpython-313.pyc [136093 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\text_service\__pycache__
    - __init__.cpython-313.pyc [356 bytes]     - async_client.cpython-313.pyc [37762 bytes]     - client.cpython-313.pyc [52534 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\text_service\transports
    - __init__.py [1358 bytes]     - base.py [10077 bytes]     - grpc_asyncio.py [24926 bytes]     - grpc.py [21428 bytes]     - rest_base.py [13826 bytes]     - rest.py [51304 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\text_service\transports\__pycache__
    - __init__.cpython-313.pyc [764 bytes]     - base.cpython-313.pyc [9964 bytes]     - grpc_asyncio.cpython-313.pyc [23383 bytes]     - grpc.cpython-313.pyc [20087 bytes]     - rest_base.cpython-313.pyc [15850 bytes]     - rest.cpython-313.pyc [45335 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\types\__pycache__
    - __init__.cpython-313.pyc [5897 bytes]     - cache_service.cpython-313.pyc [5820 bytes]     - cached_content.cpython-313.pyc [6513 bytes]     - citation.cpython-313.pyc [3101 bytes]     - content.cpython-313.pyc [30165 bytes]     - discuss_service.cpython-313.pyc [12726 bytes]     - file_service.cpython-313.pyc [4614 bytes]     - file.cpython-313.pyc [6012 bytes]     - generative_service.cpython-313.pyc [69123 bytes]     - model_service.cpython-313.pyc [11748 bytes]     - model.cpython-313.pyc [5542 bytes]     - permission_service.cpython-313.pyc [7908 bytes]     - permission.cpython-313.pyc [4693 bytes]     - prediction_service.cpython-313.pyc [2628 bytes]     - retriever_service.cpython-313.pyc [29568 bytes]     - retriever.cpython-313.pyc [15367 bytes]     - safety.cpython-313.pyc [9967 bytes]     - text_service.cpython-313.pyc [16465 bytes]     - tuned_model.cpython-313.pyc [15873 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\__pycache__
    - __init__.cpython-313.pyc [1696 bytes]     - gapic_version.cpython-313.pyc [224 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services
    - __init__.py [600 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\types
    - __init__.py [1847 bytes]     - citation.py [2905 bytes]     - discuss_service.py [11613 bytes]     - model_service.py [3158 bytes]     - model.py [4670 bytes]     - safety.py [7878 bytes]     - text_service.py [10981 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\__pycache__
    - __init__.cpython-313.pyc [199 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\discuss_service
    - __init__.py [769 bytes]     - async_client.py [26503 bytes]     - client.py [41785 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\model_service
    - __init__.py [761 bytes]     - async_client.py [22907 bytes]     - client.py [38134 bytes]     - pagers.py [7740 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\text_service
    - __init__.py [757 bytes]     - async_client.py [26806 bytes]     - client.py [42043 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\discuss_service\__pycache__
    - __init__.cpython-313.pyc [366 bytes]     - async_client.cpython-313.pyc [24597 bytes]     - client.cpython-313.pyc [40155 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\discuss_service\transports
    - __init__.py [1400 bytes]     - base.py [7528 bytes]     - grpc_asyncio.py [19481 bytes]     - grpc.py [17466 bytes]     - rest_base.py [7529 bytes]     - rest.py [23215 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\discuss_service\transports\__pycache__
    - __init__.cpython-313.pyc [783 bytes]     - base.cpython-313.pyc [7462 bytes]     - grpc_asyncio.cpython-313.pyc [18812 bytes]     - grpc.cpython-313.pyc [16425 bytes]     - rest_base.cpython-313.pyc [8632 bytes]     - rest.cpython-313.pyc [21669 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\model_service\__pycache__
    - __init__.cpython-313.pyc [360 bytes]     - async_client.cpython-313.pyc [21556 bytes]     - client.cpython-313.pyc [37108 bytes]     - pagers.cpython-313.pyc [9813 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\model_service\transports
    - __init__.py [1372 bytes]     - base.py [7340 bytes]     - grpc_asyncio.py [18964 bytes]     - grpc.py [16954 bytes]     - rest_base.py [6304 bytes]     - rest.py [21480 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\model_service\transports\__pycache__
    - __init__.cpython-313.pyc [771 bytes]     - base.cpython-313.pyc [7381 bytes]     - grpc_asyncio.cpython-313.pyc [18472 bytes]     - grpc.cpython-313.pyc [16088 bytes]     - rest_base.cpython-313.pyc [7367 bytes]     - rest.cpython-313.pyc [20537 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\text_service\__pycache__
    - __init__.cpython-313.pyc [357 bytes]     - async_client.cpython-313.pyc [24818 bytes]     - client.cpython-313.pyc [40300 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\text_service\transports
    - __init__.py [1358 bytes]     - base.py [7410 bytes]     - grpc_asyncio.py [19208 bytes]     - grpc.py [17208 bytes]     - rest_base.py [7451 bytes]     - rest.py [22234 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\services\text_service\transports\__pycache__
    - __init__.cpython-313.pyc [765 bytes]     - base.cpython-313.pyc [7382 bytes]     - grpc_asyncio.cpython-313.pyc [18622 bytes]     - grpc.cpython-313.pyc [16245 bytes]     - rest_base.cpython-313.pyc [8440 bytes]     - rest.cpython-313.pyc [20925 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta2\types\__pycache__
    - __init__.cpython-313.pyc [1184 bytes]     - citation.cpython-313.pyc [3104 bytes]     - discuss_service.cpython-313.pyc [12739 bytes]     - model_service.cpython-313.pyc [3776 bytes]     - model.cpython-313.pyc [4855 bytes]     - safety.cpython-313.pyc [8882 bytes]     - text_service.cpython-313.pyc [12339 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\__pycache__
    - __init__.cpython-313.pyc [2954 bytes]     - gapic_version.cpython-313.pyc [224 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services
    - __init__.py [600 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\types
    - __init__.py [3416 bytes]     - citation.py [2905 bytes]     - discuss_service.py [11613 bytes]     - model_service.py [8924 bytes]     - model.py [4670 bytes]     - permission_service.py [6197 bytes]     - permission.py [4460 bytes]     - safety.py [7974 bytes]     - text_service.py [13777 bytes]     - tuned_model.py [12841 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\__pycache__
    - __init__.cpython-313.pyc [199 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\discuss_service
    - __init__.py [769 bytes]     - async_client.py [26566 bytes]     - client.py [41848 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\model_service
    - __init__.py [761 bytes]     - async_client.py [49499 bytes]     - client.py [64561 bytes]     - pagers.py [14317 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\permission_service
    - __init__.py [781 bytes]     - async_client.py [44219 bytes]     - client.py [59637 bytes]     - pagers.py [7970 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\text_service
    - __init__.py [757 bytes]     - async_client.py [37884 bytes]     - client.py [52945 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\discuss_service\__pycache__
    - __init__.cpython-313.pyc [366 bytes]     - async_client.cpython-313.pyc [24654 bytes]     - client.cpython-313.pyc [40213 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\discuss_service\transports
    - __init__.py [1400 bytes]     - base.py [6906 bytes]     - grpc_asyncio.py [18849 bytes]     - grpc.py [17528 bytes]     - rest_base.py [7591 bytes]     - rest.py [23277 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\discuss_service\transports\__pycache__
    - __init__.cpython-313.pyc [783 bytes]     - base.cpython-313.pyc [7029 bytes]     - grpc_asyncio.cpython-313.pyc [18370 bytes]     - grpc.cpython-313.pyc [16483 bytes]     - rest_base.cpython-313.pyc [8690 bytes]     - rest.cpython-313.pyc [21727 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\model_service\__pycache__
    - __init__.cpython-313.pyc [360 bytes]     - async_client.cpython-313.pyc [42635 bytes]     - client.cpython-313.pyc [57788 bytes]     - pagers.cpython-313.pyc [17881 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\model_service\transports
    - __init__.py [1372 bytes]     - base.py [9363 bytes]     - grpc_asyncio.py [26290 bytes]     - grpc.py [23851 bytes]     - rest_base.py [14828 bytes]     - rest.py [58481 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\model_service\transports\__pycache__
    - __init__.cpython-313.pyc [771 bytes]     - base.cpython-313.pyc [10060 bytes]     - grpc_asyncio.cpython-313.pyc [24990 bytes]     - grpc.cpython-313.pyc [22317 bytes]     - rest_base.cpython-313.pyc [17079 bytes]     - rest.cpython-313.pyc [51623 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\permission_service\__pycache__
    - __init__.cpython-313.pyc [375 bytes]     - async_client.cpython-313.pyc [38731 bytes]     - client.cpython-313.pyc [54369 bytes]     - pagers.cpython-313.pyc [10005 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\permission_service\transports
    - __init__.py [1442 bytes]     - base.py [8895 bytes]     - grpc_asyncio.py [24498 bytes]     - grpc.py [22294 bytes]     - rest_base.py [14775 bytes]     - rest.py [54253 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\permission_service\transports\__pycache__
    - __init__.cpython-313.pyc [801 bytes]     - base.cpython-313.pyc [9321 bytes]     - grpc_asyncio.cpython-313.pyc [23145 bytes]     - grpc.cpython-313.pyc [20624 bytes]     - rest_base.cpython-313.pyc [16932 bytes]     - rest.cpython-313.pyc [46706 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\text_service\__pycache__
    - __init__.cpython-313.pyc [357 bytes]     - async_client.cpython-313.pyc [33833 bytes]     - client.cpython-313.pyc [48856 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\text_service\transports
    - __init__.py [1358 bytes]     - base.py [7784 bytes]     - grpc_asyncio.py [21459 bytes]     - grpc.py [19715 bytes]     - rest_base.py [11563 bytes]     - rest.py [37972 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\services\text_service\transports\__pycache__
    - __init__.cpython-313.pyc [765 bytes]     - base.cpython-313.pyc [8054 bytes]     - grpc_asyncio.cpython-313.pyc [20671 bytes]     - grpc.cpython-313.pyc [18484 bytes]     - rest_base.cpython-313.pyc [13051 bytes]     - rest.cpython-313.pyc [33803 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta3\types\__pycache__
    - __init__.cpython-313.pyc [2294 bytes]     - citation.cpython-313.pyc [3104 bytes]     - discuss_service.cpython-313.pyc [12739 bytes]     - model_service.cpython-313.pyc [11012 bytes]     - model.cpython-313.pyc [4855 bytes]     - permission_service.cpython-313.pyc [7749 bytes]     - permission.cpython-313.pyc [4628 bytes]     - safety.cpython-313.pyc [8965 bytes]     - text_service.cpython-313.pyc [15871 bytes]     - tuned_model.cpython-313.pyc [14599 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api\__pycache__
    - annotations_pb2.cpython-313.pyc [1666 bytes]     - auth_pb2.cpython-313.pyc [2598 bytes]     - backend_pb2.cpython-313.pyc [2694 bytes]     - billing_pb2.cpython-313.pyc [1741 bytes]     - client_pb2.cpython-313.pyc [7768 bytes]     - config_change_pb2.cpython-313.pyc [1963 bytes]     - consumer_pb2.cpython-313.pyc [1914 bytes]     - context_pb2.cpython-313.pyc [1774 bytes]     - control_pb2.cpython-313.pyc [1676 bytes]     - distribution_pb2.cpython-313.pyc [3235 bytes]     - documentation_pb2.cpython-313.pyc [2071 bytes]     - endpoint_pb2.cpython-313.pyc [1574 bytes]     - error_reason_pb2.cpython-313.pyc [2474 bytes]     - field_behavior_pb2.cpython-313.pyc [2004 bytes]     - field_info_pb2.cpython-313.pyc [2111 bytes]     - http_pb2.cpython-313.pyc [2043 bytes]     - httpbody_pb2.cpython-313.pyc [1658 bytes]     - label_pb2.cpython-313.pyc [1717 bytes]     - launch_stage_pb2.cpython-313.pyc [1616 bytes]     - log_pb2.cpython-313.pyc [1697 bytes]     - logging_pb2.cpython-313.pyc [1809 bytes]     - metric_pb2.cpython-313.pyc [3920 bytes]     - monitored_resource_pb2.cpython-313.pyc [2995 bytes]     - monitoring_pb2.cpython-313.pyc [1859 bytes]     - policy_pb2.cpython-313.pyc [2028 bytes]     - quota_pb2.cpython-313.pyc [2522 bytes]     - resource_pb2.cpython-313.pyc [2609 bytes]     - routing_pb2.cpython-313.pyc [1904 bytes]     - service_pb2.cpython-313.pyc [4579 bytes]     - source_info_pb2.cpython-313.pyc [1666 bytes]     - system_parameter_pb2.cpython-313.pyc [1985 bytes]     - usage_pb2.cpython-313.pyc [1768 bytes]     - visibility_pb2.cpython-313.pyc [2359 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\__pycache__
    - __init__.cpython-313.pyc [406 bytes]     - _rest_streaming_base.cpython-313.pyc [4964 bytes]     - bidi.cpython-313.pyc [30313 bytes]     - client_info.cpython-313.pyc [3622 bytes]     - client_logging.cpython-313.pyc [5116 bytes]     - client_options.cpython-313.pyc [6561 bytes]     - datetime_helpers.cpython-313.pyc [11224 bytes]     - exceptions.cpython-313.pyc [29490 bytes]     - extended_operation.cpython-313.pyc [8697 bytes]     - general_helpers.cpython-313.pyc [220 bytes]     - grpc_helpers_async.cpython-313.pyc [17612 bytes]     - grpc_helpers.cpython-313.pyc [24202 bytes]     - iam.cpython-313.pyc [14944 bytes]     - operation_async.cpython-313.pyc [9146 bytes]     - operation.cpython-313.pyc [14256 bytes]     - page_iterator_async.cpython-313.pyc [10506 bytes]     - page_iterator.cpython-313.pyc [21403 bytes]     - path_template.cpython-313.pyc [11822 bytes]     - protobuf_helpers.cpython-313.pyc [12783 bytes]     - rest_helpers.cpython-313.pyc [4132 bytes]     - rest_streaming_async.cpython-313.pyc [4133 bytes]     - rest_streaming.cpython-313.pyc [2848 bytes]     - retry_async.cpython-313.pyc [592 bytes]     - timeout.cpython-313.pyc [10076 bytes]     - universe.cpython-313.pyc [3140 bytes]     - version_header.cpython-313.pyc [671 bytes]     - version.cpython-313.pyc [197 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\future
    - __init__.py [702 bytes]     - _helpers.py [1248 bytes]     - async_future.py [5355 bytes]     - base.py [1763 bytes]     - polling.py [14349 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\gapic_v1
    - __init__.py [988 bytes]     - client_info.py [2341 bytes]     - config_async.py [1728 bytes]     - config.py [6300 bytes]     - method_async.py [2090 bytes]     - method.py [9494 bytes]     - routing_header.py [3093 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\operations_v1
    - __init__.py [1638 bytes]     - abstract_operations_base_client.py [14861 bytes]     - abstract_operations_client.py [16073 bytes]     - operations_async_client.py [14794 bytes]     - operations_client_config.py [2285 bytes]     - operations_client.py [15274 bytes]     - operations_rest_client_async.py [14616 bytes]     - pagers_async.py [2624 bytes]     - pagers_base.py [2652 bytes]     - pagers.py [2463 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\retry
    - __init__.py [2088 bytes]     - retry_base.py [12433 bytes]     - retry_streaming_async.py [14336 bytes]     - retry_streaming.py [10853 bytes]     - retry_unary_async.py [9415 bytes]     - retry_unary.py [13338 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\future\__pycache__
    - __init__.cpython-313.pyc [333 bytes]     - _helpers.cpython-313.pyc [1106 bytes]     - async_future.cpython-313.pyc [6811 bytes]     - base.cpython-313.pyc [2414 bytes]     - polling.cpython-313.pyc [13936 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\gapic_v1\__pycache__
    - __init__.cpython-313.pyc [469 bytes]     - client_info.cpython-313.pyc [2243 bytes]     - config_async.cpython-313.pyc [1437 bytes]     - config.cpython-313.pyc [5941 bytes]     - method_async.cpython-313.pyc [1677 bytes]     - method.cpython-313.pyc [8966 bytes]     - routing_header.cpython-313.pyc [2840 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\operations_v1\__pycache__
    - __init__.cpython-313.pyc [1051 bytes]     - abstract_operations_base_client.cpython-313.pyc [15668 bytes]     - abstract_operations_client.cpython-313.pyc [14875 bytes]     - operations_async_client.cpython-313.pyc [13773 bytes]     - operations_client_config.cpython-313.pyc [1020 bytes]     - operations_client.cpython-313.pyc [14140 bytes]     - operations_rest_client_async.cpython-313.pyc [12800 bytes]     - pagers_async.cpython-313.pyc [3410 bytes]     - pagers_base.cpython-313.pyc [2928 bytes]     - pagers.cpython-313.pyc [3077 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\operations_v1\transports
    - __init__.py [1457 bytes]     - base.py [11419 bytes]     - rest_asyncio.py [24822 bytes]     - rest.py [20599 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\operations_v1\transports\__pycache__
    - __init__.cpython-313.pyc [874 bytes]     - base.cpython-313.pyc [10549 bytes]     - rest_asyncio.cpython-313.pyc [20647 bytes]     - rest.cpython-313.pyc [18596 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\api_core\retry\__pycache__
    - __init__.cpython-313.pyc [1081 bytes]     - retry_base.cpython-313.pyc [12578 bytes]     - retry_streaming_async.cpython-313.pyc [13040 bytes]     - retry_streaming.cpython-313.pyc [10094 bytes]     - retry_unary_async.cpython-313.pyc [9100 bytes]     - retry_unary.cpython-313.pyc [12842 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\__pycache__
    - __init__.cpython-313.pyc [1508 bytes]     - _cloud_sdk.cpython-313.pyc [5014 bytes]     - _credentials_async.cpython-313.pyc [7267 bytes]     - _credentials_base.cpython-313.pyc [2736 bytes]     - _default_async.cpython-313.pyc [10871 bytes]     - _default.cpython-313.pyc [26939 bytes]     - _exponential_backoff.cpython-313.pyc [6418 bytes]     - _helpers.cpython-313.pyc [17341 bytes]     - _jwt_async.cpython-313.pyc [5853 bytes]     - _oauth2client.cpython-313.pyc [6281 bytes]     - _refresh_worker.cpython-313.pyc [4614 bytes]     - _service_account_info.cpython-313.pyc [2822 bytes]     - api_key.cpython-313.pyc [2994 bytes]     - app_engine.cpython-313.pyc [7288 bytes]     - aws.cpython-313.pyc [31870 bytes]     - credentials.cpython-313.pyc [21149 bytes]     - downscoped.cpython-313.pyc [22647 bytes]     - environment_vars.cpython-313.pyc [927 bytes]     - exceptions.cpython-313.pyc [5659 bytes]     - external_account_authorized_user.cpython-313.pyc [16612 bytes]     - external_account.cpython-313.pyc [26051 bytes]     - iam.cpython-313.pyc [5433 bytes]     - identity_pool.cpython-313.pyc [23772 bytes]     - impersonated_credentials.cpython-313.pyc [27004 bytes]     - jwt.cpython-313.pyc [33056 bytes]     - metrics.cpython-313.pyc [4716 bytes]     - pluggable.cpython-313.pyc [17462 bytes]     - version.cpython-313.pyc [193 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\aio
    - __init__.py [869 bytes]     - _helpers.py [2334 bytes]     - credentials.py [5273 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\compute_engine
    - __init__.py [910 bytes]     - _metadata.py [12860 bytes]     - credentials.py [19025 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\crypt
    - __init__.py [3324 bytes]     - _cryptography_rsa.py [5158 bytes]     - _helpers.py [0 bytes]     - _python_rsa.py [6123 bytes]     - base.py [4190 bytes]     - es256.py [6251 bytes]     - rsa.py [1109 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\transport
    - __init__.py [3621 bytes]     - _aiohttp_requests.py [14820 bytes]     - _custom_tls_signer.py [9989 bytes]     - _http_client.py [3798 bytes]     - _mtls_helper.py [14739 bytes]     - _requests_base.py [1657 bytes]     - grpc.py [13931 bytes]     - mtls.py [3968 bytes]     - requests.py [22513 bytes]     - urllib3.py [16491 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\aio\__pycache__
    - __init__.cpython-313.pyc [539 bytes]     - _helpers.cpython-313.pyc [1744 bytes]     - credentials.cpython-313.pyc [6200 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\aio\transport
    - __init__.py [4692 bytes]     - aiohttp.py [6979 bytes]     - sessions.py [10388 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\aio\transport\__pycache__
    - __init__.cpython-313.pyc [5212 bytes]     - aiohttp.cpython-313.pyc [9104 bytes]     - sessions.cpython-313.pyc [11422 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\compute_engine\__pycache__
    - __init__.cpython-313.pyc [486 bytes]     - _metadata.cpython-313.pyc [12418 bytes]     - credentials.cpython-313.pyc [19038 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\crypt\__pycache__
    - __init__.cpython-313.pyc [2850 bytes]     - _cryptography_rsa.cpython-313.pyc [6767 bytes]     - _helpers.cpython-313.pyc [171 bytes]     - _python_rsa.cpython-313.pyc [7901 bytes]     - base.cpython-313.pyc [4794 bytes]     - es256.cpython-313.pyc [8069 bytes]     - rsa.cpython-313.pyc [560 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\auth\transport\__pycache__
    - __init__.cpython-313.pyc [3716 bytes]     - _aiohttp_requests.cpython-313.pyc [16206 bytes]     - _custom_tls_signer.cpython-313.pyc [10657 bytes]     - _http_client.cpython-313.pyc [4734 bytes]     - _mtls_helper.cpython-313.pyc [15010 bytes]     - _requests_base.cpython-313.pyc [1709 bytes]     - grpc.cpython-313.pyc [13583 bytes]     - mtls.cpython-313.pyc [4247 bytes]     - requests.cpython-313.pyc [23851 bytes]     - urllib3.cpython-313.pyc [16902 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\cloud\__pycache__
    - extended_operations_pb2.cpython-313.pyc [2148 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\cloud\location
    - locations_pb2.py [5076 bytes]     - locations_pb2.pyi [3392 bytes]     - locations.proto [3604 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\cloud\location\__pycache__
    - locations_pb2.cpython-313.pyc [3919 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\gapic\metadata
    - gapic_metadata_pb2.py [4770 bytes]     - gapic_metadata_pb2.pyi [4350 bytes]     - gapic_metadata.proto [3393 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\gapic\metadata\__pycache__
    - gapic_metadata_pb2.cpython-313.pyc [3348 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\__pycache__
    - __init__.cpython-313.pyc [2008 bytes]     - answer.cpython-313.pyc [14128 bytes]     - caching.cpython-313.pyc [12583 bytes]     - client.cpython-313.pyc [17270 bytes]     - embedding.cpython-313.pyc [12127 bytes]     - files.cpython-313.pyc [4506 bytes]     - generative_models.cpython-313.pyc [34484 bytes]     - models.cpython-313.pyc [16714 bytes]     - operations.cpython-313.pyc [6151 bytes]     - permission.cpython-313.pyc [6420 bytes]     - protos.cpython-313.pyc [2305 bytes]     - responder.cpython-313.pyc [25999 bytes]     - retriever.cpython-313.pyc [9348 bytes]     - string_utils.cpython-313.pyc [3375 bytes]     - utils.cpython-313.pyc [822 bytes]     - version.cpython-313.pyc [252 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\audio_models
    - __init__.py [0 bytes]     - _audio_models.py [102 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\notebook
    - __init__.py [1169 bytes]     - argument_parser.py [3961 bytes]     - cmd_line_parser.py [20405 bytes]     - command_utils.py [6271 bytes]     - command.py [1535 bytes]     - compare_cmd.py [2566 bytes]     - compile_cmd.py [2365 bytes]     - eval_cmd.py [2756 bytes]     - flag_def.py [17278 bytes]     - gspread_client.py [7711 bytes]     - html_utils.py [1555 bytes]     - input_utils.py [2819 bytes]     - ipython_env_impl.py [1058 bytes]     - ipython_env.py [1686 bytes]     - magics_engine.py [5468 bytes]     - magics.py [4616 bytes]     - model_registry.py [1919 bytes]     - output_utils.py [2085 bytes]     - parsed_args_lib.py [3084 bytes]     - post_process_utils_test_helper.py [991 bytes]     - post_process_utils.py [5803 bytes]     - py_utils.py [2073 bytes]     - run_cmd.py [2744 bytes]     - sheets_id.py [3071 bytes]     - sheets_sanitize_url.py [2977 bytes]     - sheets_utils.py [3964 bytes]     - text_model.py [2640 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\types
    - __init__.py [1153 bytes]     - answer_types.py [2115 bytes]     - caching_types.py [2398 bytes]     - citation_types.py [1229 bytes]     - content_types.py [32180 bytes]     - file_types.py [3960 bytes]     - generation_types.py [26055 bytes]     - helper_types.py [2840 bytes]     - model_types.py [12760 bytes]     - palm_safety_types.py [10393 bytes]     - permission_types.py [16549 bytes]     - retriever_types.py [61271 bytes]     - safety_types.py [10942 bytes]     - text_types.py [982 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\audio_models\__pycache__
    - __init__.cpython-313.pyc [186 bytes]     - _audio_models.cpython-313.pyc [592 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\notebook\__pycache__
    - __init__.cpython-313.pyc [823 bytes]     - argument_parser.cpython-313.pyc [5213 bytes]     - cmd_line_parser.cpython-313.pyc [21387 bytes]     - command_utils.cpython-313.pyc [7081 bytes]     - command.cpython-313.pyc [1687 bytes]     - compare_cmd.cpython-313.pyc [2532 bytes]     - compile_cmd.cpython-313.pyc [2584 bytes]     - eval_cmd.cpython-313.pyc [2703 bytes]     - flag_def.cpython-313.pyc [20259 bytes]     - gspread_client.cpython-313.pyc [9661 bytes]     - html_utils.cpython-313.pyc [1155 bytes]     - input_utils.cpython-313.pyc [3325 bytes]     - ipython_env_impl.cpython-313.pyc [1261 bytes]     - ipython_env.cpython-313.pyc [1761 bytes]     - magics_engine.cpython-313.pyc [5180 bytes]     - magics.cpython-313.pyc [5234 bytes]     - model_registry.cpython-313.pyc [2148 bytes]     - output_utils.cpython-313.pyc [2538 bytes]     - parsed_args_lib.cpython-313.pyc [2852 bytes]     - post_process_utils_test_helper.cpython-313.pyc [1021 bytes]     - post_process_utils.cpython-313.pyc [7708 bytes]     - py_utils.cpython-313.pyc [2462 bytes]     - run_cmd.cpython-313.pyc [2691 bytes]     - sheets_id.cpython-313.pyc [4526 bytes]     - sheets_sanitize_url.cpython-313.pyc [3470 bytes]     - sheets_utils.cpython-313.pyc [5309 bytes]     - text_model.cpython-313.pyc [3121 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\notebook\lib
    - __init__.py [598 bytes]     - llm_function.py [18317 bytes]     - llmfn_input_utils.py [2969 bytes]     - llmfn_inputs_source.py [2443 bytes]     - llmfn_output_row.py [5917 bytes]     - llmfn_outputs.py [8532 bytes]     - llmfn_post_process_cmds.py [8570 bytes]     - llmfn_post_process.py [2470 bytes]     - model.py [2055 bytes]     - prompt_utils.py [1264 bytes]     - unique_fn.py [1487 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\notebook\lib\__pycache__
    - __init__.cpython-313.pyc [186 bytes]     - llm_function.cpython-313.pyc [18648 bytes]     - llmfn_input_utils.cpython-313.pyc [2860 bytes]     - llmfn_inputs_source.cpython-313.pyc [2627 bytes]     - llmfn_output_row.cpython-313.pyc [7760 bytes]     - llmfn_outputs.cpython-313.pyc [9625 bytes]     - llmfn_post_process_cmds.cpython-313.pyc [10427 bytes]     - llmfn_post_process.cpython-313.pyc [1398 bytes]     - model.cpython-313.pyc [2686 bytes]     - prompt_utils.cpython-313.pyc [1059 bytes]     - unique_fn.cpython-313.pyc [1293 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\generativeai\types\__pycache__
    - __init__.cpython-313.pyc [760 bytes]     - answer_types.cpython-313.pyc [2356 bytes]     - caching_types.cpython-313.pyc [2620 bytes]     - citation_types.cpython-313.pyc [1394 bytes]     - content_types.cpython-313.pyc [40134 bytes]     - file_types.cpython-313.pyc [6662 bytes]     - generation_types.cpython-313.pyc [31624 bytes]     - helper_types.cpython-313.pyc [3153 bytes]     - model_types.cpython-313.pyc [17006 bytes]     - palm_safety_types.cpython-313.pyc [13479 bytes]     - permission_types.cpython-313.pyc [18962 bytes]     - retriever_types.cpython-313.pyc [63977 bytes]     - safety_types.cpython-313.pyc [13712 bytes]     - text_types.cpython-313.pyc [1037 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\logging\type
    - http_request_pb2.py [3022 bytes]     - http_request_pb2.pyi [3102 bytes]     - http_request.proto [3601 bytes]     - log_severity_pb2.py [2567 bytes]     - log_severity_pb2.pyi [1378 bytes]     - log_severity.proto [2555 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\logging\type\__pycache__
    - http_request_pb2.cpython-313.pyc [2188 bytes]     - log_severity_pb2.cpython-313.pyc [1857 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\longrunning\__pycache__
    - operations_grpc_pb2.cpython-313.pyc [560 bytes]     - operations_grpc.cpython-313.pyc [292 bytes]     - operations_pb2_grpc.cpython-313.pyc [11789 bytes]     - operations_pb2.cpython-313.pyc [952 bytes]     - operations_proto_pb2.cpython-313.pyc [5565 bytes]     - operations_proto.cpython-313.pyc [294 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\oauth2\__pycache__
    - __init__.cpython-313.pyc [1043 bytes]     - _client_async.cpython-313.pyc [9951 bytes]     - _client.cpython-313.pyc [16629 bytes]     - _credentials_async.cpython-313.pyc [4865 bytes]     - _id_token_async.cpython-313.pyc [10318 bytes]     - _reauth_async.cpython-313.pyc [11609 bytes]     - _service_account_async.cpython-313.pyc [5323 bytes]     - challenges.cpython-313.pyc [12384 bytes]     - credentials.cpython-313.pyc [26440 bytes]     - gdch_credentials.cpython-313.pyc [9310 bytes]     - id_token.cpython-313.pyc [12829 bytes]     - reauth.cpython-313.pyc [12183 bytes]     - service_account.cpython-313.pyc [33819 bytes]     - sts.cpython-313.pyc [6263 bytes]     - utils.cpython-313.pyc [6906 bytes]     - webauthn_handler_factory.cpython-313.pyc [1159 bytes]     - webauthn_handler.cpython-313.pyc [4389 bytes]     - webauthn_types.cpython-313.pyc [7554 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\__pycache__
    - __init__.cpython-313.pyc [198 bytes]     - any_pb2.cpython-313.pyc [1835 bytes]     - any.cpython-313.pyc [1450 bytes]     - api_pb2.cpython-313.pyc [2854 bytes]     - descriptor_database.cpython-313.pyc [6697 bytes]     - descriptor_pb2.cpython-313.pyc [319761 bytes]     - descriptor_pool.cpython-313.pyc [55892 bytes]     - descriptor.cpython-313.pyc [57863 bytes]     - duration_pb2.cpython-313.pyc [1892 bytes]     - duration.cpython-313.pyc [3907 bytes]     - empty_pb2.cpython-313.pyc [1813 bytes]     - field_mask_pb2.cpython-313.pyc [1880 bytes]     - json_format.cpython-313.pyc [43629 bytes]     - message_factory.cpython-313.pyc [8431 bytes]     - message.cpython-313.pyc [14728 bytes]     - proto_builder.cpython-313.pyc [4416 bytes]     - proto_json.cpython-313.pyc [3014 bytes]     - proto.cpython-313.pyc [4027 bytes]     - reflection.cpython-313.pyc [2621 bytes]     - runtime_version.cpython-313.pyc [4306 bytes]     - service_reflection.cpython-313.pyc [12187 bytes]     - service.cpython-313.pyc [9185 bytes]     - source_context_pb2.cpython-313.pyc [1923 bytes]     - struct_pb2.cpython-313.pyc [2796 bytes]     - symbol_database.cpython-313.pyc [8541 bytes]     - text_encoding.cpython-313.pyc [4229 bytes]     - text_format.cpython-313.pyc [76043 bytes]     - timestamp_pb2.cpython-313.pyc [1902 bytes]     - timestamp.cpython-313.pyc [4533 bytes]     - type_pb2.cpython-313.pyc [4279 bytes]     - unknown_fields.cpython-313.pyc [4421 bytes]     - wrappers_pb2.cpython-313.pyc [2876 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\compiler
    - __init__.py [0 bytes]     - plugin_pb2.py [3797 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\internal
    - __init__.py [272 bytes]     - _parameterized.py [14073 bytes]     - api_implementation.py [4787 bytes]     - builder.py [4015 bytes]     - containers.py [21722 bytes]     - decoder.py [38770 bytes]     - encoder.py [27297 bytes]     - enum_type_wrapper.py [3747 bytes]     - extension_dict.py [7225 bytes]     - field_mask.py [10416 bytes]     - message_listener.py [2008 bytes]     - python_edition_defaults.py [434 bytes]     - python_message.py [58234 bytes]     - testing_refleaks.py [4080 bytes]     - type_checkers.py [15471 bytes]     - well_known_types.py [22705 bytes]     - wire_format.py [7087 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\pyext
    - __init__.py [0 bytes]     - cpp_message.py [1715 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\testdata
    - __init__.py [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\util
    - __init__.py [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\compiler\__pycache__
    - __init__.cpython-313.pyc [178 bytes]     - plugin_pb2.cpython-313.pyc [3315 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\internal\__pycache__
    - __init__.cpython-313.pyc [178 bytes]     - _parameterized.cpython-313.pyc [17528 bytes]     - api_implementation.cpython-313.pyc [3361 bytes]     - builder.cpython-313.pyc [5088 bytes]     - containers.cpython-313.pyc [32368 bytes]     - decoder.cpython-313.pyc [38180 bytes]     - encoder.cpython-313.pyc [33016 bytes]     - enum_type_wrapper.cpython-313.pyc [4895 bytes]     - extension_dict.cpython-313.pyc [8863 bytes]     - field_mask.cpython-313.pyc [13914 bytes]     - message_listener.cpython-313.pyc [2410 bytes]     - python_edition_defaults.cpython-313.pyc [497 bytes]     - python_message.cpython-313.pyc [66364 bytes]     - testing_refleaks.cpython-313.pyc [5306 bytes]     - type_checkers.cpython-313.pyc [20369 bytes]     - well_known_types.cpython-313.pyc [31842 bytes]     - wire_format.cpython-313.pyc [8252 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\pyext\__pycache__
    - __init__.cpython-313.pyc [175 bytes]     - cpp_message.cpython-313.pyc [1689 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\testdata\__pycache__
    - __init__.cpython-313.pyc [178 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\protobuf\util\__pycache__
    - __init__.cpython-313.pyc [174 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\rpc\__pycache__
    - code_pb2.cpython-313.pyc [1747 bytes]     - error_details_pb2.cpython-313.pyc [4518 bytes]     - http_pb2.cpython-313.pyc [1864 bytes]     - status_pb2.cpython-313.pyc [1632 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\rpc\context
    - attribute_context_pb2.py [8476 bytes]     - attribute_context_pb2.pyi [11467 bytes]     - attribute_context.proto [14888 bytes]     - audit_context_pb2.py [2416 bytes]     - audit_context_pb2.pyi [1922 bytes]     - audit_context.proto [1829 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\rpc\context\__pycache__
    - attribute_context_pb2.cpython-313.pyc [5622 bytes]     - audit_context_pb2.cpython-313.pyc [1841 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google\type\__pycache__
    - calendar_period_pb2.cpython-313.pyc [1680 bytes]     - color_pb2.cpython-313.pyc [1660 bytes]     - date_pb2.cpython-313.pyc [1497 bytes]     - datetime_pb2.cpython-313.pyc [1955 bytes]     - dayofweek_pb2.cpython-313.pyc [1631 bytes]     - decimal_pb2.cpython-313.pyc [1503 bytes]     - expr_pb2.cpython-313.pyc [1523 bytes]     - fraction_pb2.cpython-313.pyc [1538 bytes]     - interval_pb2.cpython-313.pyc [1709 bytes]     - latlng_pb2.cpython-313.pyc [1514 bytes]     - localized_text_pb2.cpython-313.pyc [1605 bytes]     - money_pb2.cpython-313.pyc [1519 bytes]     - month_pb2.cpython-313.pyc [1630 bytes]     - phone_number_pb2.cpython-313.pyc [1805 bytes]     - postal_address_pb2.cpython-313.pyc [1803 bytes]     - quaternion_pb2.cpython-313.pyc [1570 bytes]     - timeofday_pb2.cpython-313.pyc [1579 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\googleapiclient\__pycache__
    - __init__.cpython-313.pyc [817 bytes]     - _auth.cpython-313.pyc [6091 bytes]     - _helpers.cpython-313.pyc [6874 bytes]     - channel.cpython-313.pyc [11202 bytes]     - discovery.cpython-313.pyc [62231 bytes]     - errors.cpython-313.pyc [8509 bytes]     - http.cpython-313.pyc [69668 bytes]     - mimeparse.cpython-313.pyc [7819 bytes]     - model.cpython-313.pyc [15775 bytes]     - sample_tools.cpython-313.pyc [3846 bytes]     - schema.cpython-313.pyc [12194 bytes]     - version.cpython-313.pyc [198 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\googleapiclient\discovery_cache
    - __init__.py [2315 bytes]     - appengine_memcache.py [1657 bytes]     - base.py [1389 bytes]     - file_cache.py [4774 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\googleapiclient\discovery_cache\__pycache__
    - __init__.cpython-313.pyc [2605 bytes]     - appengine_memcache.cpython-313.pyc [1953 bytes]     - base.cpython-313.pyc [1373 bytes]     - file_cache.cpython-313.pyc [6502 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\googleapiclient\discovery_cache\documents
    - abusiveexperiencereport.v1.json [5671 bytes]     - acceleratedmobilepageurl.v1.json [6729 bytes]     - accessapproval.v1.json [51650 bytes]     - accesscontextmanager.v1.json [117746 bytes]     - accesscontextmanager.v1beta.json [51062 bytes]     - acmedns.v1.json [6858 bytes]     - addressvalidation.v1.json [47675 bytes]     - adexchangebuyer.v1.2.json [20475 bytes]     - adexchangebuyer.v1.3.json [59967 bytes]     - adexchangebuyer.v1.4.json [136987 bytes]     - adexchangebuyer2.v2beta1.json [231740 bytes]     - adexperiencereport.v1.json [6644 bytes]     - admin.datatransfer_v1.json [12027 bytes]     - admin.datatransferv1.json [12027 bytes]     - admin.directory_v1.json [276185 bytes]     - admin.directoryv1.json [276185 bytes]     - admin.reports_v1.json [59681 bytes]     - admin.reportsv1.json [59681 bytes]     - admob.v1.json [52164 bytes]     - admob.v1beta.json [91957 bytes]     - adsense.v2.json [105238 bytes]     - adsensehost.v4.1.json [37965 bytes]     - adsenseplatform.v1.json [20170 bytes]     - adsenseplatform.v1alpha.json [32465 bytes]     - advisorynotifications.v1.json [20673 bytes]     - aiplatform.v1.json [1807281 bytes]     - aiplatform.v1beta1.json [2122222 bytes]     - airquality.v1.json [40963 bytes]     - alertcenter.v1beta1.json [69312 bytes]     - alloydb.v1.json [216911 bytes]     - alloydb.v1alpha.json [223050 bytes]     - alloydb.v1beta.json [222412 bytes]     - analytics.v3.json [195428 bytes]     - analyticsadmin.v1alpha.json [334680 bytes]     - analyticsadmin.v1beta.json [124871 bytes]     - analyticsdata.v1alpha.json [80445 bytes]     - analyticsdata.v1beta.json [97076 bytes]     - analyticshub.v1.json [101922 bytes]     - analyticshub.v1beta1.json [58305 bytes]     - analyticsreporting.v4.json [61333 bytes]     - androiddeviceprovisioning.v1.json [61064 bytes]     - androidenterprise.v1.json [159467 bytes]     - androidmanagement.v1.json [300847 bytes]     - androidpublisher.v3.json [327374 bytes]     - apigateway.v1.json [67624 bytes]     - apigateway.v1beta.json [69337 bytes]     - apigee.v1.json [714633 bytes]     - apigeeregistry.v1.json [156027 bytes]     - apihub.v1.json [236481 bytes]     - apikeys.v2.json [22352 bytes]     - apim.v1alpha.json [48179 bytes]     - appengine.v1.json [162997 bytes]     - appengine.v1alpha.json [63487 bytes]     - appengine.v1beta.json [170673 bytes]     - appengine.v1beta4.json [110912 bytes]     - appengine.v1beta5.json [110227 bytes]     - apphub.v1.json [92728 bytes]     - apphub.v1alpha.json [97346 bytes]     - area120tables.v1alpha1.json [26671 bytes]     - areainsights.v1.json [13619 bytes]     - artifactregistry.v1.json [156305 bytes]     - artifactregistry.v1beta1.json [63804 bytes]     - artifactregistry.v1beta2.json [82401 bytes]     - assuredworkloads.v1.json [70751 bytes]     - assuredworkloads.v1beta1.json [72932 bytes]     - authorizedbuyersmarketplace.v1.json [111045 bytes]     - authorizedbuyersmarketplace.v1alpha.json [119376 bytes]     - backupdr.v1.json [196866 bytes]     - baremetalsolution.v1.json [13574 bytes]     - baremetalsolution.v1alpha1.json [19186 bytes]     - baremetalsolution.v2.json [105818 bytes]     - batch.v1.json [88899 bytes]     - beyondcorp.v1.json [160182 bytes]     - beyondcorp.v1alpha.json [257388 bytes]     - biglake.v1.json [26554 bytes]     - bigquery.v2.json [409876 bytes]     - bigqueryconnection.v1.json [43660 bytes]     - bigqueryconnection.v1beta1.json [32721 bytes]     - bigquerydatapolicy.v1.json [35034 bytes]     - bigquerydatatransfer.v1.json [90564 bytes]     - bigqueryreservation.v1.json [84042 bytes]     - bigqueryreservation.v1alpha2.json [44509 bytes]     - bigqueryreservation.v1beta1.json [60957 bytes]     - bigtableadmin.v1.json [32684 bytes]     - bigtableadmin.v2.json [213459 bytes]     - billingbudgets.v1.json [27319 bytes]     - billingbudgets.v1beta1.json [26850 bytes]     - binaryauthorization.v1.json [85436 bytes]     - binaryauthorization.v1beta1.json [50127 bytes]     - blockchainnodeengine.v1.json [33214 bytes]     - blogger.v2.json [21584 bytes]     - blogger.v3.json [44123 bytes]     - books.v1.json [110062 bytes]     - businessprofileperformance.v1.json [24459 bytes]     - calendar.v3.json [124158 bytes]     - certificatemanager.v1.json [73726 bytes]     - chat.v1.json [291023 bytes]     - checks.v1alpha.json [91808 bytes]     - chromemanagement.v1.json [190404 bytes]     - chromepolicy.v1.json [62781 bytes]     - chromeuxreport.v1.json [22609 bytes]     - civicinfo.v2.json [30776 bytes]     - classroom.v1.json [244569 bytes]     - cloudasset.v1.json [259468 bytes]     - cloudasset.v1beta1.json [95594 bytes]     - cloudasset.v1p1beta1.json [83762 bytes]     - cloudasset.v1p4beta1.json [110574 bytes]     - cloudasset.v1p5beta1.json [82419 bytes]     - cloudasset.v1p7beta1.json [88076 bytes]     - cloudbilling.v1.json [61261 bytes]     - cloudbilling.v1beta.json [98576 bytes]     - cloudbuild.v1.json [197133 bytes]     - cloudbuild.v1alpha1.json [103536 bytes]     - cloudbuild.v1alpha2.json [102139 bytes]     - cloudbuild.v1beta1.json [105268 bytes]     - cloudbuild.v2.json [113912 bytes]     - cloudchannel.v1.json [249733 bytes]     - cloudcommerceprocurement.v1.json [40056 bytes]     - cloudcontrolspartner.v1.json [39795 bytes]     - cloudcontrolspartner.v1beta.json [39907 bytes]     - clouddebugger.v2.json [53446 bytes]     - clouddeploy.v1.json [259765 bytes]     - clouderrorreporting.v1beta1.json [46460 bytes]     - cloudfunctions.v1.json [65107 bytes]     - cloudfunctions.v2.json [86776 bytes]     - cloudfunctions.v2alpha.json [87041 bytes]     - cloudfunctions.v2beta.json [86988 bytes]     - cloudidentity.v1.json [165314 bytes]     - cloudidentity.v1beta1.json [194853 bytes]     - cloudiot.v1.json [94703 bytes]     - cloudkms.v1.json [200979 bytes]     - cloudprofiler.v2.json [14920 bytes]     - cloudresourcemanager.v1.json [101372 bytes]     - cloudresourcemanager.v1beta1.json [58402 bytes]     - cloudresourcemanager.v2.json [52486 bytes]     - cloudresourcemanager.v2beta1.json [52496 bytes]     - cloudresourcemanager.v3.json [128782 bytes]     - cloudscheduler.v1.json [46943 bytes]     - cloudscheduler.v1beta1.json [43153 bytes]     - cloudsearch.v1.json [247310 bytes]     - cloudshell.v1.json [22895 bytes]     - cloudshell.v1alpha1.json [25615 bytes]     - cloudsupport.v2.json [52938 bytes]     - cloudsupport.v2beta.json [58928 bytes]     - cloudtasks.v2.json [96758 bytes]     - cloudtasks.v2beta2.json [113423 bytes]     - cloudtasks.v2beta3.json [103749 bytes]     - cloudtrace.v1.json [13386 bytes]     - cloudtrace.v2.json [21232 bytes]     - cloudtrace.v2beta1.json [11533 bytes]     - composer.v1.json [104740 bytes]     - composer.v1beta1.json [108630 bytes]     - compute.alpha.json [4930377 bytes]     - compute.beta.json [4450061 bytes]     - compute.v1.json [4011105 bytes]     - config.v1.json [101355 bytes]     - connectors.v1.json [262966 bytes]     - connectors.v2.json [71658 bytes]     - contactcenteraiplatform.v1alpha1.json [44191 bytes]     - contactcenterinsights.v1.json [409154 bytes]     - container.v1.json [330312 bytes]     - container.v1beta1.json [356106 bytes]     - containeranalysis.v1.json [234169 bytes]     - containeranalysis.v1alpha1.json [257910 bytes]     - containeranalysis.v1beta1.json [245771 bytes]     - content.v2.1.json [466121 bytes]     - content.v2.json [389993 bytes]     - contentwarehouse.v1.json [226184 bytes]     - css.v1.json [44417 bytes]     - customsearch.v1.json [63076 bytes]     - datacatalog.v1.json [193042 bytes]     - datacatalog.v1beta1.json [182403 bytes]     - dataflow.v1b3.json [270321 bytes]     - dataform.v1beta1.json [148867 bytes]     - datafusion.v1.json [67532 bytes]     - datafusion.v1beta1.json [76296 bytes]     - datalabeling.v1beta1.json [202109 bytes]     - datalineage.v1.json [52426 bytes]     - datamigration.v1.json [239832 bytes]     - datamigration.v1beta1.json [84129 bytes]     - datapipelines.v1.json [38301 bytes]     - dataplex.v1.json [500523 bytes]     - dataportability.v1.json [50408 bytes]     - dataportability.v1beta.json [50464 bytes]     - dataproc.v1.json [434272 bytes]     - dataproc.v1beta2.json [256974 bytes]     - datastore.v1.json [100229 bytes]     - datastore.v1beta1.json [28932 bytes]     - datastore.v1beta3.json [77221 bytes]     - datastream.v1.json [117463 bytes]     - datastream.v1alpha1.json [78971 bytes]     - deploymentmanager.alpha.json [157016 bytes]     - deploymentmanager.v2.json [110758 bytes]     - deploymentmanager.v2beta.json [153106 bytes]     - developerconnect.v1.json [86892 bytes]     - dfareporting.v3.3.json [721039 bytes]     - dfareporting.v3.4.json [748283 bytes]     - dfareporting.v3.5.json [17300 bytes]     - dfareporting.v4.json [562294 bytes]     - dialogflow.v2.json [935625 bytes]     - dialogflow.v2beta1.json [958742 bytes]     - dialogflow.v3.json [794696 bytes]     - dialogflow.v3beta1.json [852902 bytes]     - digitalassetlinks.v1.json [29872 bytes]     - discovery.v1.json [18215 bytes]     - discoveryengine.v1.json [992427 bytes]     - discoveryengine.v1alpha.json [1150354 bytes]     - discoveryengine.v1beta.json [1057075 bytes]     - displayvideo.v1.json [737810 bytes]     - displayvideo.v2.json [784785 bytes]     - displayvideo.v3.json [912701 bytes]     - displayvideo.v4.json [847832 bytes]     - dlp.v2.json [523095 bytes]     - dns.v1.json [121299 bytes]     - dns.v1beta2.json [121683 bytes]     - dns.v2.json [136579 bytes]     - docs.v1.json [179902 bytes]     - documentai.v1.json [217656 bytes]     - documentai.v1beta2.json [213046 bytes]     - documentai.v1beta3.json [248765 bytes]     - domains.v1.json [121515 bytes]     - domains.v1alpha2.json [122168 bytes]     - domains.v1beta1.json [121805 bytes]     - domainsrdap.v1.json [11381 bytes]     - doubleclickbidmanager.v1.1.json [117347 bytes]     - doubleclickbidmanager.v1.json [3347 bytes]     - doubleclickbidmanager.v2.json [21518 bytes]     - doubleclicksearch.v2.json [34911 bytes]     - drive.v2.json [204601 bytes]     - drive.v3.json [175590 bytes]     - driveactivity.v2.json [36966 bytes]     - drivelabels.v2.json [105413 bytes]     - drivelabels.v2beta.json [106837 bytes]     - essentialcontacts.v1.json [34471 bytes]     - eventarc.v1.json [167448 bytes]     - eventarc.v1beta1.json [46836 bytes]     - factchecktools.v1alpha1.json [22449 bytes]     - fcm.v1.json [31200 bytes]     - fcmdata.v1beta1.json [15964 bytes]     - file.v1.json [84557 bytes]     - file.v1beta1.json [97534 bytes]     - firebase.v1beta1.json [116623 bytes]     - firebaseappcheck.v1.json [101098 bytes]     - firebaseappcheck.v1beta.json [119685 bytes]     - firebaseappdistribution.v1.json [65376 bytes]     - firebaseappdistribution.v1alpha.json [56714 bytes]     - firebaseapphosting.v1.json [97764 bytes]     - firebaseapphosting.v1beta.json [100361 bytes]     - firebasedatabase.v1beta.json [15647 bytes]     - firebasedataconnect.v1.json [66425 bytes]     - firebasedataconnect.v1beta.json [66633 bytes]     - firebasedynamiclinks.v1.json [33276 bytes]     - firebasehosting.v1.json [26742 bytes]     - firebasehosting.v1beta1.json [133655 bytes]     - firebaseml.v1.json [10356 bytes]     - firebaseml.v1beta2.json [16713 bytes]     - firebaseml.v2beta.json [70945 bytes]     - firebaserules.v1.json [34267 bytes]     - firebasestorage.v1beta.json [11253 bytes]     - firestore.v1.json [187413 bytes]     - firestore.v1beta1.json [115178 bytes]     - firestore.v1beta2.json [42024 bytes]     - fitness.v1.json [62402 bytes]     - forms.v1.json [53219 bytes]     - games.v1.json [102349 bytes]     - gamesConfiguration.v1configuration.json [19280 bytes]     - gameservices.v1.json [54733 bytes]     - gameservices.v1beta.json [54813 bytes]     - gamesManagement.v1management.json [23171 bytes]     - genomics.v1.json [44128 bytes]     - genomics.v1alpha2.json [66239 bytes]     - genomics.v2alpha1.json [63816 bytes]     - gkebackup.v1.json [190426 bytes]     - gkehub.v1.json [232963 bytes]     - gkehub.v1alpha.json [266053 bytes]     - gkehub.v1alpha2.json [65373 bytes]     - gkehub.v1beta.json [236634 bytes]     - gkehub.v1beta1.json [71143 bytes]     - gkehub.v2.json [109778 bytes]     - gkehub.v2alpha.json [109888 bytes]     - gkehub.v2beta.json [109866 bytes]     - gkeonprem.v1.json [251946 bytes]     - gmail.v1.json [130629 bytes]     - gmailpostmastertools.v1.json [19156 bytes]     - gmailpostmastertools.v1beta1.json [19543 bytes]     - groupsmigration.v1.json [3947 bytes]     - groupssettings.v1.json [24702 bytes]     - healthcare.v1.json [436886 bytes]     - healthcare.v1beta1.json [529308 bytes]     - homegraph.v1.json [19440 bytes]     - iam.v1.json [260451 bytes]     - iam.v2.json [36073 bytes]     - iam.v2beta.json [36197 bytes]     - iamcredentials.v1.json [19185 bytes]     - iap.v1.json [59988 bytes]     - iap.v1beta1.json [20190 bytes]     - ideahub.v1alpha.json [21042 bytes]     - ideahub.v1beta.json [18785 bytes]     - identitytoolkit.v1.json [143803 bytes]     - identitytoolkit.v2.json [136457 bytes]     - identitytoolkit.v3.json [54662 bytes]     - ids.v1.json [25967 bytes]     - index.json [213200 bytes]     - indexing.v3.json [5776 bytes]     - integrations.v1.json [477278 bytes]     - integrations.v1alpha.json [376245 bytes]     - jobs.v2.json [227088 bytes]     - jobs.v3.json [121113 bytes]     - jobs.v3p1beta1.json [135102 bytes]     - jobs.v4.json [135236 bytes]     - keep.v1.json [16998 bytes]     - kgsearch.v1.json [4886 bytes]     - kmsinventory.v1.json [37799 bytes]     - language.v1.json [151394 bytes]     - language.v1beta1.json [38558 bytes]     - language.v1beta2.json [152192 bytes]     - language.v2.json [137860 bytes]     - libraryagent.v1.json [9898 bytes]     - licensing.v1.json [15827 bytes]     - lifesciences.v2beta.json [52568 bytes]     - localservices.v1.json [18779 bytes]     - logging.v2.json [498590 bytes]     - looker.v1.json [45211 bytes]     - managedidentities.v1.json [118816 bytes]     - managedidentities.v1alpha1.json [118842 bytes]     - managedidentities.v1beta1.json [119795 bytes]     - managedkafka.v1.json [146921 bytes]     - manufacturers.v1.json [41060 bytes]     - marketingplatformadmin.v1alpha.json [12562 bytes]     - meet.v2.json [43604 bytes]     - memcache.v1.json [64890 bytes]     - memcache.v1beta2.json [66816 bytes]     - merchantapi.accounts_v1beta.json [154981 bytes]     - merchantapi.conversions_v1beta.json [20647 bytes]     - merchantapi.datasources_v1beta.json [36561 bytes]     - merchantapi.inventories_v1beta.json [24171 bytes]     - merchantapi.issueresolution_v1beta.json [43058 bytes]     - merchantapi.lfp_v1beta.json [28854 bytes]     - merchantapi.notifications_v1beta.json [15646 bytes]     - merchantapi.ordertracking_v1beta.json [18900 bytes]     - merchantapi.products_v1beta.json [64562 bytes]     - merchantapi.promotions_v1beta.json [39935 bytes]     - merchantapi.quota_v1beta.json [11693 bytes]     - merchantapi.reports_v1beta.json [58778 bytes]     - merchantapi.reviews_v1beta.json [42743 bytes]     - metastore.v1.json [141081 bytes]     - metastore.v1alpha.json [151197 bytes]     - metastore.v1beta.json [151091 bytes]     - metastore.v2.json [66531 bytes]     - metastore.v2alpha.json [87673 bytes]     - metastore.v2beta.json [87517 bytes]     - migrationcenter.v1.json [208136 bytes]     - migrationcenter.v1alpha1.json [267736 bytes]     - ml.v1.json [175519 bytes]     - monitoring.v1.json [113569 bytes]     - monitoring.v3.json [342651 bytes]     - mybusinessaccountmanagement.v1.json [31906 bytes]     - mybusinessbusinesscalls.v1.json [16209 bytes]     - mybusinessbusinessinformation.v1.json [66245 bytes]     - mybusinesslodging.v1.json [222770 bytes]     - mybusinessnotifications.v1.json [8029 bytes]     - mybusinessplaceactions.v1.json [14578 bytes]     - mybusinessqanda.v1.json [14263 bytes]     - mybusinessverifications.v1.json [25465 bytes]     - netapp.v1.json [131753 bytes]     - netapp.v1beta1.json [133262 bytes]     - networkconnectivity.v1.json [233071 bytes]     - networkconnectivity.v1alpha1.json [80820 bytes]     - networkmanagement.v1.json [141409 bytes]     - networkmanagement.v1beta1.json [148526 bytes]     - networksecurity.v1.json [311393 bytes]     - networksecurity.v1beta1.json [322805 bytes]     - networkservices.v1.json [236000 bytes]     - networkservices.v1beta1.json [223373 bytes]     - notebooks.v1.json [159045 bytes]     - notebooks.v2.json [76406 bytes]     - oauth2.v2.json [6601 bytes]     - observability.v1.json [18284 bytes]     - ondemandscanning.v1.json [93835 bytes]     - ondemandscanning.v1beta1.json [93686 bytes]     - oracledatabase.v1.json [121767 bytes]     - orgpolicy.v2.json [55571 bytes]     - osconfig.v1.json [141498 bytes]     - osconfig.v1alpha.json [106049 bytes]     - osconfig.v1beta.json [87545 bytes]     - osconfig.v2.json [88480 bytes]     - osconfig.v2beta.json [88860 bytes]     - oslogin.v1.json [13119 bytes]     - oslogin.v1alpha.json [21875 bytes]     - oslogin.v1beta.json [20960 bytes]     - pagespeedonline.v5.json [28503 bytes]     - parallelstore.v1.json [36722 bytes]     - parallelstore.v1beta.json [36617 bytes]     - paymentsresellersubscription.v1.json [68287 bytes]     - people.v1.json [112570 bytes]     - places.v1.json [109962 bytes]     - playablelocations.v3.json [25906 bytes]     - playcustomapp.v1.json [5201 bytes]     - playdeveloperreporting.v1alpha1.json [148544 bytes]     - playdeveloperreporting.v1beta1.json [148331 bytes]     - playgrouping.v1alpha1.json [6307 bytes]     - playintegrity.v1.json [19484 bytes]     - policyanalyzer.v1.json [11965 bytes]     - policyanalyzer.v1beta1.json [10922 bytes]     - policysimulator.v1.json [64387 bytes]     - policysimulator.v1alpha.json [37556 bytes]     - policysimulator.v1beta.json [37532 bytes]     - policysimulator.v1beta1.json [54107 bytes]     - policytroubleshooter.v1.json [31718 bytes]     - policytroubleshooter.v1beta.json [30156 bytes]     - pollen.v1.json [21526 bytes]     - poly.v1.json [27091 bytes]     - privateca.v1.json [150168 bytes]     - privateca.v1beta1.json [44228 bytes]     - prod_tt_sasportal.v1alpha1.json [100005 bytes]     - publicca.v1.json [4849 bytes]     - publicca.v1alpha1.json [4873 bytes]     - publicca.v1beta1.json [4869 bytes]     - pubsub.v1.json [131361 bytes]     - pubsub.v1beta1a.json [26028 bytes]     - pubsub.v1beta2.json [51138 bytes]     - pubsublite.v1.json [53052 bytes]     - rapidmigrationassessment.v1.json [32706 bytes]     - readerrevenuesubscriptionlinking.v1.json [8706 bytes]     - realtimebidding.v1.json [118786 bytes]     - realtimebidding.v1alpha.json [18365 bytes]     - recaptchaenterprise.v1.json [97469 bytes]     - recommendationengine.v1beta1.json [87898 bytes]     - recommender.v1.json [98951 bytes]     - recommender.v1beta1.json [111437 bytes]     - redis.v1.json [163873 bytes]     - redis.v1beta1.json [164743 bytes]     - remotebuildexecution.v1.json [117609 bytes]     - remotebuildexecution.v1alpha.json [124864 bytes]     - remotebuildexecution.v2.json [148991 bytes]     - reseller.v1.json [49119 bytes]     - resourcesettings.v1.json [22442 bytes]     - retail.v2.json [371391 bytes]     - retail.v2alpha.json [431256 bytes]     - retail.v2beta.json [390600 bytes]     - run.v1.json [258417 bytes]     - run.v1alpha1.json [69965 bytes]     - run.v1beta1.json [40411 bytes]     - run.v2.json [264796 bytes]     - runtimeconfig.v1.json [10289 bytes]     - runtimeconfig.v1beta1.json [55381 bytes]     - safebrowsing.v4.json [38346 bytes]     - safebrowsing.v5.json [9639 bytes]     - sasportal.v1alpha1.json [99260 bytes]     - script.v1.json [51327 bytes]     - searchads360.v0.json [332283 bytes]     - searchconsole.v1.json [40740 bytes]     - secretmanager.v1.json [75801 bytes]     - secretmanager.v1beta1.json [47622 bytes]     - secretmanager.v1beta2.json [76127 bytes]     - securitycenter.v1.json [593055 bytes]     - securitycenter.v1beta1.json [351156 bytes]     - securitycenter.v1beta2.json [373975 bytes]     - securityposture.v1.json [71190 bytes]     - serviceconsumermanagement.v1.json [161383 bytes]     - serviceconsumermanagement.v1beta1.json [168625 bytes]     - servicecontrol.v1.json [98463 bytes]     - servicecontrol.v2.json [54820 bytes]     - servicedirectory.v1.json [54438 bytes]     - servicedirectory.v1beta1.json [58940 bytes]     - servicemanagement.v1.json [182009 bytes]     - servicenetworking.v1.json [196123 bytes]     - servicenetworking.v1beta.json [148793 bytes]     - serviceusage.v1.json [167478 bytes]     - serviceusage.v1beta1.json [205076 bytes]     - sheets.v4.json [297169 bytes]     - siteVerification.v1.json [8419 bytes]     - slides.v1.json [185359 bytes]     - smartdevicemanagement.v1.json [12914 bytes]     - solar.v1.json [38899 bytes]     - sourcerepo.v1.json [38672 bytes]     - spanner.v1.json [365481 bytes]     - speech.v1.json [64012 bytes]     - speech.v1p1beta1.json [65166 bytes]     - speech.v2beta1.json [18493 bytes]     - sqladmin.v1.json [237036 bytes]     - sqladmin.v1beta4.json [238363 bytes]     - storage.v1.json [196524 bytes]     - storagebatchoperations.v1.json [38891 bytes]     - storagetransfer.v1.json [85616 bytes]     - streetviewpublish.v1.json [48408 bytes]     - sts.v1.json [33115 bytes]     - sts.v1beta.json [31742 bytes]     - tagmanager.v1.json [75026 bytes]     - tagmanager.v2.json [158728 bytes]     - tasks.v1.json [23605 bytes]     - testing.v1.json [97369 bytes]     - texttospeech.v1.json [29258 bytes]     - texttospeech.v1beta1.json [28097 bytes]     - toolresults.v1beta3.json [129592 bytes]     - tpu.v1.json [32402 bytes]     - tpu.v1alpha1.json [32817 bytes]     - tpu.v2.json [57594 bytes]     - tpu.v2alpha1.json [69164 bytes]     - trafficdirector.v2.json [32631 bytes]     - trafficdirector.v3.json [51706 bytes]     - transcoder.v1.json [68273 bytes]     - transcoder.v1beta1.json [60514 bytes]     - translate.v2.json [11984 bytes]     - translate.v3.json [119777 bytes]     - translate.v3beta1.json [69036 bytes]     - travelimpactmodel.v1.json [9794 bytes]     - vault.v1.json [77064 bytes]     - vectortile.v1.json [47652 bytes]     - verifiedaccess.v1.json [6762 bytes]     - verifiedaccess.v2.json [20018 bytes]     - versionhistory.v1.json [17208 bytes]     - videointelligence.v1.json [177773 bytes]     - videointelligence.v1beta2.json [169935 bytes]     - videointelligence.v1p1beta1.json [170011 bytes]     - videointelligence.v1p2beta1.json [170011 bytes]     - videointelligence.v1p3beta1.json [170062 bytes]     - vision.v1.json [313435 bytes]     - vision.v1p1beta1.json [278159 bytes]     - vision.v1p2beta1.json [278159 bytes]     - vmmigration.v1.json [187544 bytes]     - vmmigration.v1alpha1.json [195147 bytes]     - vmwareengine.v1.json [281934 bytes]     - vpcaccess.v1.json [21403 bytes]     - vpcaccess.v1beta1.json [21802 bytes]     - walletobjects.v1.json [355331 bytes]     - webfonts.v1.json [6487 bytes]     - webmasters.v3.json [20097 bytes]     - webrisk.v1.json [26588 bytes]     - websecurityscanner.v1.json [44775 bytes]     - websecurityscanner.v1alpha.json [36660 bytes]     - websecurityscanner.v1beta.json [42878 bytes]     - workflowexecutions.v1.json [39175 bytes]     - workflowexecutions.v1beta.json [14318 bytes]     - workflows.v1.json [28821 bytes]     - workflows.v1beta.json [22686 bytes]     - workloadmanager.v1.json [87843 bytes]     - workspaceevents.v1.json [30767 bytes]     - workstations.v1.json [106141 bytes]     - workstations.v1beta.json [105881 bytes]     - youtube.v3.json [374965 bytes]     - youtubeAnalytics.v1.json [3220 bytes]     - youtubeAnalytics.v2.json [28888 bytes]     - youtubereporting.v1.json [23094 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\google_generativeai-0.8.5.dist-info\licenses
    - LICENSE [11358 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet\__pycache__
    - __init__.cpython-313.pyc [1050 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet\platform
    - __init__.py [0 bytes]     - setup_switch_x64_masm.cmd [143 bytes]     - switch_aarch64_gcc.h [4307 bytes]     - switch_alpha_unix.h [671 bytes]     - switch_amd64_unix.h [2748 bytes]     - switch_arm32_gcc.h [2479 bytes]     - switch_arm32_ios.h [1892 bytes]     - switch_arm64_masm.asm [1245 bytes]     - switch_arm64_masm.obj [746 bytes]     - switch_arm64_msvc.h [398 bytes]     - switch_csky_gcc.h [1331 bytes]     - switch_loongarch64_linux.h [779 bytes]     - switch_m68k_gcc.h [928 bytes]     - switch_mips_unix.h [1426 bytes]     - switch_ppc_aix.h [2941 bytes]     - switch_ppc_linux.h [2759 bytes]     - switch_ppc_macosx.h [2624 bytes]     - switch_ppc_unix.h [2652 bytes]     - switch_ppc64_aix.h [3860 bytes]     - switch_ppc64_linux.h [3815 bytes]     - switch_riscv_unix.h [949 bytes]     - switch_s390_unix.h [2763 bytes]     - switch_sh_gcc.h [901 bytes]     - switch_sparc_sun_gcc.h [2797 bytes]     - switch_x32_unix.h [1509 bytes]     - switch_x64_masm.asm [1841 bytes]     - switch_x64_masm.obj [1078 bytes]     - switch_x64_msvc.h [1805 bytes]     - switch_x86_msvc.h [12838 bytes]     - switch_x86_unix.h [3059 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet\tests
    - __init__.py [9361 bytes]     - _test_extension_cpp.cp313-win_amd64.pyd [15872 bytes]     - _test_extension_cpp.cpp [6565 bytes]     - _test_extension.c [5773 bytes]     - _test_extension.cp313-win_amd64.pyd [14336 bytes]     - fail_clearing_run_switches.py [1263 bytes]     - fail_cpp_exception.py [985 bytes]     - fail_initialstub_already_started.py [1961 bytes]     - fail_slp_switch.py [524 bytes]     - fail_switch_three_greenlets.py [956 bytes]     - fail_switch_three_greenlets2.py [1285 bytes]     - fail_switch_two_greenlets.py [817 bytes]     - leakcheck.py [11964 bytes]     - test_contextvars.py [10541 bytes]     - test_cpp.py [2736 bytes]     - test_extension_interface.py [3829 bytes]     - test_gc.py [2923 bytes]     - test_generator_nested.py [3718 bytes]     - test_generator.py [1240 bytes]     - test_greenlet_trash.py [7947 bytes]     - test_greenlet.py [46251 bytes]     - test_leaks.py [17714 bytes]     - test_stack_saved.py [446 bytes]     - test_throw.py [3712 bytes]     - test_tracing.py [8250 bytes]     - test_version.py [1339 bytes]     - test_weakref.py [883 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet\platform\__pycache__
    - __init__.cpython-313.pyc [171 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet\tests\__pycache__
    - __init__.cpython-313.pyc [9239 bytes]     - fail_clearing_run_switches.cpython-313.pyc [2132 bytes]     - fail_cpp_exception.cpython-313.pyc [1597 bytes]     - fail_initialstub_already_started.cpython-313.pyc [3617 bytes]     - fail_slp_switch.cpython-313.pyc [1286 bytes]     - fail_switch_three_greenlets.cpython-313.pyc [1710 bytes]     - fail_switch_three_greenlets2.cpython-313.pyc [2564 bytes]     - fail_switch_two_greenlets.cpython-313.pyc [1679 bytes]     - leakcheck.cpython-313.pyc [11919 bytes]     - test_contextvars.cpython-313.pyc [15609 bytes]     - test_cpp.cpython-313.pyc [4149 bytes]     - test_extension_interface.cpython-313.pyc [7558 bytes]     - test_gc.cpython-313.pyc [5074 bytes]     - test_generator_nested.cpython-313.pyc [8007 bytes]     - test_generator.cpython-313.pyc [3222 bytes]     - test_greenlet_trash.cpython-313.pyc [6780 bytes]     - test_greenlet.cpython-313.pyc [75951 bytes]     - test_leaks.cpython-313.pyc [19966 bytes]     - test_stack_saved.cpython-313.pyc [1387 bytes]     - test_throw.cpython-313.pyc [7421 bytes]     - test_tracing.cpython-313.pyc [13891 bytes]     - test_version.cpython-313.pyc [2638 bytes]     - test_weakref.cpython-313.pyc [2794 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\greenlet-3.2.2.dist-info\licenses
    - LICENSE [1434 bytes]     - LICENSE.PSF [2424 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\__pycache__
    - __init__.cpython-313.pyc [85831 bytes]     - _auth.cpython-313.pyc [3241 bytes]     - _channel.cpython-313.pyc [99484 bytes]     - _common.cpython-313.pyc [9132 bytes]     - _compression.cpython-313.pyc [1996 bytes]     - _grpcio_metadata.cpython-313.pyc [193 bytes]     - _interceptor.cpython-313.pyc [31204 bytes]     - _observability.cpython-313.pyc [12254 bytes]     - _plugin_wrapping.cpython-313.pyc [5914 bytes]     - _runtime_protos.cpython-313.pyc [5802 bytes]     - _server.cpython-313.pyc [64757 bytes]     - _simple_stubs.cpython-313.pyc [24508 bytes]     - _typing.cpython-313.pyc [2232 bytes]     - _utilities.cpython-313.pyc [10814 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\_cython
    - __init__.py [590 bytes]     - cygrpc.cp313-win_amd64.pyd [9265152 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\aio
    - __init__.py [3255 bytes]     - _base_call.py [7817 bytes]     - _base_channel.py [14257 bytes]     - _base_server.py [12945 bytes]     - _call.py [26120 bytes]     - _channel.py [22726 bytes]     - _interceptor.py [42523 bytes]     - _metadata.py [5146 bytes]     - _server.py [9170 bytes]     - _typing.py [1421 bytes]     - _utils.py [843 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\beta
    - __init__.py [590 bytes]     - _client_adaptations.py [28038 bytes]     - _metadata.py [1694 bytes]     - _server_adaptations.py [15076 bytes]     - implementations.py [12403 bytes]     - interfaces.py [6245 bytes]     - utilities.py [5158 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\experimental
    - __init__.py [4237 bytes]     - gevent.py [1000 bytes]     - session_cache.py [1578 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework
    - __init__.py [590 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\aio\__pycache__
    - __init__.cpython-313.pyc [2135 bytes]     - _base_call.cpython-313.pyc [8601 bytes]     - _base_channel.cpython-313.pyc [14857 bytes]     - _base_server.cpython-313.pyc [14675 bytes]     - _call.cpython-313.pyc [35912 bytes]     - _channel.cpython-313.pyc [22155 bytes]     - _interceptor.cpython-313.pyc [48769 bytes]     - _metadata.cpython-313.pyc [7586 bytes]     - _server.cpython-313.pyc [9900 bytes]     - _typing.cpython-313.pyc [1137 bytes]     - _utils.cpython-313.pyc [552 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\beta\__pycache__
    - __init__.cpython-313.pyc [163 bytes]     - _client_adaptations.cpython-313.pyc [28473 bytes]     - _metadata.cpython-313.pyc [2022 bytes]     - _server_adaptations.cpython-313.pyc [19365 bytes]     - implementations.cpython-313.pyc [11785 bytes]     - interfaces.cpython-313.pyc [6993 bytes]     - utilities.cpython-313.pyc [7259 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\experimental\__pycache__
    - __init__.cpython-313.pyc [4458 bytes]     - gevent.cpython-313.pyc [691 bytes]     - session_cache.cpython-313.pyc [1678 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\experimental\aio
    - __init__.py [676 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\experimental\aio\__pycache__
    - __init__.cpython-313.pyc [274 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\__pycache__
    - __init__.cpython-313.pyc [168 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\common
    - __init__.py [590 bytes]     - cardinality.py [1014 bytes]     - style.py [848 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\foundation
    - __init__.py [590 bytes]     - abandonment.py [900 bytes]     - callable_util.py [3249 bytes]     - future.py [8592 bytes]     - logging_pool.py [2320 bytes]     - stream_util.py [4920 bytes]     - stream.py [1420 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\interfaces
    - __init__.py [590 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\common\__pycache__
    - __init__.cpython-313.pyc [175 bytes]     - cardinality.cpython-313.pyc [900 bytes]     - style.cpython-313.pyc [724 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\foundation\__pycache__
    - __init__.cpython-313.pyc [179 bytes]     - abandonment.cpython-313.pyc [700 bytes]     - callable_util.cpython-313.pyc [3873 bytes]     - future.cpython-313.pyc [7421 bytes]     - logging_pool.cpython-313.pyc [3141 bytes]     - stream_util.cpython-313.pyc [8051 bytes]     - stream.cpython-313.pyc [1510 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\interfaces\__pycache__
    - __init__.cpython-313.pyc [179 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\interfaces\base
    - __init__.py [590 bytes]     - base.py [12562 bytes]     - utilities.py [2445 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\interfaces\face
    - __init__.py [590 bytes]     - face.py [40784 bytes]     - utilities.py [7022 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\interfaces\base\__pycache__
    - __init__.cpython-313.pyc [184 bytes]     - base.cpython-313.pyc [14255 bytes]     - utilities.cpython-313.pyc [2392 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\framework\interfaces\face\__pycache__
    - __init__.cpython-313.pyc [184 bytes]     - face.cpython-313.pyc [41295 bytes]     - utilities.cpython-313.pyc [6669 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\_cython\__pycache__
    - __init__.cpython-313.pyc [166 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\_cython\_credentials
    - roots.pem [268777 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\_cython\_cygrpc
    - __init__.py [590 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc\_cython\_cygrpc\__pycache__
    - __init__.cpython-313.pyc [174 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\grpc_status\__pycache__
    - __init__.cpython-313.pyc [165 bytes]     - _async.cpython-313.pyc [1919 bytes]     - _common.cpython-313.pyc [813 bytes]     - rpc_status.cpython-313.pyc [3228 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\h11\__pycache__
    - __init__.cpython-313.pyc [1048 bytes]     - _abnf.cpython-313.pyc [1740 bytes]     - _connection.cpython-313.pyc [22715 bytes]     - _events.cpython-313.pyc [13120 bytes]     - _headers.cpython-313.pyc [8020 bytes]     - _readers.cpython-313.pyc [9846 bytes]     - _receivebuffer.cpython-313.pyc [4755 bytes]     - _state.cpython-313.pyc [8755 bytes]     - _util.cpython-313.pyc [4818 bytes]     - _version.cpython-313.pyc [186 bytes]     - _writers.cpython-313.pyc [6446 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\h11-0.16.0.dist-info\licenses
    - LICENSE.txt [1124 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\httplib2\__pycache__
    - __init__.cpython-313.pyc [74208 bytes]     - auth.cpython-313.pyc [3938 bytes]     - certs.cpython-313.pyc [1575 bytes]     - error.cpython-313.pyc [2434 bytes]     - iri2uri.cpython-313.pyc [4193 bytes]     - socks.cpython-313.pyc [23742 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\idna\__pycache__
    - __init__.cpython-313.pyc [856 bytes]     - codec.cpython-313.pyc [5280 bytes]     - compat.cpython-313.pyc [866 bytes]     - core.cpython-313.pyc [16902 bytes]     - idnadata.cpython-313.pyc [99446 bytes]     - intranges.cpython-313.pyc [2579 bytes]     - package_data.cpython-313.pyc [187 bytes]     - uts46data.cpython-313.pyc [158980 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\iniconfig\__pycache__
    - __init__.cpython-313.pyc [8873 bytes]     - _parse.cpython-313.pyc [3401 bytes]     - _version.cpython-313.pyc [634 bytes]     - exceptions.cpython-313.pyc [1314 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\iniconfig-2.1.0.dist-info\licenses
    - LICENSE [1098 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi\__pycache__
    - __init__.cpython-313.pyc [1784 bytes]     - install.cpython-313.pyc [33735 bytes]     - isapicon.cpython-313.pyc [3390 bytes]     - simple.cpython-313.pyc [3575 bytes]     - threaded_extension.cpython-313.pyc [8279 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi\doc
    - isapi.html [4239 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi\samples
    - advanced.py [8124 bytes]     - README.txt [1023 bytes]     - redirector_asynch.py [2812 bytes]     - redirector_with_filter.py [6574 bytes]     - redirector.py [4587 bytes]     - test.py [6513 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi\test
    - extension_simple.py [4339 bytes]     - README.txt [115 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi\samples\__pycache__
    - advanced.cpython-313.pyc [7145 bytes]     - redirector_asynch.cpython-313.pyc [3130 bytes]     - redirector_with_filter.cpython-313.pyc [4271 bytes]     - redirector.cpython-313.pyc [3659 bytes]     - test.cpython-313.pyc [7440 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\isapi\test\__pycache__
    - extension_simple.cpython-313.pyc [4579 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako\__pycache__
    - __init__.cpython-313.pyc [187 bytes]     - _ast_util.cpython-313.pyc [36703 bytes]     - ast.cpython-313.pyc [7819 bytes]     - cache.cpython-313.pyc [8175 bytes]     - cmd.cpython-313.pyc [3736 bytes]     - codegen.cpython-313.pyc [60356 bytes]     - compat.cpython-313.pyc [3113 bytes]     - exceptions.cpython-313.pyc [14995 bytes]     - filters.cpython-313.pyc [6719 bytes]     - lexer.cpython-313.pyc [21054 bytes]     - lookup.cpython-313.pyc [13376 bytes]     - parsetree.cpython-313.pyc [30812 bytes]     - pygen.cpython-313.pyc [11379 bytes]     - pyparser.cpython-313.pyc [12550 bytes]     - runtime.cpython-313.pyc [38964 bytes]     - template.cpython-313.pyc [26485 bytes]     - util.cpython-313.pyc [20649 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako\ext
    - __init__.py [0 bytes]     - autohandler.py [1885 bytes]     - babelplugin.py [2091 bytes]     - beaker_cache.py [2578 bytes]     - extract.py [4659 bytes]     - linguaplugin.py [1935 bytes]     - preprocessors.py [576 bytes]     - pygmentplugin.py [4753 bytes]     - turbogears.py [2141 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako\testing
    - __init__.py [0 bytes]     - _config.py [3566 bytes]     - assertions.py [5161 bytes]     - config.py [323 bytes]     - exclusions.py [1553 bytes]     - fixtures.py [3044 bytes]     - helpers.py [1623 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako\ext\__pycache__
    - __init__.cpython-313.pyc [162 bytes]     - autohandler.cpython-313.pyc [2412 bytes]     - babelplugin.cpython-313.pyc [2658 bytes]     - beaker_cache.cpython-313.pyc [3962 bytes]     - extract.cpython-313.pyc [5448 bytes]     - linguaplugin.cpython-313.pyc [2721 bytes]     - preprocessors.cpython-313.pyc [647 bytes]     - pygmentplugin.cpython-313.pyc [6028 bytes]     - turbogears.cpython-313.pyc [2552 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako\testing\__pycache__
    - __init__.cpython-313.pyc [166 bytes]     - _config.cpython-313.pyc [6002 bytes]     - assertions.cpython-313.pyc [5961 bytes]     - config.cpython-313.pyc [838 bytes]     - exclusions.cpython-313.pyc [2377 bytes]     - fixtures.cpython-313.pyc [4872 bytes]     - helpers.cpython-313.pyc [3578 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\mako-1.3.10.dist-info\licenses
    - LICENSE [1098 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\markupsafe\__pycache__
    - __init__.cpython-313.pyc [21234 bytes]     - _native.cpython-313.pyc [593 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\multipart\__pycache__
    - __init__.cpython-313.pyc [1420 bytes]     - decoders.cpython-313.pyc [210 bytes]     - exceptions.cpython-313.pyc [214 bytes]     - multipart.cpython-313.pyc [212 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\packaging\__pycache__
    - __init__.cpython-313.pyc [528 bytes]     - _elffile.cpython-313.pyc [5187 bytes]     - _manylinux.cpython-313.pyc [9974 bytes]     - _musllinux.cpython-313.pyc [4593 bytes]     - _parser.cpython-313.pyc [14157 bytes]     - _structures.cpython-313.pyc [3323 bytes]     - _tokenizer.cpython-313.pyc [8080 bytes]     - markers.cpython-313.pyc [13070 bytes]     - metadata.cpython-313.pyc [27339 bytes]     - requirements.cpython-313.pyc [4604 bytes]     - specifiers.cpython-313.pyc [37612 bytes]     - tags.cpython-313.pyc [24944 bytes]     - utils.cpython-313.pyc [6728 bytes]     - version.cpython-313.pyc [19941 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\packaging\licenses
    - __init__.py [5715 bytes]     - _spdx.py [48398 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\packaging\licenses\__pycache__
    - __init__.cpython-313.pyc [4269 bytes]     - _spdx.cpython-313.pyc [47410 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\packaging-25.0.dist-info\licenses
    - LICENSE [197 bytes]     - LICENSE.APACHE [10174 bytes]     - LICENSE.BSD [1344 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\__pycache__
    - __init__.cpython-313.pyc [664 bytes]     - __main__.cpython-313.pyc [826 bytes]     - __pip-runner__.cpython-313.pyc [2263 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal
    - __init__.py [513 bytes]     - build_env.py [10584 bytes]     - cache.py [10369 bytes]     - configuration.py [14006 bytes]     - exceptions.py [26481 bytes]     - main.py [340 bytes]     - pyproject.py [7287 bytes]     - self_outdated_check.py [8145 bytes]     - wheel_builder.py [11799 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor
    - __init__.py [4873 bytes]     - typing_extensions.py [134499 bytes]     - vendor.txt [332 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\__pycache__
    - __init__.cpython-313.pyc [758 bytes]     - build_env.cpython-313.pyc [14825 bytes]     - cache.cpython-313.pyc [12878 bytes]     - configuration.cpython-313.pyc [17830 bytes]     - exceptions.cpython-313.pyc [37512 bytes]     - main.cpython-313.pyc [643 bytes]     - pyproject.cpython-313.pyc [5202 bytes]     - self_outdated_check.cpython-313.pyc [10347 bytes]     - wheel_builder.cpython-313.pyc [13941 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\cli
    - __init__.py [132 bytes]     - autocompletion.py [6865 bytes]     - base_command.py [8289 bytes]     - cmdoptions.py [30110 bytes]     - command_context.py [774 bytes]     - index_command.py [5631 bytes]     - main_parser.py [4338 bytes]     - main.py [2817 bytes]     - parser.py [10825 bytes]     - progress_bars.py [2723 bytes]     - req_command.py [12250 bytes]     - spinners.py [5118 bytes]     - status_codes.py [116 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\commands
    - __init__.py [3882 bytes]     - cache.py [7944 bytes]     - check.py [2268 bytes]     - completion.py [4287 bytes]     - configuration.py [9766 bytes]     - debug.py [6797 bytes]     - download.py [5273 bytes]     - freeze.py [3203 bytes]     - hash.py [1703 bytes]     - help.py [1132 bytes]     - index.py [4731 bytes]     - inspect.py [3189 bytes]     - install.py [29428 bytes]     - list.py [12769 bytes]     - search.py [5626 bytes]     - show.py [7507 bytes]     - uninstall.py [3892 bytes]     - wheel.py [6414 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\distributions
    - __init__.py [858 bytes]     - base.py [1783 bytes]     - installed.py [842 bytes]     - sdist.py [6751 bytes]     - wheel.py [1317 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\index
    - __init__.py [30 bytes]     - collector.py [16265 bytes]     - package_finder.py [37666 bytes]     - sources.py [8632 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\locations
    - __init__.py [14925 bytes]     - _distutils.py [6013 bytes]     - _sysconfig.py [7724 bytes]     - base.py [2556 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\metadata
    - __init__.py [4339 bytes]     - _json.py [2644 bytes]     - base.py [25298 bytes]     - pkg_resources.py [10542 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\models
    - __init__.py [63 bytes]     - candidate.py [753 bytes]     - direct_url.py [6578 bytes]     - format_control.py [2486 bytes]     - index.py [1030 bytes]     - installation_report.py [2818 bytes]     - link.py [21034 bytes]     - scheme.py [575 bytes]     - search_scope.py [4531 bytes]     - selection_prefs.py [2015 bytes]     - target_python.py [4271 bytes]     - wheel.py [4539 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\network
    - __init__.py [50 bytes]     - auth.py [20809 bytes]     - cache.py [3935 bytes]     - download.py [6048 bytes]     - lazy_wheel.py [7622 bytes]     - session.py [18741 bytes]     - utils.py [4088 bytes]     - xmlrpc.py [1838 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\operations
    - __init__.py [0 bytes]     - check.py [5912 bytes]     - freeze.py [9864 bytes]     - prepare.py [28118 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\req
    - __init__.py [2653 bytes]     - constructors.py [18430 bytes]     - req_file.py [18752 bytes]     - req_install.py [35788 bytes]     - req_set.py [2858 bytes]     - req_uninstall.py [23853 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\resolution
    - __init__.py [0 bytes]     - base.py [583 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\utils
    - __init__.py [0 bytes]     - _jaraco_text.py [3350 bytes]     - _log.py [1015 bytes]     - appdirs.py [1665 bytes]     - compat.py [2399 bytes]     - compatibility_tags.py [6272 bytes]     - datetime.py [242 bytes]     - deprecation.py [3707 bytes]     - direct_url_helpers.py [3196 bytes]     - egg_link.py [2463 bytes]     - encoding.py [1169 bytes]     - entrypoints.py [3064 bytes]     - filesystem.py [4950 bytes]     - filetypes.py [716 bytes]     - glibc.py [3734 bytes]     - hashes.py [4972 bytes]     - logging.py [11606 bytes]     - misc.py [23530 bytes]     - packaging.py [2109 bytes]     - retry.py [1392 bytes]     - setuptools_build.py [4435 bytes]     - subprocess.py [8988 bytes]     - temp_dir.py [9310 bytes]     - unpacking.py [11951 bytes]     - urls.py [1599 bytes]     - virtualenv.py [3456 bytes]     - wheel.py [4494 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\vcs
    - __init__.py [596 bytes]     - bazaar.py [3528 bytes]     - git.py [18177 bytes]     - mercurial.py [5249 bytes]     - subversion.py [11735 bytes]     - versioncontrol.py [22440 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\cli\__pycache__
    - __init__.cpython-313.pyc [259 bytes]     - autocompletion.cpython-313.pyc [8965 bytes]     - base_command.cpython-313.pyc [10361 bytes]     - cmdoptions.cpython-313.pyc [30152 bytes]     - command_context.cpython-313.pyc [1851 bytes]     - index_command.cpython-313.pyc [7353 bytes]     - main_parser.cpython-313.pyc [4988 bytes]     - main.cpython-313.pyc [2283 bytes]     - parser.cpython-313.pyc [15365 bytes]     - progress_bars.cpython-313.pyc [3836 bytes]     - req_command.cpython-313.pyc [12265 bytes]     - spinners.cpython-313.pyc [8157 bytes]     - status_codes.cpython-313.pyc [356 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\commands\__pycache__
    - __init__.cpython-313.pyc [3979 bytes]     - cache.cpython-313.pyc [9836 bytes]     - check.cpython-313.pyc [2663 bytes]     - completion.cpython-313.pyc [5208 bytes]     - configuration.cpython-313.pyc [13215 bytes]     - debug.cpython-313.pyc [10277 bytes]     - download.cpython-313.pyc [7530 bytes]     - freeze.cpython-313.pyc [4427 bytes]     - hash.cpython-313.pyc [3012 bytes]     - help.cpython-313.pyc [1726 bytes]     - index.cpython-313.pyc [6675 bytes]     - inspect.cpython-313.pyc [4017 bytes]     - install.cpython-313.pyc [29480 bytes]     - list.cpython-313.pyc [16022 bytes]     - search.cpython-313.pyc [7647 bytes]     - show.cpython-313.pyc [10650 bytes]     - uninstall.cpython-313.pyc [4751 bytes]     - wheel.cpython-313.pyc [8957 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\distributions\__pycache__
    - __init__.cpython-313.pyc [944 bytes]     - base.cpython-313.pyc [2884 bytes]     - installed.cpython-313.pyc [1735 bytes]     - sdist.cpython-313.pyc [8614 bytes]     - wheel.cpython-313.pyc [2324 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\index\__pycache__
    - __init__.cpython-313.pyc [213 bytes]     - collector.cpython-313.pyc [21822 bytes]     - package_finder.cpython-313.pyc [40154 bytes]     - sources.cpython-313.pyc [12734 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\locations\__pycache__
    - __init__.cpython-313.pyc [16944 bytes]     - _distutils.cpython-313.pyc [6880 bytes]     - _sysconfig.cpython-313.pyc [8117 bytes]     - base.cpython-313.pyc [3749 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\metadata\__pycache__
    - __init__.cpython-313.pyc [5813 bytes]     - _json.cpython-313.pyc [2956 bytes]     - base.cpython-313.pyc [34577 bytes]     - pkg_resources.cpython-313.pyc [16307 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\metadata\importlib
    - __init__.py [135 bytes]     - _compat.py [2796 bytes]     - _dists.py [8017 bytes]     - _envs.py [7431 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\metadata\importlib\__pycache__
    - __init__.cpython-313.pyc [339 bytes]     - _compat.cpython-313.pyc [4517 bytes]     - _dists.cpython-313.pyc [12788 bytes]     - _envs.cpython-313.pyc [11159 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\models\__pycache__
    - __init__.cpython-313.pyc [247 bytes]     - candidate.cpython-313.pyc [1643 bytes]     - direct_url.cpython-313.pyc [10985 bytes]     - format_control.cpython-313.pyc [4239 bytes]     - index.cpython-313.pyc [1746 bytes]     - installation_report.cpython-313.pyc [2360 bytes]     - link.cpython-313.pyc [26625 bytes]     - scheme.cpython-313.pyc [1048 bytes]     - search_scope.cpython-313.pyc [5092 bytes]     - selection_prefs.cpython-313.pyc [1811 bytes]     - target_python.cpython-313.pyc [4816 bytes]     - wheel.cpython-313.pyc [6510 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\network\__pycache__
    - __init__.cpython-313.pyc [235 bytes]     - auth.cpython-313.pyc [22543 bytes]     - cache.cpython-313.pyc [6582 bytes]     - download.cpython-313.pyc [8640 bytes]     - lazy_wheel.cpython-313.pyc [11483 bytes]     - session.cpython-313.pyc [19146 bytes]     - utils.cpython-313.pyc [2276 bytes]     - xmlrpc.cpython-313.pyc [3027 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\operations\__pycache__
    - __init__.cpython-313.pyc [178 bytes]     - check.cpython-313.pyc [7205 bytes]     - freeze.cpython-313.pyc [10359 bytes]     - prepare.cpython-313.pyc [26576 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\operations\build
    - __init__.py [0 bytes]     - build_tracker.py [4774 bytes]     - metadata_editable.py [1474 bytes]     - metadata_legacy.py [2190 bytes]     - metadata.py [1422 bytes]     - wheel_editable.py [1417 bytes]     - wheel_legacy.py [3045 bytes]     - wheel.py [1075 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\operations\install
    - __init__.py [51 bytes]     - editable_legacy.py [1283 bytes]     - wheel.py [27615 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\operations\build\__pycache__
    - __init__.cpython-313.pyc [184 bytes]     - build_tracker.cpython-313.pyc [7722 bytes]     - metadata_editable.cpython-313.pyc [1876 bytes]     - metadata_legacy.cpython-313.pyc [2977 bytes]     - metadata.cpython-313.pyc [1842 bytes]     - wheel_editable.cpython-313.pyc [2005 bytes]     - wheel_legacy.cpython-313.pyc [3848 bytes]     - wheel.cpython-313.pyc [1664 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\operations\install\__pycache__
    - __init__.cpython-313.pyc [247 bytes]     - editable_legacy.cpython-313.pyc [1778 bytes]     - wheel.cpython-313.pyc [34755 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\req\__pycache__
    - __init__.cpython-313.pyc [3552 bytes]     - constructors.cpython-313.pyc [21528 bytes]     - req_file.cpython-313.pyc [22521 bytes]     - req_install.cpython-313.pyc [39331 bytes]     - req_set.cpython-313.pyc [5602 bytes]     - req_uninstall.cpython-313.pyc [32834 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\resolution\__pycache__
    - __init__.cpython-313.pyc [178 bytes]     - base.cpython-313.pyc [1228 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\resolution\legacy
    - __init__.py [0 bytes]     - resolver.py [24068 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\resolution\resolvelib
    - __init__.py [0 bytes]     - base.py [5023 bytes]     - candidates.py [20001 bytes]     - factory.py [32661 bytes]     - found_candidates.py [6383 bytes]     - provider.py [9935 bytes]     - reporter.py [3168 bytes]     - requirements.py [8065 bytes]     - resolver.py [12592 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\resolution\legacy\__pycache__
    - __init__.cpython-313.pyc [185 bytes]     - resolver.cpython-313.pyc [22862 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\resolution\resolvelib\__pycache__
    - __init__.cpython-313.pyc [189 bytes]     - base.cpython-313.pyc [8248 bytes]     - candidates.cpython-313.pyc [29758 bytes]     - factory.cpython-313.pyc [33135 bytes]     - found_candidates.cpython-313.pyc [6851 bytes]     - provider.cpython-313.pyc [10280 bytes]     - reporter.cpython-313.pyc [5116 bytes]     - requirements.cpython-313.pyc [15728 bytes]     - resolver.cpython-313.pyc [12379 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\utils\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - _jaraco_text.cpython-313.pyc [4419 bytes]     - _log.cpython-313.pyc [1892 bytes]     - appdirs.cpython-313.pyc [2401 bytes]     - compat.cpython-313.pyc [2911 bytes]     - compatibility_tags.cpython-313.pyc [6366 bytes]     - datetime.cpython-313.pyc [658 bytes]     - deprecation.cpython-313.pyc [4224 bytes]     - direct_url_helpers.cpython-313.pyc [3612 bytes]     - egg_link.cpython-313.pyc [3193 bytes]     - encoding.cpython-313.pyc [2172 bytes]     - entrypoints.cpython-313.pyc [4053 bytes]     - filesystem.cpython-313.pyc [7418 bytes]     - filetypes.cpython-313.pyc [1148 bytes]     - glibc.cpython-313.pyc [2428 bytes]     - hashes.cpython-313.pyc [7720 bytes]     - logging.cpython-313.pyc [13813 bytes]     - misc.cpython-313.pyc [33687 bytes]     - packaging.cpython-313.pyc [2514 bytes]     - retry.cpython-313.pyc [2088 bytes]     - setuptools_build.cpython-313.pyc [4548 bytes]     - subprocess.cpython-313.pyc [8850 bytes]     - temp_dir.cpython-313.pyc [12115 bytes]     - unpacking.cpython-313.pyc [13774 bytes]     - urls.cpython-313.pyc [2108 bytes]     - virtualenv.cpython-313.pyc [4448 bytes]     - wheel.cpython-313.pyc [5874 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_internal\vcs\__pycache__
    - __init__.cpython-313.pyc [512 bytes]     - bazaar.cpython-313.pyc [5117 bytes]     - git.cpython-313.pyc [18791 bytes]     - mercurial.cpython-313.pyc [7579 bytes]     - subversion.cpython-313.pyc [12648 bytes]     - versioncontrol.cpython-313.pyc [28273 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\__pycache__
    - __init__.cpython-313.pyc [4533 bytes]     - typing_extensions.cpython-313.pyc [142760 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\cachecontrol
    - __init__.py [676 bytes]     - _cmd.py [1737 bytes]     - adapter.py [6355 bytes]     - cache.py [1952 bytes]     - controller.py [18575 bytes]     - filewrapper.py [4292 bytes]     - heuristics.py [4834 bytes]     - py.typed [0 bytes]     - serialize.py [5163 bytes]     - wrapper.py [1417 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\certifi
    - __init__.py [94 bytes]     - __main__.py [255 bytes]     - cacert.pem [299427 bytes]     - core.py [4486 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\distlib
    - __init__.py [625 bytes]     - compat.py [41467 bytes]     - database.py [51160 bytes]     - index.py [20797 bytes]     - locators.py [51026 bytes]     - manifest.py [14168 bytes]     - markers.py [5164 bytes]     - metadata.py [38724 bytes]     - resources.py [10820 bytes]     - scripts.py [18608 bytes]     - t32.exe [97792 bytes]     - t64-arm.exe [182784 bytes]     - t64.exe [108032 bytes]     - util.py [66682 bytes]     - version.py [23727 bytes]     - w32.exe [91648 bytes]     - w64-arm.exe [168448 bytes]     - w64.exe [101888 bytes]     - wheel.py [43979 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\distro
    - __init__.py [981 bytes]     - __main__.py [64 bytes]     - distro.py [49430 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\idna
    - __init__.py [849 bytes]     - codec.py [3426 bytes]     - compat.py [321 bytes]     - core.py [12663 bytes]     - idnadata.py [78320 bytes]     - intranges.py [1881 bytes]     - package_data.py [21 bytes]     - py.typed [0 bytes]     - uts46data.py [206503 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\msgpack
    - __init__.py [1077 bytes]     - exceptions.py [1081 bytes]     - ext.py [5629 bytes]     - fallback.py [33175 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\packaging
    - __init__.py [496 bytes]     - _elffile.py [3282 bytes]     - _manylinux.py [9586 bytes]     - _musllinux.py [2694 bytes]     - _parser.py [10236 bytes]     - _structures.py [1431 bytes]     - _tokenizer.py [5273 bytes]     - markers.py [10671 bytes]     - metadata.py [32349 bytes]     - py.typed [0 bytes]     - requirements.py [2947 bytes]     - specifiers.py [39738 bytes]     - tags.py [21388 bytes]     - utils.py [5287 bytes]     - version.py [16210 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pkg_resources
    - __init__.py [124463 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\platformdirs
    - __init__.py [22285 bytes]     - __main__.py [1505 bytes]     - android.py [9016 bytes]     - api.py [8996 bytes]     - macos.py [5580 bytes]     - py.typed [0 bytes]     - unix.py [10643 bytes]     - version.py [411 bytes]     - windows.py [10125 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments
    - __init__.py [2983 bytes]     - __main__.py [353 bytes]     - cmdline.py [23656 bytes]     - console.py [1718 bytes]     - filter.py [1910 bytes]     - formatter.py [4390 bytes]     - lexer.py [35349 bytes]     - modeline.py [1005 bytes]     - plugin.py [1891 bytes]     - regexopt.py [3072 bytes]     - scanner.py [3092 bytes]     - sphinxext.py [7981 bytes]     - style.py [6420 bytes]     - token.py [6226 bytes]     - unistring.py [63208 bytes]     - util.py [10031 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pyproject_hooks
    - __init__.py [491 bytes]     - _compat.py [138 bytes]     - _impl.py [11920 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\requests
    - __init__.py [5057 bytes]     - __version__.py [435 bytes]     - _internal_utils.py [1495 bytes]     - adapters.py [27607 bytes]     - api.py [6449 bytes]     - auth.py [10186 bytes]     - certs.py [575 bytes]     - compat.py [1485 bytes]     - cookies.py [18590 bytes]     - exceptions.py [4272 bytes]     - help.py [3813 bytes]     - hooks.py [733 bytes]     - models.py [35483 bytes]     - packages.py [1057 bytes]     - sessions.py [30495 bytes]     - status_codes.py [4322 bytes]     - structures.py [2912 bytes]     - utils.py [33631 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\resolvelib
    - __init__.py [537 bytes]     - providers.py [5871 bytes]     - py.typed [0 bytes]     - reporters.py [1601 bytes]     - resolvers.py [20511 bytes]     - structs.py [4963 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\rich
    - __init__.py [6090 bytes]     - __main__.py [8477 bytes]     - _cell_widths.py [10209 bytes]     - _emoji_codes.py [140235 bytes]     - _emoji_replace.py [1064 bytes]     - _export_format.py [2128 bytes]     - _extension.py [265 bytes]     - _fileno.py [799 bytes]     - _inspect.py [9695 bytes]     - _log_render.py [3225 bytes]     - _loop.py [1236 bytes]     - _null_file.py [1387 bytes]     - _palettes.py [7063 bytes]     - _pick.py [423 bytes]     - _ratio.py [5471 bytes]     - _spinners.py [19919 bytes]     - _stack.py [351 bytes]     - _timer.py [417 bytes]     - _win32_console.py [22820 bytes]     - _windows_renderer.py [2783 bytes]     - _windows.py [1925 bytes]     - _wrap.py [3404 bytes]     - abc.py [890 bytes]     - align.py [10368 bytes]     - ansi.py [6906 bytes]     - bar.py [3263 bytes]     - box.py [10831 bytes]     - cells.py [4780 bytes]     - color_triplet.py [1054 bytes]     - color.py [18223 bytes]     - columns.py [7131 bytes]     - console.py [99173 bytes]     - constrain.py [1288 bytes]     - containers.py [5502 bytes]     - control.py [6630 bytes]     - default_styles.py [8082 bytes]     - diagnose.py [972 bytes]     - emoji.py [2501 bytes]     - errors.py [642 bytes]     - file_proxy.py [1683 bytes]     - filesize.py [2508 bytes]     - highlighter.py [9585 bytes]     - json.py [5031 bytes]     - jupyter.py [3252 bytes]     - layout.py [14004 bytes]     - live_render.py [3666 bytes]     - live.py [14271 bytes]     - logging.py [11903 bytes]     - markup.py [8451 bytes]     - measure.py [5305 bytes]     - padding.py [4970 bytes]     - pager.py [828 bytes]     - palette.py [3396 bytes]     - panel.py [10705 bytes]     - pretty.py [35848 bytes]     - progress_bar.py [8164 bytes]     - progress.py [59715 bytes]     - prompt.py [11304 bytes]     - protocol.py [1391 bytes]     - py.typed [0 bytes]     - region.py [166 bytes]     - repr.py [4431 bytes]     - rule.py [4602 bytes]     - scope.py [2843 bytes]     - screen.py [1591 bytes]     - segment.py [24246 bytes]     - spinner.py [4339 bytes]     - status.py [4424 bytes]     - style.py [27073 bytes]     - styled.py [1258 bytes]     - syntax.py [35475 bytes]     - table.py [39680 bytes]     - terminal_theme.py [3370 bytes]     - text.py [47312 bytes]     - theme.py [3777 bytes]     - themes.py [102 bytes]     - traceback.py [29601 bytes]     - tree.py [9167 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\tomli
    - __init__.py [396 bytes]     - _parser.py [22633 bytes]     - _re.py [2943 bytes]     - _types.py [254 bytes]     - py.typed [26 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\truststore
    - __init__.py [1264 bytes]     - _api.py [10555 bytes]     - _macos.py [20503 bytes]     - _openssl.py [2324 bytes]     - _ssl_constants.py [1130 bytes]     - _windows.py [17993 bytes]     - py.typed [0 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3
    - __init__.py [3333 bytes]     - _collections.py [11372 bytes]     - _version.py [64 bytes]     - connection.py [20314 bytes]     - connectionpool.py [40408 bytes]     - exceptions.py [8217 bytes]     - fields.py [8579 bytes]     - filepost.py [2440 bytes]     - poolmanager.py [19990 bytes]     - request.py [6691 bytes]     - response.py [30641 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\cachecontrol\__pycache__
    - __init__.cpython-313.pyc [879 bytes]     - _cmd.cpython-313.pyc [2657 bytes]     - adapter.cpython-313.pyc [6571 bytes]     - cache.cpython-313.pyc [3909 bytes]     - controller.cpython-313.pyc [16580 bytes]     - filewrapper.cpython-313.pyc [4415 bytes]     - heuristics.cpython-313.pyc [6748 bytes]     - serialize.cpython-313.pyc [5348 bytes]     - wrapper.cpython-313.pyc [1676 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\cachecontrol\caches
    - __init__.py [303 bytes]     - file_cache.py [5406 bytes]     - redis_cache.py [1386 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\cachecontrol\caches\__pycache__
    - __init__.cpython-313.pyc [417 bytes]     - file_cache.cpython-313.pyc [7936 bytes]     - redis_cache.cpython-313.pyc [2798 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\certifi\__pycache__
    - __init__.cpython-313.pyc [300 bytes]     - __main__.cpython-313.pyc [629 bytes]     - core.cpython-313.pyc [3209 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\distlib\__pycache__
    - __init__.cpython-313.pyc [1337 bytes]     - compat.cpython-313.pyc [45847 bytes]     - database.cpython-313.pyc [65217 bytes]     - index.cpython-313.pyc [23534 bytes]     - locators.cpython-313.pyc [59513 bytes]     - manifest.cpython-313.pyc [15014 bytes]     - markers.cpython-313.pyc [7669 bytes]     - metadata.cpython-313.pyc [42572 bytes]     - resources.cpython-313.pyc [17639 bytes]     - scripts.cpython-313.pyc [20215 bytes]     - util.cpython-313.pyc [90097 bytes]     - version.cpython-313.pyc [30961 bytes]     - wheel.cpython-313.pyc [53761 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\distro\__pycache__
    - __init__.cpython-313.pyc [942 bytes]     - __main__.cpython-313.pyc [276 bytes]     - distro.cpython-313.pyc [52125 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\idna\__pycache__
    - __init__.cpython-313.pyc [863 bytes]     - codec.cpython-313.pyc [5292 bytes]     - compat.cpython-313.pyc [875 bytes]     - core.cpython-313.pyc [16585 bytes]     - idnadata.cpython-313.pyc [99458 bytes]     - intranges.cpython-313.pyc [2591 bytes]     - package_data.cpython-313.pyc [198 bytes]     - uts46data.cpython-313.pyc [158994 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\msgpack\__pycache__
    - __init__.cpython-313.pyc [1699 bytes]     - exceptions.cpython-313.pyc [2146 bytes]     - ext.cpython-313.pyc [7958 bytes]     - fallback.cpython-313.pyc [42539 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\packaging\__pycache__
    - __init__.cpython-313.pyc [540 bytes]     - _elffile.cpython-313.pyc [5150 bytes]     - _manylinux.cpython-313.pyc [9971 bytes]     - _musllinux.cpython-313.pyc [4605 bytes]     - _parser.cpython-313.pyc [14171 bytes]     - _structures.cpython-313.pyc [3335 bytes]     - _tokenizer.cpython-313.pyc [8055 bytes]     - markers.cpython-313.pyc [11309 bytes]     - metadata.cpython-313.pyc [25024 bytes]     - requirements.cpython-313.pyc [4616 bytes]     - specifiers.cpython-313.pyc [37372 bytes]     - tags.cpython-313.pyc [23523 bytes]     - utils.cpython-313.pyc [7480 bytes]     - version.cpython-313.pyc [18991 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pkg_resources\__pycache__
    - __init__.cpython-313.pyc [161600 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\platformdirs\__pycache__
    - __init__.cpython-313.pyc [19319 bytes]     - __main__.cpython-313.pyc [1914 bytes]     - android.cpython-313.pyc [10757 bytes]     - api.cpython-313.pyc [13015 bytes]     - macos.cpython-313.pyc [7979 bytes]     - unix.cpython-313.pyc [15038 bytes]     - version.cpython-313.pyc [589 bytes]     - windows.cpython-313.pyc [13766 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\__pycache__
    - __init__.cpython-313.pyc [3415 bytes]     - __main__.cpython-313.pyc [695 bytes]     - cmdline.cpython-313.pyc [27098 bytes]     - console.cpython-313.pyc [2583 bytes]     - filter.cpython-313.pyc [3209 bytes]     - formatter.cpython-313.pyc [4540 bytes]     - lexer.cpython-313.pyc [38460 bytes]     - modeline.cpython-313.pyc [1556 bytes]     - plugin.cpython-313.pyc [2530 bytes]     - regexopt.cpython-313.pyc [4087 bytes]     - scanner.cpython-313.pyc [4666 bytes]     - sphinxext.cpython-313.pyc [12223 bytes]     - style.cpython-313.pyc [6922 bytes]     - token.cpython-313.pyc [8226 bytes]     - unistring.cpython-313.pyc [33008 bytes]     - util.cpython-313.pyc [14156 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\filters
    - __init__.py [40392 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\formatters
    - __init__.py [5385 bytes]     - _mapping.py [4176 bytes]     - bbcode.py [3320 bytes]     - groff.py [5106 bytes]     - html.py [35669 bytes]     - img.py [23287 bytes]     - irc.py [4981 bytes]     - latex.py [19306 bytes]     - other.py [5034 bytes]     - pangomarkup.py [2218 bytes]     - rtf.py [11957 bytes]     - svg.py [7174 bytes]     - terminal.py [4674 bytes]     - terminal256.py [11753 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\lexers
    - __init__.py [12115 bytes]     - _mapping.py [76097 bytes]     - python.py [53687 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\styles
    - __init__.py [2042 bytes]     - _mapping.py [3312 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\filters\__pycache__
    - __init__.cpython-313.pyc [37952 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\formatters\__pycache__
    - __init__.cpython-313.pyc [6909 bytes]     - _mapping.cpython-313.pyc [4198 bytes]     - bbcode.cpython-313.pyc [4267 bytes]     - groff.cpython-313.pyc [7419 bytes]     - html.cpython-313.pyc [41091 bytes]     - img.cpython-313.pyc [28458 bytes]     - irc.cpython-313.pyc [6102 bytes]     - latex.cpython-313.pyc [20368 bytes]     - other.cpython-313.pyc [6938 bytes]     - pangomarkup.cpython-313.pyc [3015 bytes]     - rtf.cpython-313.pyc [13853 bytes]     - svg.cpython-313.pyc [9156 bytes]     - terminal.cpython-313.pyc [5882 bytes]     - terminal256.cpython-313.pyc [15506 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\lexers\__pycache__
    - __init__.cpython-313.pyc [14737 bytes]     - _mapping.cpython-313.pyc [68246 bytes]     - python.cpython-313.pyc [43022 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pygments\styles\__pycache__
    - __init__.cpython-313.pyc [2624 bytes]     - _mapping.cpython-313.pyc [3631 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pyproject_hooks\__pycache__
    - __init__.cpython-313.pyc [596 bytes]     - _compat.cpython-313.pyc [359 bytes]     - _impl.cpython-313.pyc [14389 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process
    - __init__.py [546 bytes]     - _in_process.py [10927 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\__pycache__
    - __init__.cpython-313.pyc [1071 bytes]     - _in_process.cpython-313.pyc [14566 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\requests\__pycache__
    - __init__.cpython-313.pyc [5258 bytes]     - __version__.cpython-313.pyc [567 bytes]     - _internal_utils.cpython-313.pyc [2001 bytes]     - adapters.cpython-313.pyc [27456 bytes]     - api.cpython-313.pyc [6853 bytes]     - auth.cpython-313.pyc [14215 bytes]     - certs.cpython-313.pyc [911 bytes]     - compat.cpython-313.pyc [1660 bytes]     - cookies.cpython-313.pyc [25010 bytes]     - exceptions.cpython-313.pyc [8006 bytes]     - help.cpython-313.pyc [4244 bytes]     - hooks.cpython-313.pyc [1077 bytes]     - models.cpython-313.pyc [35838 bytes]     - packages.cpython-313.pyc [1297 bytes]     - sessions.cpython-313.pyc [27369 bytes]     - status_codes.cpython-313.pyc [6027 bytes]     - structures.cpython-313.pyc [5630 bytes]     - utils.cpython-313.pyc [36503 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\resolvelib\__pycache__
    - __init__.cpython-313.pyc [617 bytes]     - providers.cpython-313.pyc [6333 bytes]     - reporters.cpython-313.pyc [2591 bytes]     - resolvers.cpython-313.pyc [25748 bytes]     - structs.cpython-313.pyc [10678 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\resolvelib\compat
    - __init__.py [0 bytes]     - collections_abc.py [156 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\resolvelib\compat\__pycache__
    - __init__.cpython-313.pyc [183 bytes]     - collections_abc.cpython-313.pyc [405 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\rich\__pycache__
    - __init__.cpython-313.pyc [6857 bytes]     - __main__.cpython-313.pyc [10158 bytes]     - _cell_widths.cpython-313.pyc [7855 bytes]     - _emoji_codes.cpython-313.pyc [205959 bytes]     - _emoji_replace.cpython-313.pyc [1727 bytes]     - _export_format.cpython-313.pyc [2332 bytes]     - _extension.cpython-313.pyc [522 bytes]     - _fileno.cpython-313.pyc [824 bytes]     - _inspect.cpython-313.pyc [12329 bytes]     - _log_render.cpython-313.pyc [4308 bytes]     - _loop.cpython-313.pyc [1883 bytes]     - _null_file.cpython-313.pyc [3709 bytes]     - _palettes.cpython-313.pyc [5143 bytes]     - _pick.cpython-313.pyc [702 bytes]     - _ratio.cpython-313.pyc [6583 bytes]     - _spinners.cpython-313.pyc [13162 bytes]     - _stack.cpython-313.pyc [1010 bytes]     - _timer.cpython-313.pyc [851 bytes]     - _win32_console.cpython-313.pyc [28487 bytes]     - _windows_renderer.cpython-313.pyc [3596 bytes]     - _windows.cpython-313.pyc [2549 bytes]     - _wrap.cpython-313.pyc [3308 bytes]     - abc.cpython-313.pyc [1671 bytes]     - align.cpython-313.pyc [12455 bytes]     - ansi.cpython-313.pyc [9228 bytes]     - bar.cpython-313.pyc [4316 bytes]     - box.cpython-313.pyc [11805 bytes]     - cells.cpython-313.pyc [5698 bytes]     - color_triplet.cpython-313.pyc [1688 bytes]     - color.cpython-313.pyc [26528 bytes]     - columns.cpython-313.pyc [8699 bytes]     - console.cpython-313.pyc [112620 bytes]     - constrain.cpython-313.pyc [2294 bytes]     - containers.cpython-313.pyc [9237 bytes]     - control.cpython-313.pyc [10895 bytes]     - default_styles.cpython-313.pyc [9541 bytes]     - diagnose.cpython-313.pyc [1477 bytes]     - emoji.cpython-313.pyc [4233 bytes]     - errors.cpython-313.pyc [2030 bytes]     - file_proxy.cpython-313.pyc [3690 bytes]     - filesize.cpython-313.pyc [2965 bytes]     - highlighter.cpython-313.pyc [9959 bytes]     - json.cpython-313.pyc [5901 bytes]     - jupyter.cpython-313.pyc [5373 bytes]     - layout.cpython-313.pyc [20156 bytes]     - live_render.cpython-313.pyc [4852 bytes]     - live.cpython-313.pyc [19622 bytes]     - logging.cpython-313.pyc [13490 bytes]     - markup.cpython-313.pyc [9704 bytes]     - measure.cpython-313.pyc [6181 bytes]     - padding.cpython-313.pyc [7119 bytes]     - pager.cpython-313.pyc [1868 bytes]     - palette.cpython-313.pyc [5295 bytes]     - panel.cpython-313.pyc [12139 bytes]     - pretty.cpython-313.pyc [40757 bytes]     - progress_bar.cpython-313.pyc [10432 bytes]     - progress.cpython-313.pyc [74687 bytes]     - prompt.cpython-313.pyc [14562 bytes]     - protocol.cpython-313.pyc [1851 bytes]     - region.cpython-313.pyc [608 bytes]     - repr.cpython-313.pyc [6728 bytes]     - rule.cpython-313.pyc [6602 bytes]     - scope.cpython-313.pyc [3777 bytes]     - screen.cpython-313.pyc [2533 bytes]     - segment.cpython-313.pyc [27903 bytes]     - spinner.cpython-313.pyc [6111 bytes]     - status.cpython-313.pyc [6009 bytes]     - style.cpython-313.pyc [34413 bytes]     - styled.cpython-313.pyc [2170 bytes]     - syntax.cpython-313.pyc [39585 bytes]     - table.cpython-313.pyc [44029 bytes]     - terminal_theme.cpython-313.pyc [3389 bytes]     - text.cpython-313.pyc [59888 bytes]     - theme.cpython-313.pyc [6317 bytes]     - themes.cpython-313.pyc [297 bytes]     - traceback.cpython-313.pyc [31761 bytes]     - tree.cpython-313.pyc [11552 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\tomli\__pycache__
    - __init__.cpython-313.pyc [367 bytes]     - _parser.cpython-313.pyc [27079 bytes]     - _re.cpython-313.pyc [3879 bytes]     - _types.cpython-313.pyc [349 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\truststore\__pycache__
    - __init__.cpython-313.pyc [1359 bytes]     - _api.cpython-313.pyc [16754 bytes]     - _macos.cpython-313.pyc [19284 bytes]     - _openssl.cpython-313.pyc [2271 bytes]     - _ssl_constants.cpython-313.pyc [1082 bytes]     - _windows.cpython-313.pyc [16228 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\__pycache__
    - __init__.cpython-313.pyc [3336 bytes]     - _collections.cpython-313.pyc [16415 bytes]     - _version.cpython-313.pyc [201 bytes]     - connection.cpython-313.pyc [20710 bytes]     - connectionpool.cpython-313.pyc [36042 bytes]     - exceptions.cpython-313.pyc [14162 bytes]     - fields.cpython-313.pyc [10169 bytes]     - filepost.cpython-313.pyc [3978 bytes]     - poolmanager.cpython-313.pyc [19968 bytes]     - request.cpython-313.pyc [6950 bytes]     - response.cpython-313.pyc [34258 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\contrib
    - __init__.py [0 bytes]     - _appengine_environ.py [957 bytes]     - appengine.py [11036 bytes]     - ntlmpool.py [4528 bytes]     - pyopenssl.py [17081 bytes]     - securetransport.py [34446 bytes]     - socks.py [7097 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\packages
    - __init__.py [0 bytes]     - six.py [34665 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\util
    - __init__.py [1155 bytes]     - connection.py [4901 bytes]     - proxy.py [1605 bytes]     - queue.py [498 bytes]     - request.py [3997 bytes]     - response.py [3510 bytes]     - retry.py [22050 bytes]     - ssl_.py [17460 bytes]     - ssl_match_hostname.py [5758 bytes]     - ssltransport.py [6895 bytes]     - timeout.py [10168 bytes]     - url.py [14296 bytes]     - wait.py [5403 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\contrib\__pycache__
    - __init__.cpython-313.pyc [181 bytes]     - _appengine_environ.cpython-313.pyc [1859 bytes]     - appengine.cpython-313.pyc [11751 bytes]     - ntlmpool.cpython-313.pyc [5719 bytes]     - pyopenssl.cpython-313.pyc [24768 bytes]     - securetransport.cpython-313.pyc [35963 bytes]     - socks.cpython-313.pyc [7713 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\contrib\_securetransport
    - __init__.py [0 bytes]     - bindings.py [17632 bytes]     - low_level.py [13922 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\contrib\_securetransport\__pycache__
    - __init__.cpython-313.pyc [198 bytes]     - bindings.cpython-313.pyc [17484 bytes]     - low_level.cpython-313.pyc [14801 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\packages\__pycache__
    - __init__.cpython-313.pyc [182 bytes]     - six.cpython-313.pyc [42002 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\packages\backports
    - __init__.py [0 bytes]     - makefile.py [1417 bytes]     - weakref_finalize.py [5343 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\packages\backports\__pycache__
    - __init__.cpython-313.pyc [192 bytes]     - makefile.cpython-313.pyc [1916 bytes]     - weakref_finalize.cpython-313.pyc [7511 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pip\_vendor\urllib3\util\__pycache__
    - __init__.cpython-313.pyc [1129 bytes]     - connection.cpython-313.pyc [4760 bytes]     - proxy.cpython-313.pyc [1558 bytes]     - queue.cpython-313.pyc [1407 bytes]     - request.cpython-313.pyc [4127 bytes]     - response.cpython-313.pyc [3026 bytes]     - retry.cpython-313.pyc [21533 bytes]     - ssl_.cpython-313.pyc [15482 bytes]     - ssl_match_hostname.cpython-313.pyc [5201 bytes]     - ssltransport.cpython-313.pyc [10916 bytes]     - timeout.cpython-313.pyc [10661 bytes]     - url.cpython-313.pyc [15918 bytes]     - wait.cpython-313.pyc [4560 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pluggy\__pycache__
    - __init__.cpython-313.pyc [836 bytes]     - _callers.cpython-313.pyc [7160 bytes]     - _hooks.cpython-313.pyc [27567 bytes]     - _manager.cpython-313.pyc [25557 bytes]     - _result.cpython-313.pyc [4125 bytes]     - _tracing.cpython-313.pyc [4120 bytes]     - _version.cpython-313.pyc [631 bytes]     - _warnings.cpython-313.pyc [1314 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pluggy-1.6.0.dist-info\licenses
    - LICENSE [1110 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\__pycache__
    - __init__.cpython-313.pyc [1394 bytes]     - _file_info.cpython-313.pyc [8324 bytes]     - _package_info.cpython-313.pyc [1287 bytes]     - datetime_helpers.cpython-313.pyc [8494 bytes]     - enums.cpython-313.pyc [6596 bytes]     - fields.cpython-313.pyc [5668 bytes]     - message.cpython-313.pyc [35603 bytes]     - modules.cpython-313.pyc [1245 bytes]     - primitives.cpython-313.pyc [879 bytes]     - utils.cpython-313.pyc [1483 bytes]     - version.cpython-313.pyc [187 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\marshal
    - __init__.py [630 bytes]     - compat.py [2349 bytes]     - marshal.py [12030 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\marshal\__pycache__
    - __init__.cpython-313.pyc [231 bytes]     - compat.cpython-313.pyc [1244 bytes]     - marshal.cpython-313.pyc [13031 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\marshal\collections
    - __init__.py [755 bytes]     - maps.py [2921 bytes]     - repeated.py [6934 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\marshal\rules
    - __init__.py [575 bytes]     - bytes.py [1593 bytes]     - dates.py [3135 bytes]     - enums.py [2213 bytes]     - field_mask.py [1182 bytes]     - message.py [2364 bytes]     - stringy_numbers.py [1787 bytes]     - struct.py [5226 bytes]     - wrappers.py [2280 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\marshal\collections\__pycache__
    - __init__.cpython-313.pyc [345 bytes]     - maps.cpython-313.pyc [3368 bytes]     - repeated.cpython-313.pyc [8156 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\proto\marshal\rules\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - bytes.cpython-313.pyc [1680 bytes]     - dates.cpython-313.pyc [3934 bytes]     - enums.cpython-313.pyc [1881 bytes]     - field_mask.cpython-313.pyc [1249 bytes]     - message.cpython-313.pyc [2258 bytes]     - stringy_numbers.cpython-313.pyc [2535 bytes]     - struct.cpython-313.pyc [7004 bytes]     - wrappers.cpython-313.pyc [3731 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\psutil\__pycache__
    - __init__.cpython-313.pyc [86384 bytes]     - _common.cpython-313.pyc [33600 bytes]     - _psaix.cpython-313.pyc [24874 bytes]     - _psbsd.cpython-313.pyc [35143 bytes]     - _pslinux.cpython-313.pyc [90825 bytes]     - _psosx.cpython-313.pyc [21603 bytes]     - _psposix.cpython-313.pyc [5744 bytes]     - _pssunos.cpython-313.pyc [30657 bytes]     - _pswindows.cpython-313.pyc [43265 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\psutil\tests
    - __init__.py [66129 bytes]     - __main__.py [321 bytes]     - test_aix.py [4550 bytes]     - test_bsd.py [20784 bytes]     - test_connections.py [21723 bytes]     - test_contracts.py [12326 bytes]     - test_linux.py [91187 bytes]     - test_memleaks.py [15608 bytes]     - test_misc.py [30545 bytes]     - test_osx.py [6512 bytes]     - test_posix.py [17675 bytes]     - test_process_all.py [18882 bytes]     - test_process.py [61548 bytes]     - test_scripts.py [7965 bytes]     - test_sunos.py [1229 bytes]     - test_system.py [37086 bytes]     - test_testutils.py [18915 bytes]     - test_unicode.py [10705 bytes]     - test_windows.py [34128 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\psutil\tests\__pycache__
    - __init__.cpython-313.pyc [93560 bytes]     - __main__.cpython-313.pyc [379 bytes]     - test_aix.cpython-313.pyc [5228 bytes]     - test_bsd.cpython-313.pyc [36352 bytes]     - test_connections.cpython-313.pyc [27705 bytes]     - test_contracts.cpython-313.pyc [23312 bytes]     - test_linux.cpython-313.pyc [138079 bytes]     - test_memleaks.cpython-313.pyc [35963 bytes]     - test_misc.cpython-313.pyc [45935 bytes]     - test_osx.cpython-313.pyc [11530 bytes]     - test_posix.cpython-313.pyc [25271 bytes]     - test_process_all.cpython-313.pyc [28317 bytes]     - test_process.cpython-313.pyc [101173 bytes]     - test_scripts.cpython-313.pyc [14607 bytes]     - test_sunos.cpython-313.pyc [2206 bytes]     - test_system.cpython-313.pyc [56471 bytes]     - test_testutils.cpython-313.pyc [38976 bytes]     - test_unicode.cpython-313.pyc [16493 bytes]     - test_windows.cpython-313.pyc [56527 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\__pycache__
    - __init__.cpython-313.pyc [186 bytes]     - debug.cpython-313.pyc [6307 bytes]     - error.cpython-313.pyc [4590 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec
    - __init__.py [59 bytes]     - streaming.py [6377 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\compat
    - __init__.py [112 bytes]     - integer.py [404 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\type
    - __init__.py [59 bytes]     - base.py [22050 bytes]     - char.py [9438 bytes]     - constraint.py [21915 bytes]     - error.py [259 bytes]     - namedtype.py [16179 bytes]     - namedval.py [4899 bytes]     - opentype.py [2861 bytes]     - tag.py [9497 bytes]     - tagmap.py [3000 bytes]     - univ.py [109212 bytes]     - useful.py [5284 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\__pycache__
    - __init__.cpython-313.pyc [166 bytes]     - streaming.cpython-313.pyc [8297 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\ber
    - __init__.py [59 bytes]     - decoder.py [79192 bytes]     - encoder.py [29796 bytes]     - eoo.py [639 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\cer
    - __init__.py [59 bytes]     - decoder.py [4589 bytes]     - encoder.py [9838 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\der
    - __init__.py [59 bytes]     - decoder.py [3428 bytes]     - encoder.py [3479 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\native
    - __init__.py [59 bytes]     - decoder.py [9118 bytes]     - encoder.py [9184 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\ber\__pycache__
    - __init__.cpython-313.pyc [170 bytes]     - decoder.cpython-313.pyc [76872 bytes]     - encoder.cpython-313.pyc [33452 bytes]     - eoo.cpython-313.pyc [1152 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\cer\__pycache__
    - __init__.cpython-313.pyc [170 bytes]     - decoder.cpython-313.pyc [4285 bytes]     - encoder.cpython-313.pyc [12012 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\der\__pycache__
    - __init__.cpython-313.pyc [170 bytes]     - decoder.cpython-313.pyc [3225 bytes]     - encoder.cpython-313.pyc [3407 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\codec\native\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - decoder.cpython-313.pyc [12015 bytes]     - encoder.cpython-313.pyc [14779 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\compat\__pycache__
    - __init__.cpython-313.pyc [205 bytes]     - integer.cpython-313.pyc [613 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1\type\__pycache__
    - __init__.cpython-313.pyc [165 bytes]     - base.cpython-313.pyc [27082 bytes]     - char.cpython-313.pyc [9937 bytes]     - constraint.cpython-313.pyc [27754 bytes]     - error.cpython-313.pyc [462 bytes]     - namedtype.cpython-313.pyc [23453 bytes]     - namedval.cpython-313.pyc [7762 bytes]     - opentype.cpython-313.pyc [3756 bytes]     - tag.cpython-313.pyc [12747 bytes]     - tagmap.cpython-313.pyc [4462 bytes]     - univ.cpython-313.pyc [129881 bytes]     - useful.cpython-313.pyc [7589 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1_modules\__pycache__
    - __init__.cpython-313.pyc [194 bytes]     - pem.cpython-313.pyc [2671 bytes]     - rfc1155.cpython-313.pyc [5425 bytes]     - rfc1157.cpython-313.pyc [6906 bytes]     - rfc1901.cpython-313.pyc [1057 bytes]     - rfc1902.cpython-313.pyc [7198 bytes]     - rfc1905.cpython-313.pyc [8528 bytes]     - rfc2251.cpython-313.pyc [36699 bytes]     - rfc2314.cpython-313.pyc [2570 bytes]     - rfc2315.cpython-313.pyc [18039 bytes]     - rfc2437.cpython-313.pyc [4408 bytes]     - rfc2459.cpython-313.pyc [88624 bytes]     - rfc2511.cpython-313.pyc [19118 bytes]     - rfc2560.cpython-313.pyc [15059 bytes]     - rfc2631.cpython-313.pyc [2027 bytes]     - rfc2634.cpython-313.pyc [14312 bytes]     - rfc2876.cpython-313.pyc [1702 bytes]     - rfc2985.cpython-313.pyc [15279 bytes]     - rfc2986.cpython-313.pyc [2549 bytes]     - rfc3058.cpython-313.pyc [1219 bytes]     - rfc3114.cpython-313.pyc [2371 bytes]     - rfc3125.cpython-313.pyc [29364 bytes]     - rfc3161.cpython-313.pyc [7349 bytes]     - rfc3274.cpython-313.pyc [1757 bytes]     - rfc3279.cpython-313.pyc [12381 bytes]     - rfc3280.cpython-313.pyc [77247 bytes]     - rfc3281.cpython-313.pyc [16908 bytes]     - rfc3370.cpython-313.pyc [3263 bytes]     - rfc3412.cpython-313.pyc [3459 bytes]     - rfc3414.cpython-313.pyc [1736 bytes]     - rfc3447.cpython-313.pyc [2756 bytes]     - rfc3537.cpython-313.pyc [788 bytes]     - rfc3560.cpython-313.pyc [1869 bytes]     - rfc3565.cpython-313.pyc [1808 bytes]     - rfc3657.cpython-313.pyc [2049 bytes]     - rfc3709.cpython-313.pyc [10452 bytes]     - rfc3739.cpython-313.pyc [7254 bytes]     - rfc3770.cpython-313.pyc [1766 bytes]     - rfc3779.cpython-313.pyc [5421 bytes]     - rfc3820.cpython-313.pyc [2165 bytes]     - rfc3852.cpython-313.pyc [35231 bytes]     - rfc4010.cpython-313.pyc [1706 bytes]     - rfc4043.cpython-313.pyc [1300 bytes]     - rfc4055.cpython-313.pyc [11871 bytes]     - rfc4073.cpython-313.pyc [1931 bytes]     - rfc4108.cpython-313.pyc [14275 bytes]     - rfc4210.cpython-313.pyc [40428 bytes]     - rfc4211.cpython-313.pyc [21138 bytes]     - rfc4334.cpython-313.pyc [1810 bytes]     - rfc4357.cpython-313.pyc [17058 bytes]     - rfc4387.cpython-313.pyc [422 bytes]     - rfc4476.cpython-313.pyc [2859 bytes]     - rfc4490.cpython-313.pyc [4368 bytes]     - rfc4491.cpython-313.pyc [741 bytes]     - rfc4683.cpython-313.pyc [2673 bytes]     - rfc4985.cpython-313.pyc [1150 bytes]     - rfc5035.cpython-313.pyc [4831 bytes]     - rfc5083.cpython-313.pyc [2469 bytes]     - rfc5084.cpython-313.pyc [3365 bytes]     - rfc5126.cpython-313.pyc [25679 bytes]     - rfc5208.cpython-313.pyc [3052 bytes]     - rfc5275.cpython-313.pyc [16631 bytes]     - rfc5280.cpython-313.pyc [80572 bytes]     - rfc5480.cpython-313.pyc [5235 bytes]     - rfc5636.cpython-313.pyc [3821 bytes]     - rfc5639.cpython-313.pyc [1032 bytes]     - rfc5649.cpython-313.pyc [1130 bytes]     - rfc5652.cpython-313.pyc [36240 bytes]     - rfc5697.cpython-313.pyc [2283 bytes]     - rfc5751.cpython-313.pyc [4053 bytes]     - rfc5752.cpython-313.pyc [1972 bytes]     - rfc5753.cpython-313.pyc [5159 bytes]     - rfc5755.cpython-313.pyc [19258 bytes]     - rfc5913.cpython-313.pyc [1189 bytes]     - rfc5914.cpython-313.pyc [6112 bytes]     - rfc5915.cpython-313.pyc [1668 bytes]     - rfc5916.cpython-313.pyc [780 bytes]     - rfc5917.cpython-313.pyc [1564 bytes]     - rfc5924.cpython-313.pyc [350 bytes]     - rfc5934.cpython-313.pyc [34530 bytes]     - rfc5940.cpython-313.pyc [1592 bytes]     - rfc5958.cpython-313.pyc [4521 bytes]     - rfc5990.cpython-313.pyc [6897 bytes]     - rfc6010.cpython-313.pyc [3202 bytes]     - rfc6019.cpython-313.pyc [1169 bytes]     - rfc6031.cpython-313.pyc [20116 bytes]     - rfc6032.cpython-313.pyc [2417 bytes]     - rfc6120.cpython-313.pyc [878 bytes]     - rfc6170.cpython-313.pyc [323 bytes]     - rfc6187.cpython-313.pyc [443 bytes]     - rfc6210.cpython-313.pyc [1168 bytes]     - rfc6211.cpython-313.pyc [2540 bytes]     - rfc6402.cpython-313.pyc [26307 bytes]     - rfc6482.cpython-313.pyc [3491 bytes]     - rfc6486.cpython-313.pyc [2920 bytes]     - rfc6487.cpython-313.pyc [434 bytes]     - rfc6664.cpython-313.pyc [5399 bytes]     - rfc6955.cpython-313.pyc [2976 bytes]     - rfc6960.cpython-313.pyc [12791 bytes]     - rfc7030.cpython-313.pyc [1934 bytes]     - rfc7191.cpython-313.pyc [9848 bytes]     - rfc7229.cpython-313.pyc [752 bytes]     - rfc7292.cpython-313.pyc [11800 bytes]     - rfc7296.cpython-313.pyc [1534 bytes]     - rfc7508.cpython-313.pyc [3648 bytes]     - rfc7585.cpython-313.pyc [1248 bytes]     - rfc7633.cpython-313.pyc [922 bytes]     - rfc7773.cpython-313.pyc [1742 bytes]     - rfc7894.cpython-313.pyc [3124 bytes]     - rfc7906.cpython-313.pyc [25603 bytes]     - rfc7914.cpython-313.pyc [2006 bytes]     - rfc8017.cpython-313.pyc [6085 bytes]     - rfc8018.cpython-313.pyc [8505 bytes]     - rfc8103.cpython-313.pyc [1303 bytes]     - rfc8209.cpython-313.pyc [357 bytes]     - rfc8226.cpython-313.pyc [6636 bytes]     - rfc8358.cpython-313.pyc [1166 bytes]     - rfc8360.cpython-313.pyc [809 bytes]     - rfc8398.cpython-313.pyc [1193 bytes]     - rfc8410.cpython-313.pyc [1503 bytes]     - rfc8418.cpython-313.pyc [1500 bytes]     - rfc8419.cpython-313.pyc [1605 bytes]     - rfc8479.cpython-313.pyc [1354 bytes]     - rfc8494.cpython-313.pyc [3689 bytes]     - rfc8520.cpython-313.pyc [1484 bytes]     - rfc8619.cpython-313.pyc [918 bytes]     - rfc8649.cpython-313.pyc [1202 bytes]     - rfc8692.cpython-313.pyc [1725 bytes]     - rfc8696.cpython-313.pyc [5268 bytes]     - rfc8702.cpython-313.pyc [3076 bytes]     - rfc8708.cpython-313.pyc [944 bytes]     - rfc8769.cpython-313.pyc [429 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyasn1_modules-0.4.2.dist-info\licenses
    - LICENSE.txt [1334 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\__pycache__
    - __init__.cpython-313.pyc [14152 bytes]     - _migration.cpython-313.pyc [10819 bytes]     - alias_generators.cpython-313.pyc [3178 bytes]     - aliases.cpython-313.pyc [6529 bytes]     - annotated_handlers.cpython-313.pyc [5203 bytes]     - class_validators.cpython-313.pyc [347 bytes]     - color.cpython-313.pyc [29783 bytes]     - config.cpython-313.pyc [7061 bytes]     - dataclasses.cpython-313.pyc [14714 bytes]     - datetime_parse.cpython-313.pyc [347 bytes]     - decorator.cpython-313.pyc [337 bytes]     - env_settings.cpython-313.pyc [343 bytes]     - error_wrappers.cpython-313.pyc [347 bytes]     - errors.cpython-313.pyc [7705 bytes]     - fields.cpython-313.pyc [63122 bytes]     - functional_serializers.cpython-313.pyc [17441 bytes]     - functional_validators.cpython-313.pyc [31536 bytes]     - generics.cpython-313.pyc [335 bytes]     - json_schema.cpython-313.pyc [111772 bytes]     - json.cpython-313.pyc [327 bytes]     - main.cpython-313.pyc [72741 bytes]     - mypy.cpython-313.pyc [63574 bytes]     - networks.cpython-313.pyc [48527 bytes]     - parse.cpython-313.pyc [329 bytes]     - root_model.cpython-313.pyc [7767 bytes]     - schema.cpython-313.pyc [331 bytes]     - tools.cpython-313.pyc [329 bytes]     - type_adapter.cpython-313.pyc [30665 bytes]     - types.cpython-313.pyc [93603 bytes]     - typing.cpython-313.pyc [327 bytes]     - utils.cpython-313.pyc [329 bytes]     - validate_call_decorator.cpython-313.pyc [5473 bytes]     - validators.cpython-313.pyc [339 bytes]     - version.cpython-313.pyc [3906 bytes]     - warnings.cpython-313.pyc [5952 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\_internal
    - __init__.py [0 bytes]     - _config.py [14253 bytes]     - _core_metadata.py [5162 bytes]     - _core_utils.py [6746 bytes]     - _dataclasses.py [8909 bytes]     - _decorators_v1.py [6185 bytes]     - _decorators.py [32638 bytes]     - _discriminated_union.py [25478 bytes]     - _docs_extraction.py [3831 bytes]     - _fields.py [20911 bytes]     - _forward_ref.py [611 bytes]     - _generate_schema.py [133100 bytes]     - _generics.py [23849 bytes]     - _git.py [809 bytes]     - _import_utils.py [402 bytes]     - _internal_dataclass.py [144 bytes]     - _known_annotated_metadata.py [16213 bytes]     - _mock_val_ser.py [8885 bytes]     - _model_construction.py [35228 bytes]     - _namespace_utils.py [12878 bytes]     - _repr.py [5081 bytes]     - _schema_gather.py [9114 bytes]     - _schema_generation_shared.py [4842 bytes]     - _serializers.py [1474 bytes]     - _signature.py [6779 bytes]     - _typing_extra.py [28216 bytes]     - _utils.py [15344 bytes]     - _validate_call.py [5321 bytes]     - _validators.py [20610 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\deprecated
    - __init__.py [0 bytes]     - class_validators.py [10245 bytes]     - config.py [2663 bytes]     - copy_internals.py [7616 bytes]     - decorator.py [10845 bytes]     - json.py [4657 bytes]     - parse.py [2511 bytes]     - tools.py [3330 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\experimental
    - __init__.py [328 bytes]     - arguments_schema.py [1866 bytes]     - pipeline.py [23910 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\plugin
    - __init__.py [6965 bytes]     - _loader.py [2167 bytes]     - _schema_validator.py [5267 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\v1
    - __init__.py [2946 bytes]     - _hypothesis_plugin.py [14847 bytes]     - annotated_types.py [3157 bytes]     - class_validators.py [14672 bytes]     - color.py [16844 bytes]     - config.py [6532 bytes]     - dataclasses.py [18172 bytes]     - datetime_parse.py [7724 bytes]     - decorator.py [10339 bytes]     - env_settings.py [14105 bytes]     - error_wrappers.py [5196 bytes]     - errors.py [17726 bytes]     - fields.py [50649 bytes]     - generics.py [17871 bytes]     - json.py [3390 bytes]     - main.py [44824 bytes]     - mypy.py [38949 bytes]     - networks.py [22124 bytes]     - parse.py [1821 bytes]     - py.typed [0 bytes]     - schema.py [47801 bytes]     - tools.py [2881 bytes]     - types.py [35455 bytes]     - typing.py [19720 bytes]     - utils.py [25989 bytes]     - validators.py [22187 bytes]     - version.py [1039 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\deprecated\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - class_validators.cpython-313.pyc [11816 bytes]     - config.cpython-313.pyc [4196 bytes]     - copy_internals.cpython-313.pyc [9129 bytes]     - decorator.cpython-313.pyc [14274 bytes]     - json.cpython-313.pyc [6142 bytes]     - parse.cpython-313.pyc [3524 bytes]     - tools.cpython-313.pyc [3494 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\experimental\__pycache__
    - __init__.cpython-313.pyc [535 bytes]     - arguments_schema.cpython-313.pyc [2323 bytes]     - pipeline.cpython-313.pyc [35617 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\plugin\__pycache__
    - __init__.cpython-313.pyc [8299 bytes]     - _loader.cpython-313.pyc [2438 bytes]     - _schema_validator.cpython-313.pyc [6976 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\v1\__pycache__
    - __init__.cpython-313.pyc [2196 bytes]     - _hypothesis_plugin.cpython-313.pyc [20480 bytes]     - annotated_types.cpython-313.pyc [3911 bytes]     - class_validators.cpython-313.pyc [19294 bytes]     - color.cpython-313.pyc [25633 bytes]     - config.cpython-313.pyc [8600 bytes]     - dataclasses.cpython-313.pyc [23008 bytes]     - datetime_parse.cpython-313.pyc [10545 bytes]     - decorator.cpython-313.pyc [14263 bytes]     - env_settings.cpython-313.pyc [18177 bytes]     - error_wrappers.cpython-313.pyc [9223 bytes]     - errors.cpython-313.pyc [31490 bytes]     - fields.cpython-313.pyc [58215 bytes]     - generics.cpython-313.pyc [17124 bytes]     - json.cpython-313.pyc [5174 bytes]     - main.cpython-313.pyc [49456 bytes]     - mypy.cpython-313.pyc [47138 bytes]     - networks.cpython-313.pyc [30348 bytes]     - parse.cpython-313.pyc [2878 bytes]     - schema.cpython-313.pyc [48708 bytes]     - tools.cpython-313.pyc [3817 bytes]     - types.cpython-313.pyc [49753 bytes]     - typing.cpython-313.pyc [22892 bytes]     - utils.cpython-313.pyc [35471 bytes]     - validators.cpython-313.pyc [32050 bytes]     - version.cpython-313.pyc [1951 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic\_internal\__pycache__
    - __init__.cpython-313.pyc [172 bytes]     - _config.cpython-313.pyc [15290 bytes]     - _core_metadata.cpython-313.pyc [4658 bytes]     - _core_utils.cpython-313.pyc [8205 bytes]     - _dataclasses.cpython-313.pyc [9392 bytes]     - _decorators_v1.cpython-313.pyc [8805 bytes]     - _decorators.cpython-313.pyc [35400 bytes]     - _discriminated_union.cpython-313.pyc [20764 bytes]     - _docs_extraction.cpython-313.pyc [5406 bytes]     - _fields.cpython-313.pyc [18672 bytes]     - _forward_ref.cpython-313.pyc [1317 bytes]     - _generate_schema.cpython-313.pyc [134000 bytes]     - _generics.cpython-313.pyc [24375 bytes]     - _git.cpython-313.pyc [1495 bytes]     - _import_utils.cpython-313.pyc [814 bytes]     - _internal_dataclass.cpython-313.pyc [323 bytes]     - _known_annotated_metadata.cpython-313.pyc [14032 bytes]     - _mock_val_ser.cpython-313.pyc [10930 bytes]     - _model_construction.cpython-313.pyc [34120 bytes]     - _namespace_utils.cpython-313.pyc [12120 bytes]     - _repr.cpython-313.pyc [7905 bytes]     - _schema_gather.cpython-313.pyc [7818 bytes]     - _schema_generation_shared.cpython-313.pyc [6193 bytes]     - _serializers.cpython-313.pyc [2105 bytes]     - _signature.cpython-313.pyc [6860 bytes]     - _typing_extra.cpython-313.pyc [27138 bytes]     - _utils.cpython-313.pyc [19667 bytes]     - _validate_call.cpython-313.pyc [7060 bytes]     - _validators.cpython-313.pyc [23391 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic-2.11.5.dist-info\licenses
    - LICENSE [1129 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic_core\__pycache__
    - __init__.cpython-313.pyc [3188 bytes]     - core_schema.cpython-313.pyc [146136 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pydantic_core-2.33.2.dist-info\licenses
    - LICENSE [1101 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing\__pycache__
    - __init__.cpython-313.pyc [7972 bytes]     - actions.cpython-313.pyc [8348 bytes]     - common.cpython-313.pyc [13228 bytes]     - core.cpython-313.pyc [270651 bytes]     - exceptions.cpython-313.pyc [13451 bytes]     - helpers.cpython-313.pyc [46245 bytes]     - results.cpython-313.pyc [33845 bytes]     - testing.cpython-313.pyc [17962 bytes]     - unicode.cpython-313.pyc [13898 bytes]     - util.cpython-313.pyc [20977 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing\diagram
    - __init__.py [26976 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing\tools
    - __init__.py [0 bytes]     - cvt_pyparsing_pep8_names.py [5369 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing\diagram\__pycache__
    - __init__.cpython-313.pyc [30020 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyparsing\tools\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - cvt_pyparsing_pep8_names.cpython-313.pyc [7206 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest\__pycache__
    - __init__.cpython-313.pyc [4159 bytes]     - __main__.cpython-313.pyc [408 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest_asyncio\__pycache__
    - __init__.cpython-313.pyc [425 bytes]     - _version.cpython-313.pyc [639 bytes]     - plugin.cpython-313.pyc [41245 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pytest_asyncio-1.0.0.dist-info\licenses
    - LICENSE [11324 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin
    - __init__.py [147 bytes]     - default.cfg [6995 bytes]     - IDLE.cfg [769 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\__pycache__
    - __init__.cpython-313.pyc [213 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\debugger
    - __init__.py [3173 bytes]     - configui.py [1211 bytes]     - dbgcon.py [860 bytes]     - dbgpyapp.py [1601 bytes]     - debugger.py [38766 bytes]     - fail.py [981 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\Demos
    - cmdserver.py [3119 bytes]     - createwin.py [2621 bytes]     - demoutils.py [1514 bytes]     - dibdemo.py [2364 bytes]     - dlgtest.py [4709 bytes]     - dyndlg.py [2925 bytes]     - fontdemo.py [2851 bytes]     - guidemo.py [1752 bytes]     - hiertest.py [3904 bytes]     - menutest.py [503 bytes]     - objdoc.py [1778 bytes]     - openGLDemo.py [11200 bytes]     - progressbar.py [2560 bytes]     - sliderdemo.py [2267 bytes]     - splittst.py [2924 bytes]     - threadedgui.py [6433 bytes]     - toolbar.py [3256 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\dialogs
    - __init__.py [0 bytes]     - ideoptions.py [5181 bytes]     - list.py [4761 bytes]     - login.py [4882 bytes]     - status.py [6862 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\docking
    - __init__.py [0 bytes]     - DockingBar.py [24001 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\framework
    - __init__.py [0 bytes]     - app.py [15112 bytes]     - bitmap.py [5679 bytes]     - cmdline.py [1508 bytes]     - dbgcommands.py [6993 bytes]     - dlgappcore.py [2222 bytes]     - help.py [5849 bytes]     - interact.py [37302 bytes]     - intpyapp.py [20642 bytes]     - intpydde.py [1884 bytes]     - scriptutils.py [23709 bytes]     - sgrepmdi.py [25431 bytes]     - startup.py [2582 bytes]     - stdin.py [6625 bytes]     - toolmenu.py [9506 bytes]     - window.py [549 bytes]     - winout.py [20821 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\idle
    - __init__.py [56 bytes]     - AutoExpand.py [2806 bytes]     - AutoIndent.py [20669 bytes]     - CallTips.py [6493 bytes]     - FormatParagraph.py [5850 bytes]     - IdleHistory.py [3138 bytes]     - PyParse.py [18708 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\mfc
    - __init__.py [0 bytes]     - activex.py [2690 bytes]     - afxres.py [15595 bytes]     - dialog.py [9086 bytes]     - docview.py [4240 bytes]     - object.py [2244 bytes]     - thread.py [613 bytes]     - window.py [1549 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\scintilla
    - __init__.py [17 bytes]     - bindings.py [6230 bytes]     - config.py [12708 bytes]     - configui.py [11450 bytes]     - control.py [20920 bytes]     - document.py [11730 bytes]     - find.py [17302 bytes]     - formatter.py [27200 bytes]     - IDLEenvironment.py [20040 bytes]     - keycodes.py [5461 bytes]     - scintillacon.py [76371 bytes]     - view.py [31767 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\tools
    - __init__.py [0 bytes]     - browseProjects.py [9819 bytes]     - browser.py [13408 bytes]     - hierlist.py [12308 bytes]     - regedit.py [13533 bytes]     - regpy.py [2307 bytes]     - TraceCollector.py [2520 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\debugger\__pycache__
    - __init__.cpython-313.pyc [4279 bytes]     - configui.cpython-313.pyc [2498 bytes]     - dbgcon.cpython-313.pyc [1272 bytes]     - dbgpyapp.cpython-313.pyc [2548 bytes]     - debugger.cpython-313.pyc [55546 bytes]     - fail.cpython-313.pyc [1409 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\Demos\__pycache__
    - cmdserver.cpython-313.pyc [5101 bytes]     - createwin.cpython-313.pyc [5175 bytes]     - demoutils.cpython-313.pyc [2380 bytes]     - dibdemo.cpython-313.pyc [4057 bytes]     - dlgtest.cpython-313.pyc [6712 bytes]     - dyndlg.cpython-313.pyc [4419 bytes]     - fontdemo.cpython-313.pyc [4043 bytes]     - guidemo.cpython-313.pyc [2191 bytes]     - hiertest.cpython-313.pyc [8054 bytes]     - menutest.cpython-313.pyc [916 bytes]     - objdoc.cpython-313.pyc [3161 bytes]     - openGLDemo.cpython-313.pyc [15929 bytes]     - progressbar.cpython-313.pyc [3258 bytes]     - sliderdemo.cpython-313.pyc [3325 bytes]     - splittst.cpython-313.pyc [5408 bytes]     - threadedgui.cpython-313.pyc [11484 bytes]     - toolbar.cpython-313.pyc [4974 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\Demos\app
    - basictimerapp.py [7913 bytes]     - customprint.py [6049 bytes]     - demoutils.py [1480 bytes]     - dlgappdemo.py [1429 bytes]     - dojobapp.py [1555 bytes]     - helloapp.py [1664 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\Demos\ocx
    - demoutils.py [1480 bytes]     - flash.py [3085 bytes]     - msoffice.py [5488 bytes]     - ocxserialtest.py [3718 bytes]     - ocxtest.py [6938 bytes]     - webbrowser.py [2391 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\Demos\app\__pycache__
    - basictimerapp.cpython-313.pyc [15198 bytes]     - customprint.cpython-313.pyc [10390 bytes]     - demoutils.cpython-313.pyc [2335 bytes]     - dlgappdemo.cpython-313.pyc [2670 bytes]     - dojobapp.cpython-313.pyc [3190 bytes]     - helloapp.cpython-313.pyc [1894 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\Demos\ocx\__pycache__
    - demoutils.cpython-313.pyc [2335 bytes]     - flash.cpython-313.pyc [5475 bytes]     - msoffice.cpython-313.pyc [8397 bytes]     - ocxserialtest.cpython-313.pyc [6756 bytes]     - ocxtest.cpython-313.pyc [11812 bytes]     - webbrowser.cpython-313.pyc [4282 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\dialogs\__pycache__
    - __init__.cpython-313.pyc [177 bytes]     - ideoptions.cpython-313.pyc [7905 bytes]     - list.cpython-313.pyc [8493 bytes]     - login.cpython-313.pyc [7514 bytes]     - status.cpython-313.pyc [12855 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\docking\__pycache__
    - __init__.cpython-313.pyc [177 bytes]     - DockingBar.cpython-313.pyc [33982 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\framework\__pycache__
    - __init__.cpython-313.pyc [179 bytes]     - app.cpython-313.pyc [21595 bytes]     - bitmap.cpython-313.pyc [8290 bytes]     - cmdline.cpython-313.pyc [2291 bytes]     - dbgcommands.cpython-313.pyc [10867 bytes]     - dlgappcore.cpython-313.pyc [4505 bytes]     - help.cpython-313.pyc [6418 bytes]     - interact.cpython-313.pyc [46311 bytes]     - intpyapp.cpython-313.pyc [24742 bytes]     - intpydde.cpython-313.pyc [3176 bytes]     - scriptutils.cpython-313.pyc [28477 bytes]     - sgrepmdi.cpython-313.pyc [36938 bytes]     - startup.cpython-313.pyc [1981 bytes]     - stdin.cpython-313.pyc [7851 bytes]     - toolmenu.cpython-313.pyc [13339 bytes]     - window.cpython-313.pyc [1179 bytes]     - winout.cpython-313.pyc [29165 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\framework\editor
    - __init__.py [3109 bytes]     - configui.py [11765 bytes]     - document.py [15133 bytes]     - editor.py [18776 bytes]     - frame.py [3162 bytes]     - ModuleBrowser.py [7282 bytes]     - template.py [2127 bytes]     - vss.py [3480 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\framework\editor\__pycache__
    - __init__.cpython-313.pyc [1896 bytes]     - configui.cpython-313.pyc [16865 bytes]     - document.cpython-313.pyc [18996 bytes]     - editor.cpython-313.pyc [23254 bytes]     - frame.cpython-313.pyc [3575 bytes]     - ModuleBrowser.cpython-313.pyc [13811 bytes]     - template.cpython-313.pyc [3691 bytes]     - vss.cpython-313.pyc [3407 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\framework\editor\color
    - __init__.py [0 bytes]     - coloreditor.py [26029 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\framework\editor\color\__pycache__
    - __init__.cpython-313.pyc [192 bytes]     - coloreditor.cpython-313.pyc [31320 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\idle\__pycache__
    - __init__.cpython-313.pyc [174 bytes]     - AutoExpand.cpython-313.pyc [4015 bytes]     - AutoIndent.cpython-313.pyc [21216 bytes]     - CallTips.cpython-313.pyc [8828 bytes]     - FormatParagraph.cpython-313.pyc [6982 bytes]     - IdleHistory.cpython-313.pyc [4855 bytes]     - PyParse.cpython-313.pyc [13404 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\mfc\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - activex.cpython-313.pyc [4462 bytes]     - afxres.cpython-313.pyc [19471 bytes]     - dialog.cpython-313.pyc [15279 bytes]     - docview.cpython-313.pyc [9381 bytes]     - object.cpython-313.pyc [3343 bytes]     - thread.cpython-313.pyc [1526 bytes]     - window.cpython-313.pyc [2615 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\scintilla\__pycache__
    - __init__.cpython-313.pyc [179 bytes]     - bindings.cpython-313.pyc [8410 bytes]     - config.cpython-313.pyc [16021 bytes]     - configui.cpython-313.pyc [18769 bytes]     - control.cpython-313.pyc [36896 bytes]     - document.cpython-313.pyc [14411 bytes]     - find.cpython-313.pyc [21424 bytes]     - formatter.cpython-313.pyc [28917 bytes]     - IDLEenvironment.cpython-313.pyc [25180 bytes]     - keycodes.cpython-313.pyc [6979 bytes]     - scintillacon.cpython-313.pyc [101969 bytes]     - view.cpython-313.pyc [38994 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pythonwin\pywin\tools\__pycache__
    - __init__.cpython-313.pyc [175 bytes]     - browseProjects.cpython-313.pyc [19245 bytes]     - browser.cpython-313.pyc [24685 bytes]     - hierlist.cpython-313.pyc [16318 bytes]     - regedit.cpython-313.pyc [22985 bytes]     - regpy.cpython-313.pyc [4655 bytes]     - TraceCollector.cpython-313.pyc [3322 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\python_dotenv-1.1.0.dist-info\licenses
    - LICENSE [1556 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\python_multipart\__pycache__
    - __init__.cpython-313.pyc [562 bytes]     - decoders.cpython-313.pyc [8181 bytes]     - exceptions.cpython-313.pyc [1857 bytes]     - multipart.cpython-313.pyc [67604 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\python_multipart-0.0.20.dist-info\licenses
    - LICENSE.txt [556 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyttsx3\__pycache__
    - __init__.cpython-313.pyc [1222 bytes]     - driver.cpython-313.pyc [8934 bytes]     - engine.cpython-313.pyc [8594 bytes]     - six.cpython-313.pyc [36773 bytes]     - voice.cpython-313.pyc [994 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyttsx3\drivers
    - __init__.py [830 bytes]     - _espeak.py [18530 bytes]     - dummy.py [6008 bytes]     - espeak.py [8477 bytes]     - nsss.py [5559 bytes]     - sapi5.py [6410 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\pyttsx3\drivers\__pycache__
    - __init__.cpython-313.pyc [1236 bytes]     - _espeak.cpython-313.pyc [20187 bytes]     - dummy.cpython-313.pyc [7351 bytes]     - espeak.cpython-313.pyc [13336 bytes]     - nsss.cpython-313.pyc [9896 bytes]     - sapi5.cpython-313.pyc [10374 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\requests\__pycache__
    - __init__.cpython-313.pyc [5404 bytes]     - __version__.cpython-313.pyc [555 bytes]     - _internal_utils.cpython-313.pyc [1989 bytes]     - adapters.cpython-313.pyc [27371 bytes]     - api.cpython-313.pyc [6841 bytes]     - auth.cpython-313.pyc [14203 bytes]     - certs.cpython-313.pyc [639 bytes]     - compat.cpython-313.pyc [2077 bytes]     - cookies.cpython-313.pyc [24998 bytes]     - exceptions.cpython-313.pyc [7981 bytes]     - help.cpython-313.pyc [4335 bytes]     - hooks.cpython-313.pyc [1065 bytes]     - models.cpython-313.pyc [35753 bytes]     - packages.cpython-313.pyc [1137 bytes]     - sessions.cpython-313.pyc [27357 bytes]     - status_codes.cpython-313.pyc [6015 bytes]     - structures.cpython-313.pyc [5618 bytes]     - utils.cpython-313.pyc [36479 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\rsa\__pycache__
    - __init__.cpython-313.pyc [1146 bytes]     - asn1.cpython-313.pyc [2246 bytes]     - cli.cpython-313.pyc [14886 bytes]     - common.cpython-313.pyc [5105 bytes]     - core.cpython-313.pyc [1744 bytes]     - key.cpython-313.pyc [34295 bytes]     - parallel.cpython-313.pyc [2646 bytes]     - pem.cpython-313.pyc [3950 bytes]     - pkcs1_v2.cpython-313.pyc [3475 bytes]     - pkcs1.cpython-313.pyc [16380 bytes]     - prime.cpython-313.pyc [4738 bytes]     - randnum.cpython-313.pyc [2314 bytes]     - transform.cpython-313.pyc [2147 bytes]     - util.cpython-313.pyc [3485 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sniffio\__pycache__
    - __init__.cpython-313.pyc [437 bytes]     - _impl.cpython-313.pyc [3140 bytes]     - _version.cpython-313.pyc [189 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sniffio\_tests
    - __init__.py [0 bytes]     - test_sniffio.py [2058 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sniffio\_tests\__pycache__
    - __init__.cpython-313.pyc [168 bytes]     - test_sniffio.cpython-313.pyc [3943 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speechrecognition-3.14.3.dist-info\licenses
    - LICENSE-FLAC.txt [18092 bytes]     - LICENSE.txt [1515 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\__pycache__
    - __init__.cpython-313.pyc [78982 bytes]     - __main__.cpython-313.pyc [1650 bytes]     - audio.cpython-313.pyc [14968 bytes]     - exceptions.cpython-313.pyc [1249 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\pocketsphinx-data


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\recognizers
    - __init__.py [0 bytes]     - google_cloud.py [6101 bytes]     - google.py [10149 bytes]     - pocketsphinx.py [7349 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\pocketsphinx-data\en-US
    - language-model.lm.bin [29208442 bytes]     - LICENSE.txt [1537 bytes]     - pronounciation-dictionary.dict [3240807 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\pocketsphinx-data\en-US\acoustic-model
    - feat.params [165 bytes]     - mdef [2959176 bytes]     - means [838732 bytes]     - noisedict [56 bytes]     - README [1617 bytes]     - sendump [1969024 bytes]     - transition_matrices [2080 bytes]     - variances [838732 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\recognizers\__pycache__
    - __init__.cpython-313.pyc [184 bytes]     - google_cloud.cpython-313.pyc [7347 bytes]     - google.cpython-313.pyc [12010 bytes]     - pocketsphinx.cpython-313.pyc [8938 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\recognizers\whisper_api
    - __init__.py [0 bytes]     - base.py [805 bytes]     - groq.py [1638 bytes]     - openai.py [3143 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\recognizers\whisper_local
    - __init__.py [0 bytes]     - base.py [1265 bytes]     - faster_whisper.py [3196 bytes]     - whisper.py [3340 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\recognizers\whisper_api\__pycache__
    - __init__.cpython-313.pyc [196 bytes]     - base.cpython-313.pyc [1723 bytes]     - groq.cpython-313.pyc [2293 bytes]     - openai.cpython-313.pyc [4112 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\speech_recognition\recognizers\whisper_local\__pycache__
    - __init__.cpython-313.pyc [198 bytes]     - base.cpython-313.pyc [2267 bytes]     - faster_whisper.cpython-313.pyc [4728 bytes]     - whisper.cpython-313.pyc [4738 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\__pycache__
    - __init__.cpython-313.pyc [9402 bytes]     - events.cpython-313.pyc [537 bytes]     - exc.cpython-313.pyc [32063 bytes]     - inspection.cpython-313.pyc [6697 bytes]     - log.cpython-313.pyc [11598 bytes]     - schema.cpython-313.pyc [2355 bytes]     - types.cpython-313.pyc [2264 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\connectors
    - __init__.py [494 bytes]     - aioodbc.py [5462 bytes]     - asyncio.py [6351 bytes]     - pyodbc.py [8714 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\cyextension
    - __init__.py [250 bytes]     - collections.cp313-win_amd64.pyd [167936 bytes]     - collections.pyx [12980 bytes]     - immutabledict.cp313-win_amd64.pyd [68096 bytes]     - immutabledict.pxd [299 bytes]     - immutabledict.pyx [3668 bytes]     - processors.cp313-win_amd64.pyd [57856 bytes]     - processors.pyx [1860 bytes]     - resultproxy.cp313-win_amd64.pyd [58880 bytes]     - resultproxy.pyx [2827 bytes]     - util.cp313-win_amd64.pyd [70144 bytes]     - util.pyx [2621 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects
    - __init__.py [1831 bytes]     - _typing.py [1001 bytes]     - type_migration_guidelines.txt [8384 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\engine
    - __init__.py [2880 bytes]     - _py_processors.py [3880 bytes]     - _py_row.py [3915 bytes]     - _py_util.py [2558 bytes]     - base.py [126181 bytes]     - characteristics.py [4920 bytes]     - create.py [34095 bytes]     - cursor.py [78670 bytes]     - default.py [87686 bytes]     - events.py [38365 bytes]     - interfaces.py [116869 bytes]     - mock.py [4290 bytes]     - processors.py [2440 bytes]     - reflection.py [77667 bytes]     - result.py [80193 bytes]     - row.py [12431 bytes]     - strategies.py [461 bytes]     - url.py [31991 bytes]     - util.py [5849 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\event
    - __init__.py [1022 bytes]     - api.py [8333 bytes]     - attr.py [21406 bytes]     - base.py [15726 bytes]     - legacy.py [8473 bytes]     - registry.py [11534 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext
    - __init__.py [333 bytes]     - associationproxy.py [68075 bytes]     - automap.py [63376 bytes]     - baked.py [18323 bytes]     - compiler.py [21489 bytes]     - horizontal_shard.py [17169 bytes]     - hybrid.py [54064 bytes]     - indexable.py [11410 bytes]     - instrumentation.py [16157 bytes]     - mutable.py [38655 bytes]     - orderinglist.py [14858 bytes]     - serializer.py [6354 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\future
    - __init__.py [528 bytes]     - engine.py [510 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\orm
    - __init__.py [8633 bytes]     - _orm_constructors.py [106216 bytes]     - _typing.py [5152 bytes]     - attributes.py [95369 bytes]     - base.py [28474 bytes]     - bulk_persistence.py [74786 bytes]     - clsregistry.py [18523 bytes]     - collections.py [53879 bytes]     - context.py [118414 bytes]     - decl_api.py [66847 bytes]     - decl_base.py [85433 bytes]     - dependency.py [48925 bytes]     - descriptor_props.py [38308 bytes]     - dynamic.py [10116 bytes]     - evaluator.py [12732 bytes]     - events.py [131052 bytes]     - exc.py [7873 bytes]     - identity.py [9551 bytes]     - instrumentation.py [25075 bytes]     - interfaces.py [50287 bytes]     - loading.py [59959 bytes]     - mapped_collection.py [20239 bytes]     - mapper.py [176128 bytes]     - path_registry.py [26727 bytes]     - persistence.py [63483 bytes]     - properties.py [30385 bytes]     - query.py [122177 bytes]     - relationships.py [132333 bytes]     - scoping.py [80762 bytes]     - session.py [201172 bytes]     - state_changes.py [7013 bytes]     - state.py [38813 bytes]     - strategies.py [123276 bytes]     - strategy_options.py [87571 bytes]     - sync.py [5943 bytes]     - unitofwork.py [27829 bytes]     - util.py [83308 bytes]     - writeonly.py [22983 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\pool
    - __init__.py [1848 bytes]     - base.py [53848 bytes]     - events.py [13521 bytes]     - impl.py [19525 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\sql
    - __init__.py [5965 bytes]     - _dml_constructors.py [3927 bytes]     - _elements_constructors.py [64968 bytes]     - _orm_types.py [645 bytes]     - _py_util.py [2248 bytes]     - _selectable_constructors.py [21166 bytes]     - _typing.py [13495 bytes]     - annotation.py [18830 bytes]     - base.py [76338 bytes]     - cache_key.py [34710 bytes]     - coercions.py [42069 bytes]     - compiler.py [288311 bytes]     - crud.py [58526 bytes]     - ddl.py [49391 bytes]     - default_comparator.py [17259 bytes]     - dml.py [68069 bytes]     - elements.py [183430 bytes]     - events.py [18770 bytes]     - expression.py [7748 bytes]     - functions.py [66947 bytes]     - lambdas.py [50544 bytes]     - naming.py [7070 bytes]     - operators.py [79415 bytes]     - roles.py [7985 bytes]     - schema.py [236616 bytes]     - selectable.py [248672 bytes]     - sqltypes.py [135722 bytes]     - traversals.py [34688 bytes]     - type_api.py [87277 bytes]     - util.py [49615 bytes]     - visitors.py [37486 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing
    - __init__.py [3256 bytes]     - assertions.py [32444 bytes]     - assertsql.py [17333 bytes]     - asyncio.py [3965 bytes]     - config.py [12481 bytes]     - engines.py [13888 bytes]     - entities.py [3471 bytes]     - exclusions.py [12895 bytes]     - pickleable.py [2988 bytes]     - profiling.py [10472 bytes]     - provision.py [15204 bytes]     - requirements.py [56884 bytes]     - schema.py [6737 bytes]     - util.py [15109 bytes]     - warnings.py [1598 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\util
    - __init__.py [8474 bytes]     - _collections.py [20868 bytes]     - _concurrency_py3k.py [9458 bytes]     - _has_cy.py [1287 bytes]     - _py_collections.py [17255 bytes]     - compat.py [9151 bytes]     - concurrency.py [3412 bytes]     - deprecations.py [12413 bytes]     - langhelpers.py [70674 bytes]     - preloaded.py [6054 bytes]     - queue.py [10507 bytes]     - tool_support.py [6336 bytes]     - topological.py [3571 bytes]     - typing.py [23199 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\connectors\__pycache__
    - __init__.cpython-313.pyc [622 bytes]     - aioodbc.cpython-313.pyc [7289 bytes]     - asyncio.cpython-313.pyc [12574 bytes]     - pyodbc.cpython-313.pyc [9704 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\cyextension\__pycache__
    - __init__.cpython-313.pyc [176 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\__pycache__
    - __init__.cpython-313.pyc [1876 bytes]     - _typing.cpython-313.pyc [1066 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\mssql
    - __init__.py [1968 bytes]     - aioodbc.py [2084 bytes]     - base.py [136725 bytes]     - information_schema.py [8338 bytes]     - json.py [4885 bytes]     - provision.py [5755 bytes]     - pymssql.py [4223 bytes]     - pyodbc.py [27933 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\mysql
    - __init__.py [2310 bytes]     - aiomysql.py [10348 bytes]     - asyncmy.py [10420 bytes]     - base.py [128357 bytes]     - cymysql.py [2384 bytes]     - dml.py [7993 bytes]     - enumerated.py [8690 bytes]     - expression.py [4264 bytes]     - json.py [2350 bytes]     - mariadb.py [1715 bytes]     - mariadbconnector.py [8900 bytes]     - mysqlconnector.py [8439 bytes]     - mysqldb.py [9831 bytes]     - provision.py [3832 bytes]     - pymysql.py [4218 bytes]     - pyodbc.py [4437 bytes]     - reflection.py [23519 bytes]     - reserved_words.py [9829 bytes]     - types.py [25028 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\oracle
    - __init__.py [1859 bytes]     - base.py [140804 bytes]     - cx_oracle.py [58164 bytes]     - dictionary.py [20026 bytes]     - oracledb.py [34718 bytes]     - provision.py [8533 bytes]     - types.py [9374 bytes]     - vector.py [8127 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\postgresql
    - __init__.py [4059 bytes]     - _psycopg_common.py [5860 bytes]     - array.py [17495 bytes]     - asyncpg.py [42574 bytes]     - base.py [189193 bytes]     - dml.py [12465 bytes]     - ext.py [17883 bytes]     - hstore.py [12340 bytes]     - json.py [13209 bytes]     - named_types.py [18818 bytes]     - operators.py [2937 bytes]     - pg_catalog.py [9944 bytes]     - pg8000.py [19304 bytes]     - provision.py [5945 bytes]     - psycopg.py [24109 bytes]     - psycopg2.py [32924 bytes]     - psycopg2cffi.py [1817 bytes]     - ranges.py [34009 bytes]     - types.py [7942 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\sqlite
    - __init__.py [1239 bytes]     - aiosqlite.py [12656 bytes]     - base.py [105514 bytes]     - dml.py [9401 bytes]     - json.py [2869 bytes]     - provision.py [5792 bytes]     - pysqlcipher.py [5528 bytes]     - pysqlite.py [25984 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\mssql\__pycache__
    - __init__.cpython-313.pyc [1644 bytes]     - aioodbc.cpython-313.pyc [2465 bytes]     - base.cpython-313.pyc [154891 bytes]     - information_schema.cpython-313.pyc [7708 bytes]     - json.cpython-313.pyc [5235 bytes]     - provision.cpython-313.pyc [7383 bytes]     - pymssql.cpython-313.pyc [6125 bytes]     - pyodbc.cpython-313.pyc [31374 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\__pycache__
    - __init__.cpython-313.pyc [1973 bytes]     - aiomysql.cpython-313.pyc [17229 bytes]     - asyncmy.cpython-313.pyc [17659 bytes]     - base.cpython-313.pyc [143200 bytes]     - cymysql.cpython-313.pyc [3224 bytes]     - dml.cpython-313.pyc [8015 bytes]     - enumerated.cpython-313.pyc [9933 bytes]     - expression.cpython-313.pyc [4896 bytes]     - json.cpython-313.pyc [3569 bytes]     - mariadb.cpython-313.pyc [2492 bytes]     - mariadbconnector.cpython-313.pyc [11920 bytes]     - mysqlconnector.cpython-313.pyc [12270 bytes]     - mysqldb.cpython-313.pyc [11931 bytes]     - provision.cpython-313.pyc [4413 bytes]     - pymysql.cpython-313.pyc [5391 bytes]     - pyodbc.cpython-313.pyc [5368 bytes]     - reflection.cpython-313.pyc [24757 bytes]     - reserved_words.cpython-313.pyc [4373 bytes]     - types.cpython-313.pyc [29358 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\oracle\__pycache__
    - __init__.cpython-313.pyc [1558 bytes]     - base.cpython-313.pyc [153566 bytes]     - cx_oracle.cpython-313.pyc [62024 bytes]     - dictionary.cpython-313.pyc [24232 bytes]     - oracledb.cpython-313.pyc [41743 bytes]     - provision.cpython-313.pyc [10853 bytes]     - types.cpython-313.pyc [13352 bytes]     - vector.cpython-313.pyc [9006 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\postgresql\__pycache__
    - __init__.cpython-313.pyc [3414 bytes]     - _psycopg_common.cpython-313.pyc [8081 bytes]     - array.cpython-313.pyc [18813 bytes]     - asyncpg.cpython-313.pyc [59569 bytes]     - base.cpython-313.pyc [211481 bytes]     - dml.cpython-313.pyc [12261 bytes]     - ext.cpython-313.pyc [19826 bytes]     - hstore.cpython-313.pyc [15126 bytes]     - json.cpython-313.pyc [14482 bytes]     - named_types.cpython-313.pyc [23326 bytes]     - operators.cpython-313.pyc [2001 bytes]     - pg_catalog.cpython-313.pyc [11427 bytes]     - pg8000.cpython-313.pyc [30877 bytes]     - provision.cpython-313.pyc [7749 bytes]     - psycopg.cpython-313.pyc [38403 bytes]     - psycopg2.cpython-313.pyc [36299 bytes]     - psycopg2cffi.cpython-313.pyc [2184 bytes]     - ranges.cpython-313.pyc [35357 bytes]     - types.cpython-313.pyc [11691 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\dialects\sqlite\__pycache__
    - __init__.cpython-313.pyc [1040 bytes]     - aiosqlite.cpython-313.pyc [18177 bytes]     - base.cpython-313.pyc [106970 bytes]     - dml.cpython-313.pyc [9737 bytes]     - json.cpython-313.pyc [3874 bytes]     - provision.cpython-313.pyc [7332 bytes]     - pysqlcipher.cpython-313.pyc [6289 bytes]     - pysqlite.cpython-313.pyc [28997 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\engine\__pycache__
    - __init__.cpython-313.pyc [2255 bytes]     - _py_processors.cpython-313.pyc [4550 bytes]     - _py_row.cpython-313.pyc [5869 bytes]     - _py_util.cpython-313.pyc [2326 bytes]     - base.cpython-313.pyc [126083 bytes]     - characteristics.cpython-313.pyc [6823 bytes]     - create.cpython-313.pyc [33338 bytes]     - cursor.cpython-313.pyc [78010 bytes]     - default.cpython-313.pyc [89920 bytes]     - events.cpython-313.pyc [36696 bytes]     - interfaces.cpython-313.pyc [94007 bytes]     - mock.cpython-313.pyc [5599 bytes]     - processors.cpython-313.pyc [1289 bytes]     - reflection.cpython-313.pyc [77444 bytes]     - result.cpython-313.pyc [88697 bytes]     - row.cpython-313.pyc [17118 bytes]     - strategies.cpython-313.pyc [596 bytes]     - url.cpython-313.pyc [33121 bytes]     - util.cpython-313.pyc [6825 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\event\__pycache__
    - __init__.cpython-313.pyc [818 bytes]     - api.cpython-313.pyc [8615 bytes]     - attr.cpython-313.pyc [30919 bytes]     - base.cpython-313.pyc [20358 bytes]     - legacy.cpython-313.pyc [9408 bytes]     - registry.cpython-313.pyc [12848 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\__pycache__
    - __init__.cpython-313.pyc [331 bytes]     - associationproxy.cpython-313.pyc [87128 bytes]     - automap.cpython-313.pyc [56831 bytes]     - baked.cpython-313.pyc [22941 bytes]     - compiler.cpython-313.pyc [21369 bytes]     - horizontal_shard.cpython-313.pyc [17675 bytes]     - hybrid.cpython-313.pyc [59096 bytes]     - indexable.cpython-313.pyc [12029 bytes]     - instrumentation.cpython-313.pyc [19852 bytes]     - mutable.cpython-313.pyc [46143 bytes]     - orderinglist.cpython-313.pyc [17012 bytes]     - serializer.cpython-313.pyc [8278 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\asyncio
    - __init__.py [1342 bytes]     - base.py [9313 bytes]     - engine.py [49789 bytes]     - exc.py [660 bytes]     - result.py [31516 bytes]     - scoping.py [54183 bytes]     - session.py [65704 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\declarative
    - __init__.py [1883 bytes]     - extensions.py [20095 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\mypy
    - __init__.py [247 bytes]     - apply.py [10915 bytes]     - decl_class.py [17899 bytes]     - infer.py [19957 bytes]     - names.py [10814 bytes]     - plugin.py [10053 bytes]     - util.py [10317 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\__pycache__
    - __init__.cpython-313.pyc [954 bytes]     - base.cpython-313.pyc [11348 bytes]     - engine.cpython-313.pyc [54628 bytes]     - exc.cpython-313.pyc [1104 bytes]     - result.cpython-313.pyc [35158 bytes]     - scoping.cpython-313.pyc [52372 bytes]     - session.cpython-313.pyc [67675 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\declarative\__pycache__
    - __init__.cpython-313.pyc [1982 bytes]     - extensions.cpython-313.pyc [20754 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\ext\mypy\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - apply.cpython-313.pyc [10800 bytes]     - decl_class.cpython-313.pyc [16202 bytes]     - infer.cpython-313.pyc [15982 bytes]     - names.cpython-313.pyc [11285 bytes]     - plugin.cpython-313.pyc [12836 bytes]     - util.cpython-313.pyc [14410 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\future\__pycache__
    - __init__.cpython-313.pyc [441 bytes]     - engine.cpython-313.pyc [374 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\orm\__pycache__
    - __init__.cpython-313.pyc [6324 bytes]     - _orm_constructors.cpython-313.pyc [99616 bytes]     - _typing.cpython-313.pyc [6948 bytes]     - attributes.cpython-313.pyc [99575 bytes]     - base.cpython-313.pyc [30324 bytes]     - bulk_persistence.cpython-313.pyc [65865 bytes]     - clsregistry.cpython-313.pyc [24374 bytes]     - collections.cpython-313.pyc [61191 bytes]     - context.cpython-313.pyc [106392 bytes]     - decl_api.cpython-313.pyc [65180 bytes]     - decl_base.cpython-313.pyc [71395 bytes]     - dependency.cpython-313.pyc [44424 bytes]     - descriptor_props.cpython-313.pyc [49740 bytes]     - dynamic.cpython-313.pyc [13067 bytes]     - evaluator.cpython-313.pyc [17049 bytes]     - events.cpython-313.pyc [125474 bytes]     - exc.cpython-313.pyc [10423 bytes]     - identity.cpython-313.pyc [12821 bytes]     - instrumentation.cpython-313.pyc [31518 bytes]     - interfaces.cpython-313.pyc [52937 bytes]     - loading.cpython-313.pyc [48481 bytes]     - mapped_collection.cpython-313.pyc [21644 bytes]     - mapper.cpython-313.pyc [166664 bytes]     - path_registry.cpython-313.pyc [32267 bytes]     - persistence.cpython-313.pyc [49952 bytes]     - properties.cpython-313.pyc [33599 bytes]     - query.cpython-313.pyc [120751 bytes]     - relationships.cpython-313.pyc [130540 bytes]     - scoping.cpython-313.pyc [76004 bytes]     - session.cpython-313.pyc [195330 bytes]     - state_changes.cpython-313.pyc [7081 bytes]     - state.cpython-313.pyc [44747 bytes]     - strategies.cpython-313.pyc [108315 bytes]     - strategy_options.cpython-313.pyc [84970 bytes]     - sync.cpython-313.pyc [6548 bytes]     - unitofwork.cpython-313.pyc [35013 bytes]     - util.cpython-313.pyc [85879 bytes]     - writeonly.cpython-313.pyc [28953 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\pool\__pycache__
    - __init__.cpython-313.pyc [1492 bytes]     - base.cpython-313.pyc [56061 bytes]     - events.cpython-313.pyc [13297 bytes]     - impl.cpython-313.pyc [25865 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\sql\__pycache__
    - __init__.cpython-313.pyc [4651 bytes]     - _dml_constructors.cpython-313.pyc [3733 bytes]     - _elements_constructors.cpython-313.pyc [61475 bytes]     - _orm_types.cpython-313.pyc [605 bytes]     - _py_util.cpython-313.pyc [2980 bytes]     - _selectable_constructors.cpython-313.pyc [22503 bytes]     - _typing.cpython-313.pyc [15085 bytes]     - annotation.cpython-313.pyc [21420 bytes]     - base.cpython-313.pyc [97055 bytes]     - cache_key.cpython-313.pyc [35889 bytes]     - coercions.cpython-313.pyc [51169 bytes]     - compiler.cpython-313.pyc [282661 bytes]     - crud.cpython-313.pyc [47755 bytes]     - ddl.cpython-313.pyc [58110 bytes]     - default_comparator.cpython-313.pyc [19402 bytes]     - dml.cpython-313.pyc [71414 bytes]     - elements.cpython-313.pyc [210593 bytes]     - events.cpython-313.pyc [17380 bytes]     - expression.cpython-313.pyc [5127 bytes]     - functions.cpython-313.pyc [74946 bytes]     - lambdas.cpython-313.pyc [54960 bytes]     - naming.cpython-313.pyc [8684 bytes]     - operators.cpython-313.pyc [84003 bytes]     - roles.cpython-313.pyc [12992 bytes]     - schema.cpython-313.pyc [236847 bytes]     - selectable.cpython-313.pyc [251294 bytes]     - sqltypes.cpython-313.pyc [152079 bytes]     - traversals.cpython-313.pyc [42765 bytes]     - type_api.cpython-313.pyc [83574 bytes]     - util.cpython-313.pyc [55533 bytes]     - visitors.cpython-313.pyc [35722 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\__pycache__
    - __init__.cpython-313.pyc [3309 bytes]     - assertions.cpython-313.pyc [43111 bytes]     - assertsql.cpython-313.pyc [21085 bytes]     - asyncio.cpython-313.pyc [4101 bytes]     - config.cpython-313.pyc [17627 bytes]     - engines.cpython-313.pyc [21955 bytes]     - entities.cpython-313.pyc [5199 bytes]     - exclusions.cpython-313.pyc [21972 bytes]     - pickleable.cpython-313.pyc [7136 bytes]     - profiling.cpython-313.pyc [13285 bytes]     - provision.cpython-313.pyc [21030 bytes]     - requirements.cpython-313.pyc [88141 bytes]     - schema.cpython-313.pyc [9109 bytes]     - util.cpython-313.pyc [21856 bytes]     - warnings.cpython-313.pyc [1929 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\fixtures
    - __init__.py [1226 bytes]     - base.py [12622 bytes]     - mypy.py [13087 bytes]     - orm.py [6322 bytes]     - sql.py [16403 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\plugin
    - __init__.py [253 bytes]     - bootstrap.py [1736 bytes]     - plugin_base.py [22357 bytes]     - pytestplugin.py [28491 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\suite
    - __init__.py [741 bytes]     - test_cte.py [6662 bytes]     - test_ddl.py [12420 bytes]     - test_deprecations.py [5490 bytes]     - test_dialect.py [23648 bytes]     - test_insert.py [19454 bytes]     - test_reflection.py [114580 bytes]     - test_results.py [17546 bytes]     - test_rowcount.py [8158 bytes]     - test_select.py [64049 bytes]     - test_sequence.py [10240 bytes]     - test_types.py [70158 bytes]     - test_unicode_ddl.py [6330 bytes]     - test_update_delete.py [4133 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\fixtures\__pycache__
    - __init__.cpython-313.pyc [890 bytes]     - base.cpython-313.pyc [13726 bytes]     - mypy.cpython-313.pyc [13829 bytes]     - orm.cpython-313.pyc [11682 bytes]     - sql.cpython-313.pyc [22652 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\plugin\__pycache__
    - __init__.cpython-313.pyc [179 bytes]     - bootstrap.cpython-313.pyc [2132 bytes]     - plugin_base.cpython-313.pyc [28105 bytes]     - pytestplugin.cpython-313.pyc [33632 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\testing\suite\__pycache__
    - __init__.cpython-313.pyc [546 bytes]     - test_cte.cpython-313.pyc [9914 bytes]     - test_ddl.cpython-313.pyc [18743 bytes]     - test_deprecations.cpython-313.pyc [9061 bytes]     - test_dialect.cpython-313.pyc [34821 bytes]     - test_insert.cpython-313.pyc [25234 bytes]     - test_reflection.cpython-313.pyc [143845 bytes]     - test_results.cpython-313.pyc [25523 bytes]     - test_rowcount.cpython-313.pyc [10398 bytes]     - test_select.cpython-313.pyc [114756 bytes]     - test_sequence.cpython-313.pyc [14918 bytes]     - test_types.cpython-313.pyc [99662 bytes]     - test_unicode_ddl.cpython-313.pyc [7589 bytes]     - test_update_delete.cpython-313.pyc [7584 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy\util\__pycache__
    - __init__.cpython-313.pyc [5649 bytes]     - _collections.cpython-313.pyc [32219 bytes]     - _concurrency_py3k.cpython-313.pyc [11145 bytes]     - _has_cy.cpython-313.pyc [1088 bytes]     - _py_collections.cpython-313.pyc [30080 bytes]     - compat.cpython-313.pyc [12927 bytes]     - concurrency.cpython-313.pyc [4193 bytes]     - deprecations.cpython-313.pyc [13730 bytes]     - langhelpers.cpython-313.pyc [88472 bytes]     - preloaded.cpython-313.pyc [5878 bytes]     - queue.cpython-313.pyc [14939 bytes]     - tool_support.cpython-313.pyc [8882 bytes]     - topological.cpython-313.pyc [3949 bytes]     - typing.cpython-313.pyc [27019 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\sqlalchemy-2.0.41.dist-info\licenses
    - LICENSE [1119 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\starlette\__pycache__
    - __init__.cpython-313.pyc [190 bytes]     - _exception_handler.cpython-313.pyc [3117 bytes]     - _utils.cpython-313.pyc [5562 bytes]     - applications.cpython-313.pyc [12736 bytes]     - authentication.cpython-313.pyc [8187 bytes]     - background.cpython-313.pyc [2707 bytes]     - concurrency.cpython-313.pyc [3303 bytes]     - config.cpython-313.pyc [7685 bytes]     - convertors.cpython-313.pyc [4992 bytes]     - datastructures.cpython-313.pyc [40070 bytes]     - endpoints.cpython-313.pyc [7956 bytes]     - exceptions.cpython-313.pyc [2484 bytes]     - formparsers.cpython-313.pyc [14506 bytes]     - requests.cpython-313.pyc [16693 bytes]     - responses.cpython-313.pyc [29626 bytes]     - routing.cpython-313.pyc [44752 bytes]     - schemas.cpython-313.pyc [7222 bytes]     - staticfiles.cpython-313.pyc [11750 bytes]     - status.cpython-313.pyc [3617 bytes]     - templating.cpython-313.pyc [10135 bytes]     - testclient.cpython-313.pyc [33985 bytes]     - types.cpython-313.pyc [1772 bytes]     - websockets.cpython-313.pyc [12082 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\starlette\middleware
    - __init__.py [1224 bytes]     - authentication.py [1791 bytes]     - base.py [8971 bytes]     - cors.py [7051 bytes]     - errors.py [8066 bytes]     - exceptions.py [2791 bytes]     - gzip.py [5697 bytes]     - httpsredirect.py [848 bytes]     - sessions.py [3566 bytes]     - trustedhost.py [2203 bytes]     - wsgi.py [5386 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\starlette\middleware\__pycache__
    - __init__.cpython-313.pyc [2630 bytes]     - authentication.cpython-313.pyc [2909 bytes]     - base.cpython-313.pyc [11563 bytes]     - cors.cpython-313.pyc [7621 bytes]     - errors.cpython-313.pyc [10025 bytes]     - exceptions.cpython-313.pyc [4156 bytes]     - gzip.cpython-313.pyc [8672 bytes]     - httpsredirect.cpython-313.pyc [1787 bytes]     - sessions.cpython-313.pyc [4732 bytes]     - trustedhost.cpython-313.pyc [3240 bytes]     - wsgi.cpython-313.pyc [8679 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\starlette-0.46.2.dist-info\licenses
    - LICENSE.md [1518 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tests\__pycache__
    - __init__.cpython-313.pyc [159 bytes]     - test_audio.cpython-313.pyc [13918 bytes]     - test_recognition.cpython-313.pyc [9821 bytes]     - test_special_features.cpython-313.pyc [2833 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tqdm\__pycache__
    - __init__.cpython-313.pyc [1646 bytes]     - __main__.cpython-313.pyc [212 bytes]     - _dist_ver.cpython-313.pyc [186 bytes]     - _main.cpython-313.pyc [454 bytes]     - _monitor.cpython-313.pyc [4265 bytes]     - _tqdm_gui.cpython-313.pyc [462 bytes]     - _tqdm_notebook.cpython-313.pyc [483 bytes]     - _tqdm_pandas.cpython-313.pyc [1389 bytes]     - _tqdm.cpython-313.pyc [449 bytes]     - _utils.cpython-313.pyc [861 bytes]     - asyncio.cpython-313.pyc [4509 bytes]     - auto.cpython-313.pyc [1373 bytes]     - autonotebook.cpython-313.pyc [1228 bytes]     - cli.cpython-313.pyc [15633 bytes]     - dask.cpython-313.pyc [2549 bytes]     - gui.cpython-313.pyc [7939 bytes]     - keras.cpython-313.pyc [6920 bytes]     - notebook.cpython-313.pyc [11806 bytes]     - rich.cpython-313.pyc [7246 bytes]     - std.cpython-313.pyc [63679 bytes]     - tk.cpython-313.pyc [9977 bytes]     - utils.cpython-313.pyc [18860 bytes]     - version.cpython-313.pyc [583 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tqdm\contrib
    - __init__.py [2494 bytes]     - bells.py [837 bytes]     - concurrent.py [3986 bytes]     - discord.py [5243 bytes]     - itertools.py [774 bytes]     - logging.py [3760 bytes]     - slack.py [4007 bytes]     - telegram.py [5008 bytes]     - utils_worker.py [1207 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\tqdm\contrib\__pycache__
    - __init__.cpython-313.pyc [4219 bytes]     - bells.cpython-313.pyc [1368 bytes]     - concurrent.cpython-313.pyc [4517 bytes]     - discord.cpython-313.pyc [8144 bytes]     - itertools.cpython-313.pyc [1401 bytes]     - logging.cpython-313.pyc [5088 bytes]     - slack.cpython-313.pyc [6105 bytes]     - telegram.cpython-313.pyc [7783 bytes]     - utils_worker.cpython-313.pyc [2135 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\typing_extensions-4.13.2.dist-info\licenses
    - LICENSE [13936 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\typing_inspection\__pycache__
    - __init__.cpython-313.pyc [171 bytes]     - introspection.cpython-313.pyc [17664 bytes]     - typing_objects.cpython-313.pyc [17184 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\typing_inspection-0.4.1.dist-info\licenses
    - LICENSE [1090 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uritemplate\__pycache__
    - __init__.cpython-313.pyc [1300 bytes]     - api.cpython-313.pyc [2880 bytes]     - orderedset.cpython-313.pyc [5113 bytes]     - template.cpython-313.pyc [6838 bytes]     - variable.cpython-313.pyc [18820 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\__pycache__
    - __init__.cpython-313.pyc [7102 bytes]     - _base_connection.cpython-313.pyc [6945 bytes]     - _collections.cpython-313.pyc [22639 bytes]     - _request_methods.cpython-313.pyc [9939 bytes]     - _version.cpython-313.pyc [632 bytes]     - connection.cpython-313.pyc [36528 bytes]     - connectionpool.cpython-313.pyc [39098 bytes]     - exceptions.cpython-313.pyc [17400 bytes]     - fields.cpython-313.pyc [11585 bytes]     - filepost.cpython-313.pyc [3474 bytes]     - poolmanager.cpython-313.pyc [23622 bytes]     - response.cpython-313.pyc [51857 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\contrib
    - __init__.py [0 bytes]     - pyopenssl.py [19720 bytes]     - socks.py [7549 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\http2
    - __init__.py [1741 bytes]     - connection.py [12694 bytes]     - probe.py [3014 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\util
    - __init__.py [1001 bytes]     - connection.py [4444 bytes]     - proxy.py [1148 bytes]     - request.py [8218 bytes]     - response.py [3374 bytes]     - retry.py [18459 bytes]     - ssl_.py [19786 bytes]     - ssl_match_hostname.py [5845 bytes]     - ssltransport.py [8847 bytes]     - timeout.py [10346 bytes]     - url.py [15205 bytes]     - util.py [1146 bytes]     - wait.py [4423 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\contrib\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - pyopenssl.cpython-313.pyc [28559 bytes]     - socks.cpython-313.pyc [8388 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\contrib\emscripten
    - __init__.py [733 bytes]     - connection.py [8771 bytes]     - emscripten_fetch_worker.js [3655 bytes]     - fetch.py [22867 bytes]     - request.py [566 bytes]     - response.py [9507 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\contrib\emscripten\__pycache__
    - __init__.cpython-313.pyc [879 bytes]     - connection.cpython-313.pyc [10489 bytes]     - fetch.cpython-313.pyc [28604 bytes]     - request.cpython-313.pyc [1445 bytes]     - response.cpython-313.pyc [12347 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\http2\__pycache__
    - __init__.cpython-313.pyc [1731 bytes]     - connection.cpython-313.pyc [17408 bytes]     - probe.cpython-313.pyc [3766 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3\util\__pycache__
    - __init__.cpython-313.pyc [982 bytes]     - connection.cpython-313.pyc [4712 bytes]     - proxy.cpython-313.pyc [1202 bytes]     - request.cpython-313.pyc [8215 bytes]     - response.cpython-313.pyc [2881 bytes]     - retry.cpython-313.pyc [20238 bytes]     - ssl_.cpython-313.pyc [17196 bytes]     - ssl_match_hostname.cpython-313.pyc [5708 bytes]     - ssltransport.cpython-313.pyc [13423 bytes]     - timeout.cpython-313.pyc [11275 bytes]     - url.cpython-313.pyc [16267 bytes]     - util.cpython-313.pyc [2105 bytes]     - wait.cpython-313.pyc [3529 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\urllib3-2.4.0.dist-info\licenses
    - LICENSE.txt [1093 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\__pycache__
    - __init__.cpython-313.pyc [359 bytes]     - __main__.cpython-313.pyc [279 bytes]     - _subprocess.cpython-313.pyc [2839 bytes]     - _types.cpython-313.pyc [11888 bytes]     - config.cpython-313.pyc [25590 bytes]     - importer.cpython-313.pyc [1824 bytes]     - logging.cpython-313.pyc [7654 bytes]     - main.cpython-313.pyc [20232 bytes]     - server.cpython-313.pyc [16884 bytes]     - workers.cpython-313.pyc [6807 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\lifespan
    - __init__.py [0 bytes]     - off.py [332 bytes]     - on.py [5184 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\loops
    - __init__.py [0 bytes]     - asyncio.py [301 bytes]     - auto.py [400 bytes]     - uvloop.py [148 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\middleware
    - __init__.py [0 bytes]     - asgi2.py [394 bytes]     - message_logger.py [2859 bytes]     - proxy_headers.py [5790 bytes]     - wsgi.py [7105 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\protocols
    - __init__.py [0 bytes]     - utils.py [1849 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\supervisors
    - __init__.py [507 bytes]     - basereload.py [4036 bytes]     - multiprocess.py [7507 bytes]     - statreload.py [1566 bytes]     - watchfilesreload.py [3010 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\lifespan\__pycache__
    - __init__.cpython-313.pyc [170 bytes]     - off.cpython-313.pyc [1022 bytes]     - on.cpython-313.pyc [8294 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\loops\__pycache__
    - __init__.cpython-313.pyc [167 bytes]     - asyncio.cpython-313.pyc [748 bytes]     - auto.cpython-313.pyc [628 bytes]     - uvloop.cpython-313.pyc [528 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\middleware\__pycache__
    - __init__.cpython-313.pyc [172 bytes]     - asgi2.cpython-313.pyc [1035 bytes]     - message_logger.cpython-313.pyc [4448 bytes]     - proxy_headers.cpython-313.pyc [5977 bytes]     - wsgi.cpython-313.pyc [10124 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\protocols\__pycache__
    - __init__.cpython-313.pyc [171 bytes]     - utils.cpython-313.pyc [3091 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\protocols\http
    - __init__.py [0 bytes]     - auto.py [403 bytes]     - flow_control.py [1701 bytes]     - h11_impl.py [20694 bytes]     - httptools_impl.py [21805 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\protocols\websockets
    - __init__.py [0 bytes]     - auto.py [574 bytes]     - websockets_impl.py [15504 bytes]     - wsproto_impl.py [15375 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\protocols\http\__pycache__
    - __init__.cpython-313.pyc [176 bytes]     - auto.cpython-313.pyc [589 bytes]     - flow_control.cpython-313.pyc [3141 bytes]     - h11_impl.cpython-313.pyc [27639 bytes]     - httptools_impl.cpython-313.pyc [29833 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\protocols\websockets\__pycache__
    - __init__.cpython-313.pyc [182 bytes]     - auto.cpython-313.pyc [772 bytes]     - websockets_impl.cpython-313.pyc [20841 bytes]     - wsproto_impl.cpython-313.pyc [21625 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn\supervisors\__pycache__
    - __init__.cpython-313.pyc [751 bytes]     - basereload.cpython-313.pyc [7145 bytes]     - multiprocess.cpython-313.pyc [13635 bytes]     - statreload.cpython-313.pyc [2932 bytes]     - watchfilesreload.cpython-313.pyc [4775 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\uvicorn-0.34.2.dist-info\licenses
    - LICENSE.md [1526 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\__pycache__
    - winxpgui.cpython-313.pyc [535 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos
    - BackupRead_BackupWrite.py [3829 bytes]     - BackupSeek_streamheaders.py [4001 bytes]     - CopyFileEx.py [1300 bytes]     - CreateFileTransacted_MiniVersion.py [3717 bytes]     - desktopmanager.py [8363 bytes]     - eventLogDemo.py [4522 bytes]     - EvtFormatMessage.py [3401 bytes]     - EvtSubscribe_pull.py [820 bytes]     - EvtSubscribe_push.py [835 bytes]     - FileSecurityTest.py [4523 bytes]     - getfilever.py [1153 bytes]     - GetSaveFileName.py [1211 bytes]     - mmapfile_demo.py [2870 bytes]     - NetValidatePasswordPolicy.py [3635 bytes]     - OpenEncryptedFileRaw.py [2024 bytes]     - print_desktop.py [2972 bytes]     - rastest.py [5237 bytes]     - RegCreateKeyTransacted.py [1972 bytes]     - RegRestoreKey.py [2134 bytes]     - SystemParametersInfo.py [8162 bytes]     - timer_demo.py [2257 bytes]     - win32clipboard_bitmapdemo.py [4022 bytes]     - win32clipboardDemo.py [4978 bytes]     - win32comport_demo.py [6202 bytes]     - win32console_demo.py [5205 bytes]     - win32cred_demo.py [2834 bytes]     - win32fileDemo.py [1407 bytes]     - win32gui_demo.py [5205 bytes]     - win32gui_devicenotify.py [3919 bytes]     - win32gui_dialog.py [15763 bytes]     - win32gui_menu.py [17210 bytes]     - win32gui_taskbar.py [5088 bytes]     - win32netdemo.py [9192 bytes]     - win32rcparser_demo.py [2889 bytes]     - win32servicedemo.py [602 bytes]     - win32ts_logoff_disconnected.py [1007 bytes]     - winprocess.py [7588 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\include
    - PyWinTypes.h [31269 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\lib
    - _win32verstamp_pywin32ctypes.py [4668 bytes]     - afxres.py [206 bytes]     - commctrl.py [47709 bytes]     - mmsystem.py [31230 bytes]     - netbios.py [6895 bytes]     - ntsecuritycon.py [23075 bytes]     - pywin32_bootstrap.py [772 bytes]     - pywin32_testutil.py [10876 bytes]     - pywintypes.py [6034 bytes]     - rasutil.py [1764 bytes]     - regcheck.py [4602 bytes]     - regutil.py [12695 bytes]     - sspi.py [16036 bytes]     - sspicon.py [16174 bytes]     - win2kras.py [446 bytes]     - win32con.py [121632 bytes]     - win32cryptcon.py [74342 bytes]     - win32evtlogutil.py [7827 bytes]     - win32gui_struct.py [29901 bytes]     - win32inetcon.py [44303 bytes]     - win32netcon.py [19159 bytes]     - win32pdhquery.py [24089 bytes]     - win32pdhutil.py [7828 bytes]     - win32rcparser.py [22333 bytes]     - win32serviceutil.py [39076 bytes]     - win32timezone.py [42439 bytes]     - win32traceutil.py [1661 bytes]     - win32verstamp.py [7210 bytes]     - winerror.py [290166 bytes]     - winioctlcon.py [36454 bytes]     - winnt.py [38459 bytes]     - winperf.py [6147 bytes]     - winxptheme.py [282 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\libs
    - pywintypes.lib [108206 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\scripts
    - backupEventLog.py [1288 bytes]     - ControlService.py [18634 bytes]     - h2py.py [6620 bytes]     - killProcName.py [2050 bytes]     - pywin32_postinstall.py [25736 bytes]     - pywin32_testall.py [3847 bytes]     - rasutil.py [2821 bytes]     - regsetup.py [21043 bytes]     - setup_d.py [3639 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\test
    - handles.py [5630 bytes]     - test_clipboard.py [4409 bytes]     - test_exceptions.py [8227 bytes]     - test_odbc.py [8613 bytes]     - test_pywintypes.py [4139 bytes]     - test_security.py [6222 bytes]     - test_sspi.py [8379 bytes]     - test_win32api.py [10013 bytes]     - test_win32clipboard.py [2013 bytes]     - test_win32cred.py [3239 bytes]     - test_win32crypt.py [4907 bytes]     - test_win32event.py [4487 bytes]     - test_win32file.py [42783 bytes]     - test_win32gui.py [7943 bytes]     - test_win32guistruct.py [9610 bytes]     - test_win32inet.py [3517 bytes]     - test_win32net.py [658 bytes]     - test_win32pipe.py [4945 bytes]     - test_win32print.py [705 bytes]     - test_win32profile.py [433 bytes]     - test_win32rcparser.py [2533 bytes]     - test_win32timezone.py [327 bytes]     - test_win32trace.py [11665 bytes]     - test_win32ts.py [439 bytes]     - test_win32wnet.py [5827 bytes]     - testall.py [7257 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\__pycache__
    - BackupRead_BackupWrite.cpython-313.pyc [5584 bytes]     - BackupSeek_streamheaders.cpython-313.pyc [4885 bytes]     - CopyFileEx.cpython-313.pyc [1681 bytes]     - CreateFileTransacted_MiniVersion.cpython-313.pyc [4646 bytes]     - desktopmanager.cpython-313.pyc [11138 bytes]     - eventLogDemo.cpython-313.pyc [5217 bytes]     - EvtFormatMessage.cpython-313.pyc [3501 bytes]     - EvtSubscribe_pull.cpython-313.pyc [1018 bytes]     - EvtSubscribe_push.cpython-313.pyc [1327 bytes]     - FileSecurityTest.cpython-313.pyc [4201 bytes]     - getfilever.cpython-313.pyc [1101 bytes]     - GetSaveFileName.cpython-313.pyc [1765 bytes]     - mmapfile_demo.cpython-313.pyc [3906 bytes]     - NetValidatePasswordPolicy.cpython-313.pyc [4112 bytes]     - OpenEncryptedFileRaw.cpython-313.pyc [3114 bytes]     - print_desktop.cpython-313.pyc [4518 bytes]     - rastest.cpython-313.pyc [6488 bytes]     - RegCreateKeyTransacted.cpython-313.pyc [2435 bytes]     - RegRestoreKey.cpython-313.pyc [2827 bytes]     - SystemParametersInfo.cpython-313.pyc [9281 bytes]     - timer_demo.cpython-313.pyc [2453 bytes]     - win32clipboard_bitmapdemo.cpython-313.pyc [6031 bytes]     - win32clipboardDemo.cpython-313.pyc [5936 bytes]     - win32comport_demo.cpython-313.pyc [6191 bytes]     - win32console_demo.cpython-313.pyc [5936 bytes]     - win32cred_demo.cpython-313.pyc [2956 bytes]     - win32fileDemo.cpython-313.pyc [1710 bytes]     - win32gui_demo.cpython-313.pyc [8589 bytes]     - win32gui_devicenotify.cpython-313.pyc [4159 bytes]     - win32gui_dialog.cpython-313.pyc [22306 bytes]     - win32gui_menu.cpython-313.pyc [16069 bytes]     - win32gui_taskbar.cpython-313.pyc [7943 bytes]     - win32netdemo.cpython-313.pyc [12412 bytes]     - win32rcparser_demo.cpython-313.pyc [5075 bytes]     - win32servicedemo.cpython-313.pyc [965 bytes]     - win32ts_logoff_disconnected.cpython-313.pyc [1303 bytes]     - winprocess.cpython-313.pyc [9393 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\c_extension
    - setup.py [783 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\dde
    - ddeclient.py [493 bytes]     - ddeserver.py [1182 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\images
    - frowny.bmp [3126 bytes]     - smiley.bmp [3126 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\pipes
    - cat.py [352 bytes]     - runproc.py [4137 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\security
    - account_rights.py [1555 bytes]     - explicit_entries.py [5135 bytes]     - get_policy_info.py [1227 bytes]     - GetTokenInformation.py [3823 bytes]     - list_rights.py [1082 bytes]     - localized_names.py [2215 bytes]     - lsaregevent.py [546 bytes]     - lsastore.py [484 bytes]     - query_information.py [837 bytes]     - regsave_sa.py [1751 bytes]     - regsecurity.py [1157 bytes]     - sa_inherit.py [289 bytes]     - security_enums.py [9776 bytes]     - set_file_audit.py [3460 bytes]     - set_file_owner.py [2314 bytes]     - set_policy_info.py [936 bytes]     - setkernelobjectsecurity.py [5027 bytes]     - setnamedsecurityinfo.py [4519 bytes]     - setsecurityinfo.py [4648 bytes]     - setuserobjectsecurity.py [3468 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\service
    - nativePipeTestService.py [2198 bytes]     - pipeTestService.py [7136 bytes]     - pipeTestServiceClient.py [4785 bytes]     - serviceEvents.py [4157 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\win32wnet
    - testwnet.py [4405 bytes]     - winnetwk.py [3293 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\c_extension\__pycache__
    - setup.cpython-313.pyc [770 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\dde\__pycache__
    - ddeclient.cpython-313.pyc [887 bytes]     - ddeserver.cpython-313.pyc [3008 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\pipes\__pycache__
    - cat.cpython-313.pyc [870 bytes]     - runproc.cpython-313.pyc [3816 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\security\__pycache__
    - account_rights.cpython-313.pyc [2039 bytes]     - explicit_entries.cpython-313.pyc [5521 bytes]     - get_policy_info.cpython-313.pyc [1360 bytes]     - GetTokenInformation.cpython-313.pyc [4859 bytes]     - list_rights.cpython-313.pyc [1496 bytes]     - localized_names.cpython-313.pyc [2465 bytes]     - lsaregevent.cpython-313.pyc [800 bytes]     - lsastore.cpython-313.pyc [727 bytes]     - query_information.cpython-313.pyc [1420 bytes]     - regsave_sa.cpython-313.pyc [2739 bytes]     - regsecurity.cpython-313.pyc [1965 bytes]     - sa_inherit.cpython-313.pyc [704 bytes]     - security_enums.cpython-313.pyc [9661 bytes]     - set_file_audit.cpython-313.pyc [3737 bytes]     - set_file_owner.cpython-313.pyc [3221 bytes]     - set_policy_info.cpython-313.pyc [1265 bytes]     - setkernelobjectsecurity.cpython-313.pyc [6150 bytes]     - setnamedsecurityinfo.cpython-313.pyc [5365 bytes]     - setsecurityinfo.cpython-313.pyc [5414 bytes]     - setuserobjectsecurity.cpython-313.pyc [4523 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\security\sspi
    - fetch_url.py [5555 bytes]     - simple_auth.py [2917 bytes]     - socket_server.py [6535 bytes]     - validate_password.py [1164 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\security\sspi\__pycache__
    - fetch_url.cpython-313.pyc [7699 bytes]     - simple_auth.cpython-313.pyc [3070 bytes]     - socket_server.cpython-313.pyc [8756 bytes]     - validate_password.cpython-313.pyc [1648 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\service\__pycache__
    - nativePipeTestService.cpython-313.pyc [2112 bytes]     - pipeTestService.cpython-313.pyc [7777 bytes]     - pipeTestServiceClient.cpython-313.pyc [5366 bytes]     - serviceEvents.cpython-313.pyc [4423 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\Demos\win32wnet\__pycache__
    - testwnet.cpython-313.pyc [6401 bytes]     - winnetwk.cpython-313.pyc [3378 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\lib\__pycache__
    - _win32verstamp_pywin32ctypes.cpython-313.pyc [4886 bytes]     - afxres.cpython-313.pyc [403 bytes]     - commctrl.cpython-313.pyc [49869 bytes]     - mmsystem.cpython-313.pyc [32176 bytes]     - netbios.cpython-313.pyc [9681 bytes]     - ntsecuritycon.cpython-313.pyc [23249 bytes]     - pywin32_bootstrap.cpython-313.pyc [539 bytes]     - pywin32_testutil.cpython-313.pyc [13282 bytes]     - pywintypes.cpython-313.pyc [3627 bytes]     - rasutil.cpython-313.pyc [2846 bytes]     - regcheck.cpython-313.pyc [6369 bytes]     - regutil.cpython-313.pyc [15751 bytes]     - sspi.cpython-313.pyc [17953 bytes]     - sspicon.cpython-313.pyc [18769 bytes]     - win2kras.cpython-313.pyc [646 bytes]     - win32con.cpython-313.pyc [147129 bytes]     - win32cryptcon.cpython-313.pyc [81041 bytes]     - win32evtlogutil.cpython-313.pyc [7382 bytes]     - win32gui_struct.cpython-313.pyc [29150 bytes]     - win32inetcon.cpython-313.pyc [45361 bytes]     - win32netcon.cpython-313.pyc [21322 bytes]     - win32pdhquery.cpython-313.pyc [24413 bytes]     - win32pdhutil.cpython-313.pyc [7710 bytes]     - win32rcparser.cpython-313.pyc [29586 bytes]     - win32serviceutil.cpython-313.pyc [41292 bytes]     - win32timezone.cpython-313.pyc [49271 bytes]     - win32traceutil.cpython-313.pyc [1370 bytes]     - win32verstamp.cpython-313.pyc [9628 bytes]     - winerror.cpython-313.pyc [348623 bytes]     - winioctlcon.cpython-313.pyc [31624 bytes]     - winnt.cpython-313.pyc [44398 bytes]     - winperf.cpython-313.pyc [4570 bytes]     - winxptheme.cpython-313.pyc [441 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\scripts\__pycache__
    - backupEventLog.cpython-313.pyc [2155 bytes]     - ControlService.cpython-313.pyc [22641 bytes]     - h2py.cpython-313.pyc [8318 bytes]     - killProcName.cpython-313.pyc [1913 bytes]     - pywin32_postinstall.cpython-313.pyc [30282 bytes]     - pywin32_testall.cpython-313.pyc [4782 bytes]     - rasutil.cpython-313.pyc [4083 bytes]     - regsetup.cpython-313.pyc [23755 bytes]     - setup_d.cpython-313.pyc [5866 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\scripts\VersionStamp
    - BrandProject.py [2873 bytes]     - bulkstamp.py [4823 bytes]     - vssutil.py [5818 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\scripts\VersionStamp\__pycache__
    - BrandProject.cpython-313.pyc [3572 bytes]     - bulkstamp.cpython-313.pyc [5060 bytes]     - vssutil.cpython-313.pyc [8077 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\test\__pycache__
    - handles.cpython-313.pyc [7999 bytes]     - test_clipboard.cpython-313.pyc [7889 bytes]     - test_exceptions.cpython-313.pyc [15096 bytes]     - test_odbc.cpython-313.pyc [11980 bytes]     - test_pywintypes.cpython-313.pyc [6834 bytes]     - test_security.cpython-313.pyc [10340 bytes]     - test_sspi.cpython-313.pyc [12171 bytes]     - test_win32api.cpython-313.pyc [15214 bytes]     - test_win32clipboard.cpython-313.pyc [3362 bytes]     - test_win32cred.cpython-313.pyc [5727 bytes]     - test_win32crypt.cpython-313.pyc [6107 bytes]     - test_win32event.cpython-313.pyc [8237 bytes]     - test_win32file.cpython-313.pyc [58771 bytes]     - test_win32gui.cpython-313.pyc [11160 bytes]     - test_win32guistruct.cpython-313.pyc [13558 bytes]     - test_win32inet.cpython-313.pyc [7357 bytes]     - test_win32net.cpython-313.pyc [1274 bytes]     - test_win32pipe.cpython-313.pyc [7090 bytes]     - test_win32print.cpython-313.pyc [1651 bytes]     - test_win32profile.cpython-313.pyc [1159 bytes]     - test_win32rcparser.cpython-313.pyc [4665 bytes]     - test_win32timezone.cpython-313.pyc [926 bytes]     - test_win32trace.cpython-313.pyc [21170 bytes]     - test_win32ts.cpython-313.pyc [1099 bytes]     - test_win32wnet.cpython-313.pyc [8456 bytes]     - testall.cpython-313.pyc [9294 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32\test\win32rcparser
    - python.bmp [778 bytes]     - python.ico [766 bytes]     - test.h [1215 bytes]     - test.rc [6474 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\__pycache__
    - __init__.cpython-313.pyc [3873 bytes]     - olectl.cpython-313.pyc [3739 bytes]     - storagecon.cpython-313.pyc [3630 bytes]     - universal.cpython-313.pyc [10689 bytes]     - util.cpython-313.pyc [1487 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\client
    - __init__.py [27152 bytes]     - build.py [29158 bytes]     - CLSIDToClass.py [1871 bytes]     - combrowse.py [20520 bytes]     - connect.py [1585 bytes]     - dynamic.py [28284 bytes]     - gencache.py [29645 bytes]     - genpy.py [54950 bytes]     - makepy.py [14962 bytes]     - selecttlb.py [6451 bytes]     - tlbrowse.py [9787 bytes]     - util.py [3445 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\demos
    - __init__.py [0 bytes]     - connect.py [3854 bytes]     - dump_clipboard.py [3015 bytes]     - eventsApartmentThreaded.py [3764 bytes]     - eventsFreeThreaded.py [3556 bytes]     - excelAddin.py [6216 bytes]     - excelRTDServer.py [16612 bytes]     - iebutton.py [7157 bytes]     - ietoolbar.py [11148 bytes]     - outlookAddin.py [4746 bytes]     - trybag.py [2185 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\HTML
    - docindex.html [1317 bytes]     - GeneratedSupport.html [6128 bytes]     - index.html [1660 bytes]     - misc.html [1182 bytes]     - package.html [3290 bytes]     - PythonCOM.html [9033 bytes]     - QuickStartClientCom.html [7360 bytes]     - QuickStartServerCom.html [13249 bytes]     - variant.html [6016 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\include
    - PythonCOM.h [30321 bytes]     - PythonCOMRegister.h [3092 bytes]     - PythonCOMServer.h [8936 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\libs
    - axscript.lib [72478 bytes]     - pythoncom.lib [159886 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\makegw
    - __init__.py [31 bytes]     - makegw.py [22299 bytes]     - makegwenum.py [10803 bytes]     - makegwparse.py [36307 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\server
    - __init__.py [51 bytes]     - connect.py [2859 bytes]     - dispatcher.py [8278 bytes]     - exception.py [3500 bytes]     - factory.py [876 bytes]     - localserver.py [1247 bytes]     - policy.py [33223 bytes]     - register.py [25863 bytes]     - util.py [6930 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\servers
    - __init__.py [0 bytes]     - dictionary.py [4500 bytes]     - interp.py [1794 bytes]     - perfmon.py [1233 bytes]     - PythonTools.py [1224 bytes]     - test_pycomtest.py [5255 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\test
    - __init__.py [44 bytes]     - daodump.py [2333 bytes]     - errorSemantics.py [8767 bytes]     - GenTestScripts.py [2715 bytes]     - pippo_server.py [2726 bytes]     - pippo.idl [1916 bytes]     - policySemantics.py [3226 bytes]     - readme.txt [735 bytes]     - testAccess.py [5874 bytes]     - testADOEvents.py [2878 bytes]     - testall.py [10229 bytes]     - testArrays.py [2165 bytes]     - testAXScript.py [1388 bytes]     - testClipboard.py [5952 bytes]     - testCollections.py [4400 bytes]     - testConversionErrors.py [713 bytes]     - testDates.py [1908 bytes]     - testDCOM.py [1766 bytes]     - testDictionary.py [2862 bytes]     - testDictionary.vbs [584 bytes]     - testDynamic.py [2756 bytes]     - testExchange.py [3361 bytes]     - testExplorer.py [4599 bytes]     - testGatewayAddresses.py [5342 bytes]     - testGIT.py [4766 bytes]     - testInterp.vbs [268 bytes]     - testIterators.py [4769 bytes]     - testmakepy.py [1929 bytes]     - testMarshal.py [6213 bytes]     - testMSOffice.py [5661 bytes]     - testMSOfficeEvents.py [4160 bytes]     - testPersist.py [6465 bytes]     - testPippo.py [2652 bytes]     - testPyComTest.py [32897 bytes]     - Testpys.sct [1098 bytes]     - testPyScriptlet.js [1120 bytes]     - testROT.py [793 bytes]     - testServers.py [1401 bytes]     - testShell.py [9694 bytes]     - testStorage.py [3707 bytes]     - testStreams.py [4336 bytes]     - testvb.py [20762 bytes]     - testvbscript_regexp.py [1138 bytes]     - testWMI.py [486 bytes]     - testxslt.js [581 bytes]     - testxslt.py [948 bytes]     - testxslt.xsl [2111 bytes]     - util.py [8340 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\client\__pycache__
    - __init__.cpython-313.pyc [29477 bytes]     - build.cpython-313.pyc [27280 bytes]     - CLSIDToClass.cpython-313.pyc [2332 bytes]     - combrowse.cpython-313.pyc [33322 bytes]     - connect.cpython-313.pyc [2815 bytes]     - dynamic.cpython-313.pyc [30889 bytes]     - gencache.cpython-313.pyc [29199 bytes]     - genpy.cpython-313.pyc [56570 bytes]     - makepy.cpython-313.pyc [18564 bytes]     - selecttlb.cpython-313.pyc [7917 bytes]     - tlbrowse.cpython-313.pyc [16168 bytes]     - util.cpython-313.pyc [5587 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\demos\__pycache__
    - __init__.cpython-313.pyc [168 bytes]     - connect.cpython-313.pyc [3921 bytes]     - dump_clipboard.cpython-313.pyc [4111 bytes]     - eventsApartmentThreaded.cpython-313.pyc [3559 bytes]     - eventsFreeThreaded.cpython-313.pyc [3198 bytes]     - excelAddin.cpython-313.pyc [6275 bytes]     - excelRTDServer.cpython-313.pyc [16394 bytes]     - iebutton.cpython-313.pyc [8516 bytes]     - ietoolbar.cpython-313.pyc [14611 bytes]     - outlookAddin.cpython-313.pyc [5818 bytes]     - trybag.cpython-313.pyc [4301 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\HTML\image
    - blank.gif [864 bytes]     - BTN_HomePage.gif [211 bytes]     - BTN_ManualTop.gif [215 bytes]     - BTN_NextPage.gif [218 bytes]     - BTN_PrevPage.gif [216 bytes]     - pycom_blowing.gif [20926 bytes]     - pythoncom.gif [5767 bytes]     - www_icon.gif [275 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\makegw\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - makegw.cpython-313.pyc [25006 bytes]     - makegwenum.cpython-313.pyc [11345 bytes]     - makegwparse.cpython-313.pyc [52549 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\server\__pycache__
    - __init__.cpython-313.pyc [169 bytes]     - connect.cpython-313.pyc [4270 bytes]     - dispatcher.cpython-313.pyc [12833 bytes]     - exception.cpython-313.pyc [3812 bytes]     - factory.cpython-313.pyc [1274 bytes]     - localserver.cpython-313.pyc [1729 bytes]     - policy.cpython-313.pyc [36293 bytes]     - register.cpython-313.pyc [27074 bytes]     - util.cpython-313.pyc [10636 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\servers\__pycache__
    - __init__.cpython-313.pyc [170 bytes]     - dictionary.cpython-313.pyc [5303 bytes]     - interp.cpython-313.pyc [2684 bytes]     - perfmon.cpython-313.pyc [1928 bytes]     - PythonTools.cpython-313.pyc [2145 bytes]     - test_pycomtest.cpython-313.pyc [9241 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32com\test\__pycache__
    - __init__.cpython-313.pyc [167 bytes]     - daodump.cpython-313.pyc [3843 bytes]     - errorSemantics.cpython-313.pyc [10154 bytes]     - GenTestScripts.cpython-313.pyc [4677 bytes]     - pippo_server.cpython-313.pyc [4081 bytes]     - policySemantics.cpython-313.pyc [5918 bytes]     - testAccess.cpython-313.pyc [7783 bytes]     - testADOEvents.cpython-313.pyc [3852 bytes]     - testall.cpython-313.pyc [11915 bytes]     - testArrays.cpython-313.pyc [4292 bytes]     - testAXScript.cpython-313.pyc [3099 bytes]     - testClipboard.cpython-313.pyc [9435 bytes]     - testCollections.cpython-313.pyc [6514 bytes]     - testConversionErrors.cpython-313.pyc [2208 bytes]     - testDates.cpython-313.pyc [3095 bytes]     - testDCOM.cpython-313.pyc [2148 bytes]     - testDictionary.cpython-313.pyc [4402 bytes]     - testDynamic.cpython-313.pyc [4196 bytes]     - testExchange.cpython-313.pyc [4915 bytes]     - testExplorer.cpython-313.pyc [5591 bytes]     - testGatewayAddresses.cpython-313.pyc [5334 bytes]     - testGIT.cpython-313.pyc [6589 bytes]     - testIterators.cpython-313.pyc [7481 bytes]     - testmakepy.cpython-313.pyc [2669 bytes]     - testMarshal.cpython-313.pyc [7223 bytes]     - testMSOffice.cpython-313.pyc [8798 bytes]     - testMSOfficeEvents.cpython-313.pyc [7003 bytes]     - testPersist.cpython-313.pyc [9346 bytes]     - testPippo.cpython-313.pyc [4683 bytes]     - testPyComTest.cpython-313.pyc [40461 bytes]     - testROT.cpython-313.pyc [1432 bytes]     - testServers.cpython-313.pyc [3388 bytes]     - testShell.cpython-313.pyc [17426 bytes]     - testStorage.cpython-313.pyc [4179 bytes]     - testStreams.cpython-313.pyc [8067 bytes]     - testvb.cpython-313.pyc [23275 bytes]     - testvbscript_regexp.cpython-313.pyc [2237 bytes]     - testWMI.cpython-313.pyc [1321 bytes]     - testxslt.cpython-313.pyc [1951 bytes]     - util.cpython-313.pyc [12628 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\adsi
    - __init__.py [3709 bytes]     - adsi.pyd [98816 bytes]     - adsicon.py [12607 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\authorization
    - __init__.py [198 bytes]     - authorization.pyd [29696 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axcontrol
    - __init__.py [139 bytes]     - axcontrol.pyd [144896 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axdebug
    - __init__.py [139 bytes]     - adb.py [18086 bytes]     - codecontainer.py [9314 bytes]     - contexts.py [2125 bytes]     - debugger.py [7244 bytes]     - documents.py [4297 bytes]     - dump.py [1807 bytes]     - expressions.py [6744 bytes]     - gateways.py [17951 bytes]     - stackframe.py [6032 bytes]     - util.py [3218 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript
    - __init__.py [139 bytes]     - asputil.py [262 bytes]     - axscript.pyd [94208 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\bits
    - __init__.py [198 bytes]     - bits.pyd [63488 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\directsound
    - __init__.py [139 bytes]     - directsound.pyd [77312 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\ifilter
    - __init__.py [41 bytes]     - ifilter.pyd [30720 bytes]     - ifiltercon.py [3269 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\internet
    - __init__.py [139 bytes]     - inetcon.py [11879 bytes]     - internet.pyd [94208 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\mapi
    - __init__.py [490 bytes]     - emsabtags.py [50241 bytes]     - exchange.pyd [141824 bytes]     - mapi.pyd [249856 bytes]     - mapitags.py [52360 bytes]     - mapiutil.py [7180 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\propsys
    - __init__.py [28 bytes]     - propsys.pyd [139776 bytes]     - pscon.py [49460 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell
    - __init__.py [139 bytes]     - shell.pyd [538112 bytes]     - shellcon.py [51330 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\taskscheduler
    - __init__.py [198 bytes]     - taskscheduler.pyd [53760 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\adsi\__pycache__
    - __init__.cpython-313.pyc [5585 bytes]     - adsicon.cpython-313.pyc [13373 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\adsi\demos
    - objectPicker.py [2009 bytes]     - scp.py [19693 bytes]     - search.py [4290 bytes]     - test.py [8649 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\adsi\demos\__pycache__
    - objectPicker.cpython-313.pyc [2340 bytes]     - scp.cpython-313.pyc [19221 bytes]     - search.cpython-313.pyc [6283 bytes]     - test.cpython-313.pyc [11443 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\authorization\__pycache__
    - __init__.cpython-313.pyc [293 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\authorization\demos
    - EditSecurity.py [8599 bytes]     - EditServiceSecurity.py [8241 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\authorization\demos\__pycache__
    - EditSecurity.cpython-313.pyc [8336 bytes]     - EditServiceSecurity.cpython-313.pyc [8326 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axcontrol\__pycache__
    - __init__.cpython-313.pyc [287 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axdebug\__pycache__
    - __init__.cpython-313.pyc [285 bytes]     - adb.cpython-313.pyc [22327 bytes]     - codecontainer.cpython-313.pyc [13242 bytes]     - contexts.cpython-313.pyc [3341 bytes]     - debugger.cpython-313.pyc [11534 bytes]     - documents.cpython-313.pyc [7061 bytes]     - dump.cpython-313.pyc [2899 bytes]     - expressions.cpython-313.pyc [10544 bytes]     - gateways.cpython-313.pyc [26593 bytes]     - stackframe.cpython-313.pyc [7910 bytes]     - util.cpython-313.pyc [4274 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\__pycache__
    - __init__.cpython-313.pyc [286 bytes]     - asputil.cpython-313.pyc [492 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\client
    - __init__.py [28 bytes]     - debug.py [8317 bytes]     - error.py [9264 bytes]     - framework.py [47513 bytes]     - pydumper.py [2231 bytes]     - pyscript_rexec.py [2203 bytes]     - pyscript.py [15656 bytes]     - scriptdispatch.py [3856 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\Demos


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\server
    - __init__.py [0 bytes]     - axsite.py [4275 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\test
    - debugTest.pys [217 bytes]     - debugTest.vbs [89 bytes]     - leakTest.py [4835 bytes]     - testHost.py [7923 bytes]     - testHost4Dbg.py [2735 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\client\__pycache__
    - __init__.cpython-313.pyc [181 bytes]     - debug.cpython-313.pyc [11127 bytes]     - error.cpython-313.pyc [11305 bytes]     - framework.cpython-313.pyc [53901 bytes]     - pydumper.cpython-313.pyc [2446 bytes]     - pyscript_rexec.cpython-313.pyc [2063 bytes]     - pyscript.cpython-313.pyc [19864 bytes]     - scriptdispatch.cpython-313.pyc [5921 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\Demos\client


📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\Demos\client\asp
    - caps.asp [1356 bytes]     - CreateObject.asp [511 bytes]     - tut1.asp [156 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\Demos\client\ie
    - calc.htm [4145 bytes]     - CHARTPY.HTM [6687 bytes]     - dbgtest.htm [207 bytes]     - demo_check.htm [1631 bytes]     - demo_intro.htm [1654 bytes]     - demo_menu.htm [507 bytes]     - demo.htm [472 bytes]     - docwrite.htm [492 bytes]     - FOO.HTM [990 bytes]     - foo2.htm [3610 bytes]     - form.htm [506 bytes]     - marqueeDemo.htm [1221 bytes]     - MarqueeText1.htm [728 bytes]     - mousetrack.htm [2311 bytes]     - pycom_blowing.gif [20926 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\Demos\client\wsh
    - blank.pys [0 bytes]     - excel.pys [1029 bytes]     - registry.pys [1667 bytes]     - test.pys [384 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\Demos\client\asp\interrupt
    - test.asp [77 bytes]     - test.html [166 bytes]     - test1.asp [94 bytes]     - test1.html [166 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\server\__pycache__
    - __init__.cpython-313.pyc [181 bytes]     - axsite.cpython-313.pyc [7204 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\axscript\test\__pycache__
    - leakTest.cpython-313.pyc [7935 bytes]     - testHost.cpython-313.pyc [11548 bytes]     - testHost4Dbg.cpython-313.pyc [3866 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\bits\__pycache__
    - __init__.cpython-313.pyc [284 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\bits\test
    - show_all_jobs.py [1567 bytes]     - test_bits.py [3999 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\bits\test\__pycache__
    - show_all_jobs.cpython-313.pyc [3060 bytes]     - test_bits.cpython-313.pyc [5861 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\directsound\__pycache__
    - __init__.cpython-313.pyc [289 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\directsound\test
    - __init__.py [66 bytes]     - ds_record.py [1463 bytes]     - ds_test.py [13357 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\directsound\test\__pycache__
    - __init__.cpython-313.pyc [182 bytes]     - ds_record.cpython-313.pyc [2666 bytes]     - ds_test.cpython-313.pyc [22179 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\ifilter\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - ifiltercon.cpython-313.pyc [3624 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\ifilter\demo
    - filterDemo.py [11775 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\ifilter\demo\__pycache__
    - filterDemo.cpython-313.pyc [10580 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\internet\__pycache__
    - __init__.cpython-313.pyc [286 bytes]     - inetcon.cpython-313.pyc [10911 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\mapi\__pycache__
    - __init__.cpython-313.pyc [691 bytes]     - emsabtags.cpython-313.pyc [54454 bytes]     - mapitags.cpython-313.pyc [57979 bytes]     - mapiutil.cpython-313.pyc [8250 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\mapi\demos
    - mapisend.py [3668 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\mapi\demos\__pycache__
    - mapisend.cpython-313.pyc [4556 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\propsys\__pycache__
    - __init__.cpython-313.pyc [173 bytes]     - pscon.cpython-313.pyc [54238 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\propsys\test
    - testpropsys.py [230 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\propsys\test\__pycache__
    - testpropsys.cpython-313.pyc [430 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\__pycache__
    - __init__.cpython-313.pyc [283 bytes]     - shellcon.cpython-313.pyc [55164 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\demos
    - browse_for_folder.py [1547 bytes]     - create_link.py [2409 bytes]     - dump_link.py [1746 bytes]     - explorer_browser.py [5099 bytes]     - IActiveDesktop.py [2205 bytes]     - IFileOperationProgressSink.py [5309 bytes]     - IShellLinkDataList.py [1984 bytes]     - ITransferAdviseSink.py [2629 bytes]     - IUniformResourceLocator.py [1708 bytes]     - shellexecuteex.py [489 bytes]     - viewstate.py [2386 bytes]     - walk_shell_folders.py [693 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\test
    - testShellFolder.py [603 bytes]     - testShellItem.py [2960 bytes]     - testSHFileOperation.py [2167 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\demos\__pycache__
    - browse_for_folder.cpython-313.pyc [1798 bytes]     - create_link.cpython-313.pyc [3394 bytes]     - dump_link.cpython-313.pyc [3036 bytes]     - explorer_browser.cpython-313.pyc [6941 bytes]     - IActiveDesktop.cpython-313.pyc [2548 bytes]     - IFileOperationProgressSink.cpython-313.pyc [7655 bytes]     - IShellLinkDataList.cpython-313.pyc [2512 bytes]     - ITransferAdviseSink.cpython-313.pyc [4237 bytes]     - IUniformResourceLocator.cpython-313.pyc [3566 bytes]     - shellexecuteex.cpython-313.pyc [1031 bytes]     - viewstate.cpython-313.pyc [2820 bytes]     - walk_shell_folders.cpython-313.pyc [1242 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\demos\servers
    - column_provider.py [3825 bytes]     - context_menu.py [4539 bytes]     - copy_hook.py [2755 bytes]     - empty_volume_cache.py [7830 bytes]     - folder_view.py [30173 bytes]     - icon_handler.py [2600 bytes]     - shell_view.py [38140 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\demos\servers\__pycache__
    - column_provider.cpython-313.pyc [4455 bytes]     - context_menu.cpython-313.pyc [5749 bytes]     - copy_hook.cpython-313.pyc [3279 bytes]     - empty_volume_cache.cpython-313.pyc [8283 bytes]     - folder_view.cpython-313.pyc [36509 bytes]     - icon_handler.cpython-313.pyc [3636 bytes]     - shell_view.cpython-313.pyc [43400 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\shell\test\__pycache__
    - testShellFolder.cpython-313.pyc [1053 bytes]     - testShellItem.cpython-313.pyc [5033 bytes]     - testSHFileOperation.cpython-313.pyc [3332 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\taskscheduler\__pycache__
    - __init__.cpython-313.pyc [293 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\taskscheduler\test
    - test_addtask_1.py [2160 bytes]     - test_addtask_2.py [1659 bytes]     - test_addtask.py [2278 bytes]     - test_localsystem.py [75 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\win32comext\taskscheduler\test\__pycache__
    - test_addtask_1.cpython-313.pyc [3631 bytes]     - test_addtask_2.cpython-313.pyc [2791 bytes]     - test_addtask.cpython-313.pyc [3677 bytes]     - test_localsystem.cpython-313.pyc [368 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\__pycache__
    - __init__.cpython-313.pyc [462 bytes]     - _argcomplete.cpython-313.pyc [4915 bytes]     - _version.cpython-313.pyc [632 bytes]     - cacheprovider.cpython-313.pyc [31244 bytes]     - capture.cpython-313.pyc [54806 bytes]     - compat.cpython-313.pyc [12764 bytes]     - debugging.cpython-313.pyc [18171 bytes]     - deprecated.cpython-313.pyc [2548 bytes]     - doctest.cpython-313.pyc [33662 bytes]     - faulthandler.cpython-313.pyc [4409 bytes]     - fixtures.cpython-313.pyc [78897 bytes]     - freeze_support.cpython-313.pyc [1783 bytes]     - helpconfig.cpython-313.pyc [12340 bytes]     - hookspec.cpython-313.pyc [42930 bytes]     - junitxml.cpython-313.pyc [33438 bytes]     - legacypath.cpython-313.pyc [24117 bytes]     - logging.cpython-313.pyc [45665 bytes]     - main.cpython-313.pyc [44349 bytes]     - monkeypatch.cpython-313.pyc [16103 bytes]     - nodes.cpython-313.pyc [30610 bytes]     - outcomes.cpython-313.pyc [11729 bytes]     - pastebin.cpython-313.pyc [5665 bytes]     - pathlib.cpython-313.pyc [41482 bytes]     - pytester_assertions.cpython-313.pyc [2448 bytes]     - pytester.cpython-313.pyc [83032 bytes]     - python_api.cpython-313.pyc [44231 bytes]     - python_path.cpython-313.pyc [1572 bytes]     - python.cpython-313.pyc [70575 bytes]     - recwarn.cpython-313.pyc [16259 bytes]     - reports.cpython-313.pyc [24773 bytes]     - runner.cpython-313.pyc [23924 bytes]     - scope.cpython-313.pyc [3702 bytes]     - setuponly.cpython-313.pyc [5561 bytes]     - setupplan.cpython-313.pyc [1889 bytes]     - skipping.cpython-313.pyc [13894 bytes]     - stash.cpython-313.pyc [4262 bytes]     - stepwise.cpython-313.pyc [5667 bytes]     - terminal.cpython-313.pyc [77043 bytes]     - threadexception.cpython-313.pyc [5206 bytes]     - timing.cpython-313.pyc [612 bytes]     - tmpdir.cpython-313.pyc [12737 bytes]     - unittest.cpython-313.pyc [19552 bytes]     - unraisableexception.cpython-313.pyc [5273 bytes]     - warning_types.cpython-313.pyc [7003 bytes]     - warnings.cpython-313.pyc [6670 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\_code
    - __init__.py [521 bytes]     - code.py [50133 bytes]     - source.py [7278 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\_io
    - __init__.py [190 bytes]     - pprint.py [19633 bytes]     - saferepr.py [4082 bytes]     - terminalwriter.py [9319 bytes]     - wcwidth.py [1289 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\_py
    - __init__.py [0 bytes]     - error.py [3015 bytes]     - path.py [49211 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\assertion
    - __init__.py [6791 bytes]     - rewrite.py [48404 bytes]     - truncate.py [4459 bytes]     - util.py [20265 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\config
    - __init__.py [70645 bytes]     - argparsing.py [20562 bytes]     - compat.py [2938 bytes]     - exceptions.py [288 bytes]     - findpaths.py [8062 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\mark
    - __init__.py [9307 bytes]     - expression.py [10152 bytes]     - structures.py [21039 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\assertion\__pycache__
    - __init__.cpython-313.pyc [9482 bytes]     - rewrite.cpython-313.pyc [62805 bytes]     - truncate.cpython-313.pyc [3760 bytes]     - util.cpython-313.pyc [24351 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\config\__pycache__
    - __init__.cpython-313.pyc [81809 bytes]     - argparsing.cpython-313.pyc [26154 bytes]     - compat.cpython-313.pyc [3589 bytes]     - exceptions.cpython-313.pyc [848 bytes]     - findpaths.cpython-313.pyc [9287 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\mark\__pycache__
    - __init__.cpython-313.pyc [12643 bytes]     - expression.cpython-313.pyc [16876 bytes]     - structures.cpython-313.pyc [25937 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\_code\__pycache__
    - __init__.cpython-313.pyc [666 bytes]     - code.cpython-313.pyc [62655 bytes]     - source.cpython-313.pyc [11412 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\_io\__pycache__
    - __init__.cpython-313.pyc [357 bytes]     - pprint.cpython-313.pyc [24579 bytes]     - saferepr.cpython-313.pyc [5817 bytes]     - terminalwriter.cpython-313.pyc [11538 bytes]     - wcwidth.cpython-313.pyc [1687 bytes]

📁 D:\Agentleefreshidea\.venv\Lib\site-packages\_pytest\_py\__pycache__
    - __init__.cpython-313.pyc [165 bytes]     - error.cpython-313.pyc [4888 bytes]     - path.cpython-313.pyc [69367 bytes]

📁 D:\Agentleefreshidea\.venv\Scripts\__pycache__
    - pywin32_postinstall.cpython-313.pyc [30282 bytes]     - pywin32_testall.cpython-313.pyc [4782 bytes]

