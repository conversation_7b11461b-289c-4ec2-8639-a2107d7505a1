const { app, BrowserWindow, ipcMain, shell, dialog } = require('electron');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');
const fs = require('fs');
const os = require('os');

// Keep a global reference of the mainWindow object
let mainWindow;
let backendProcess = null;
const isWindows = process.platform === 'win32';
const isMac = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

// Define backend paths
const backendPath = path.join(app.getAppPath(), 'backend');
const backendScriptWindows = path.join(backendPath, 'start_backend.ps1');
const backendScriptUnix = path.join(backendPath, 'start_backend.sh');

/**
 * Create the main application window
 */
function createMainWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: 'Agent Lee™ System Assistant',
    icon: path.join(app.getAppPath(), 'assets/icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: !app.isPackaged,
    },
    autoHideMenuBar: app.isPackaged, // Hide menu bar in production
    show: false, // Don't show until ready
  });

  // Load the index.html file
  mainWindow.loadFile(path.join(app.getAppPath(), 'frontend/index.html'));

  // Show window when ready to avoid flashing
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open external links in default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Handle window close
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

/**
 * Start the backend server
 */
async function startBackendServer() {
  try {
    console.log('Starting backend server...');
    
    // Determine the appropriate script based on platform
    let scriptPath;
    let spawnArgs;
    
    if (isWindows) {
      scriptPath = backendScriptWindows;
      spawnArgs = ['powershell', ['-ExecutionPolicy', 'Bypass', '-File', scriptPath]];
    } else {
      scriptPath = backendScriptUnix;
      // Make script executable
      try {
        fs.chmodSync(scriptPath, '755');
      } catch (err) {
        console.error('Error making script executable:', err);
      }
      spawnArgs = [scriptPath, []];
    }

    // Check if script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Backend script not found: ${scriptPath}`);
      dialog.showErrorBox(
        'Backend Error',
        `Could not find backend script at ${scriptPath}`
      );
      return false;
    }

    // Spawn backend process
    backendProcess = spawn(...spawnArgs, {
      cwd: backendPath,
      stdio: 'pipe',
      shell: true,
    });

    // Log output
    backendProcess.stdout.on('data', (data) => {
      console.log(`Backend stdout: ${data}`);
    });

    backendProcess.stderr.on('data', (data) => {
      console.error(`Backend stderr: ${data}`);
    });

    backendProcess.on('error', (err) => {
      console.error('Failed to start backend process:', err);
      dialog.showErrorBox(
        'Backend Error',
        `Failed to start backend: ${err.message}`
      );
    });

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
      backendProcess = null;
    });

    // Wait for backend to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    return true;
  } catch (error) {
    console.error('Error starting backend:', error);
    return false;
  }
}

/**
 * App initialization
 */
app.whenReady().then(async () => {
  // Start backend server
  const backendStarted = await startBackendServer();
  
  // Create main window whether backend started or not
  // The UI will handle connectivity errors
  createMainWindow();
  
  // Set app menu
  if (isMac) {
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
      }
    });
  }
});

/**
 * Handle app shutdown
 */
app.on('window-all-closed', () => {
  if (backendProcess) {
    console.log('Killing backend process...');
    if (isWindows) {
      spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
    } else {
      backendProcess.kill('SIGTERM');
    }
  }
  
  if (!isMac) {
    app.quit();
  }
});

/**
 * IPC Message Handlers
 */
// Handler for checking backend status
ipcMain.handle('check-backend', async () => {
  try {
    const response = await fetch('http://localhost:8000/api/system_status');
    return response.ok;
  } catch (error) {
    console.error('Backend check error:', error);
    return false;
  }
});

// Handler for restarting backend
ipcMain.handle('restart-backend', async () => {
  if (backendProcess) {
    if (isWindows) {
      spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
    } else {
      backendProcess.kill('SIGTERM');
    }
    backendProcess = null;
  }
  
  return await startBackendServer();
});