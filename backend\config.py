"""
Configuration management for Agent Lee™ System
"""

import os
import platform
from typing import List, Optional
from dotenv import load_dotenv

# Load environment variables if not already loaded
try:
    load_dotenv()
except Exception:
    pass  # Environment variables may already be loaded

class AppConfig:
    """Application configuration with environment variable support"""
    
    # Basic server configuration
    DEBUG: bool = os.getenv("AGENTLEE_DEBUG", "False").lower() == "true"
    HOST: str = os.getenv("AGENTLEE_HOST", "127.0.0.1")
    PORT: int = int(os.getenv("AGENTLEE_PORT", "8000"))
    
    # Security configuration
    ALLOWED_ORIGINS: List[str] = [
        origin.strip() 
        for origin in os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000").split(",")
        if origin.strip()
    ]
    
    # Database configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./agentlee.db")
    
    # LLM configuration
    GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")
    OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OLLAMA_MODEL: str = os.getenv("OLLAMA_MODEL", "llama2")
    
    # Logging configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "agentlee.log")
    
    # TTS configuration
    TTS_ENGINE: str = os.getenv("TTS_ENGINE", "pyttsx3")
    TTS_VOICE: str = os.getenv("TTS_VOICE", "default")
    TTS_RATE: float = float(os.getenv("TTS_RATE", "1.0"))
    
    # Voice recognition configuration
    VOICE_RECOGNITION_TIMEOUT: int = int(os.getenv("VOICE_RECOGNITION_TIMEOUT", "5"))
    VOICE_RECOGNITION_PHRASE_TIMEOUT: int = int(os.getenv("VOICE_RECOGNITION_PHRASE_TIMEOUT", "1"))
    
    # Browser paths (auto-detected)
    CHROME_PATH: Optional[str] = None
    FIREFOX_PATH: Optional[str] = None
    
    @classmethod
    def detect_browsers(cls):
        """Detect browser paths based on operating system"""
        system = platform.system()
        
        if system == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
            ]
            firefox_paths = [
                r"C:\Program Files\Mozilla Firefox\firefox.exe",
                r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
            ]
        elif system == "Darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary"
            ]
            firefox_paths = [
                "/Applications/Firefox.app/Contents/MacOS/firefox"
            ]
        else:  # Linux and others
            chrome_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser"
            ]
            firefox_paths = [
                "/usr/bin/firefox"
            ]
        
        # Find first existing Chrome path
        for path in chrome_paths:
            if os.path.exists(path):
                cls.CHROME_PATH = path
                break
        
        # Find first existing Firefox path
        for path in firefox_paths:
            if os.path.exists(path):
                cls.FIREFOX_PATH = path
                break
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate configuration and return list of warnings/errors"""
        warnings = []
        
        # Check if Gemini API key is provided
        if not cls.GEMINI_API_KEY:
            warnings.append("GEMINI_API_KEY not provided - Gemini AI features will be disabled")
        
        # Check database URL format
        if not cls.DATABASE_URL:
            warnings.append("DATABASE_URL not provided - using default SQLite")
        
        # Check CORS origins
        if "*" in cls.ALLOWED_ORIGINS and not cls.DEBUG:
            warnings.append("CORS allows all origins in production mode - security risk")
        
        # Check log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if cls.LOG_LEVEL.upper() not in valid_log_levels:
            warnings.append(f"Invalid LOG_LEVEL '{cls.LOG_LEVEL}' - using INFO")
            cls.LOG_LEVEL = "INFO"
        
        return warnings
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """Get a summary of current configuration (excluding sensitive data)"""
        return {
            "debug": cls.DEBUG,
            "host": cls.HOST,
            "port": cls.PORT,
            "allowed_origins": cls.ALLOWED_ORIGINS,
            "database_type": "sqlite" if "sqlite" in cls.DATABASE_URL else "postgresql" if "postgresql" in cls.DATABASE_URL else "other",
            "gemini_configured": bool(cls.GEMINI_API_KEY),
            "ollama_url": cls.OLLAMA_BASE_URL,
            "ollama_model": cls.OLLAMA_MODEL,
            "log_level": cls.LOG_LEVEL,
            "tts_engine": cls.TTS_ENGINE,
            "chrome_available": bool(cls.CHROME_PATH),
            "firefox_available": bool(cls.FIREFOX_PATH),
            "platform": platform.system()
        }

# Initialize browser detection
AppConfig.detect_browsers()
