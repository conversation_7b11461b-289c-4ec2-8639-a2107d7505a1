{"version": 3, "file": "defaultFileCompare.js", "sourceRoot": "", "sources": ["../../../../src/fileCompareHandler/default/defaultFileCompare.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,4CAAmB;AACnB,gEAAsC;AACtC,sEAAkE;AAClE,mEAA0C;AAC1C,oDAA4D;AAC5D,mEAA0C;AAI1C,MAAM,2BAA2B,GAAG,CAAC,CAAA;AACrC,MAAM,QAAQ,GAAG,MAAM,CAAA;AACvB,MAAM,OAAO,GAAG,IAAI,yCAAmB,CAAC,2BAA2B,GAAG,CAAC,CAAC,CAAA;AACxE,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,QAAQ,EAAE,2BAA2B,CAAC,CAAA,CAAE,+HAA+H;AAE5L,QAAA,kBAAkB,GAAuB;IAClD;;OAEG;IACH,WAAW,CAAC,KAAa,EAAE,KAAe,EAAE,KAAa,EAAE,KAAe,EAAE,OAAgB;QACxF,IAAI,GAAuB,CAAA;QAC3B,IAAI,GAAuB,CAAA;QAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;YAC3B,OAAO,KAAK,CAAA;SACf;QACD,MAAM,UAAU,GAAG,UAAU,CAAC,eAAe,EAAE,CAAA;QAC/C,IAAI;YACA,GAAG,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YAC7B,GAAG,GAAG,YAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;YAC7B,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;YAC5B,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;YAC5B,SAAU;gBACN,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;gBACvD,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;gBACvD,IAAI,KAAK,KAAK,KAAK,EAAE;oBACjB,OAAO,KAAK,CAAA;iBACf;qBAAM,IAAI,KAAK,KAAK,CAAC,EAAE;oBACpB,sBAAsB;oBACtB,OAAO,IAAI,CAAA;iBACd;qBAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE;oBAC3C,OAAO,KAAK,CAAA;iBACf;aACJ;SACJ;gBAAS;YACN,mBAAS,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YAClC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;SACrC;IACL,CAAC;IAGD;;OAEG;IACG,YAAY,CAAC,KAAa,EAAE,KAAe,EAAE,KAAa,EAAE,KAAe,EAAE,OAAgB;;YAC/F,IAAI,GAAuB,CAAA;YAC3B,IAAI,GAAuB,CAAA;YAC3B,IAAI,UAAkC,CAAA;YACtC,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;gBAC3B,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;aAChC;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;iBACjF,IAAI,CAAC,GAAG,CAAC,EAAE;gBACR,UAAU,GAAG,UAAU,CAAC,eAAe,EAAE,CAAA;gBACzC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;gBACZ,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;gBACZ,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;gBAC5B,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAA;gBAC5B,MAAM,oBAAoB,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;oBAC3C,mBAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;oBAC5C,mBAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC;iBAC/C,CAAC;qBACG,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;oBAClB,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;oBAC5B,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;oBAC5B,IAAI,KAAK,KAAK,KAAK,EAAE;wBACjB,OAAO,KAAK,CAAA;qBACf;yBAAM,IAAI,KAAK,KAAK,CAAC,EAAE;wBACpB,sBAAsB;wBACtB,OAAO,IAAI,CAAA;qBACd;yBAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE;wBAC3C,OAAO,KAAK,CAAA;qBACf;yBAAM;wBACH,OAAO,oBAAoB,EAAE,CAAA;qBAChC;gBACL,CAAC,CAAC,CAAA;gBACN,OAAO,oBAAoB,EAAE,CAAA;YACjC,CAAC,CAAC;iBACD,IAAI;YACD,0CAA0C;YAC1C,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAC1D,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG,CAAA,CAAC,CAAC,CAAC,CACvE,CAAA;QACT,CAAC;KAAA;CAEJ,CAAA;AAGD,SAAS,cAAc,CAAC,IAAY,EAAE,IAAY,EAAE,WAAmB;IACnE,OAAO,sBAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAA;AAC9E,CAAC;AAED,SAAS,aAAa,CAAC,GAAY,EAAE,GAAY,EAAE,UAAuB;IACtE,IAAI,UAAU,EAAE;QACZ,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;KACrC;IACD,OAAO,mBAAS,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;AACvD,CAAC"}