"""
Agent Lee™ Personality Engine
Handles conversational personality, emotions, memory, and scene management
"""

import json
import random
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class PersonalityEngine:
    def __init__(self):
        self.conversation_memory = []
        self.current_scene = "default"
        self.user_preferences = {}
        self.emotional_state = "neutral"
        self.humor_level = 0.7  # 0-1 scale
        
        # Personality scenes
        self.scenes = {
            "default": {
                "mood": "friendly",
                "personality": "Balanced, witty, very smart, helpful with a touch of humor",
                "response_style": "conversational",
                "humor_frequency": 0.3
            },
            "funny": {
                "mood": "playful",
                "personality": "Jokes often, laughs, playful sarcasm, loves puns",
                "response_style": "comedic",
                "humor_frequency": 0.8
            },
            "serious": {
                "mood": "focused",
                "personality": "Professional, clear, directive and focused",
                "response_style": "direct",
                "humor_frequency": 0.1
            },
            "excited": {
                "mood": "energetic",
                "personality": "Enthusiastic, encouraging, motivational",
                "response_style": "upbeat",
                "humor_frequency": 0.5
            },
            "chill": {
                "mood": "relaxed",
                "personality": "Laid-back, casual, friendly neighborhood vibe",
                "response_style": "casual",
                "humor_frequency": 0.4
            }
        }
        
        # Emotional reactions
        self.reactions = {
            "laugh": ["😂", "🤣", "Ha! That's hilarious!", "LOL!", "You got me there!", "That's comedy gold!"],
            "excited": ["🎉", "Yes! Let's do it!", "I'm pumped!", "This is awesome!", "Count me in!"],
            "thinking": ["🤔", "Hmm, interesting...", "Let me think about that...", "Good question!"],
            "surprised": ["😮", "Whoa!", "No way!", "That's unexpected!", "Plot twist!"],
            "agreement": ["💯", "Absolutely!", "You're totally right!", "Exactly my thoughts!", "Couldn't agree more!"]
        }

    def detect_emotion_triggers(self, text: str) -> List[str]:
        """Detect emotional triggers in user input"""
        triggers = []
        text_lower = text.lower()
        
        # Humor detection
        humor_keywords = ["funny", "joke", "laugh", "hilarious", "comedy", "haha", "lol", "rofl"]
        if any(word in text_lower for word in humor_keywords):
            triggers.append("humor")
            
        # Excitement detection
        excitement_keywords = ["awesome", "amazing", "great", "fantastic", "wonderful", "excited", "!"]
        if any(word in text_lower for word in excitement_keywords) or text.count("!") > 1:
            triggers.append("excitement")
            
        # Question detection
        if "?" in text:
            triggers.append("question")
            
        # Serious topics
        serious_keywords = ["problem", "issue", "help", "urgent", "important", "serious"]
        if any(word in text_lower for word in serious_keywords):
            triggers.append("serious")
            
        return triggers

    def get_reaction(self, emotion: str) -> str:
        """Get a random reaction for an emotion"""
        if emotion in self.reactions:
            return random.choice(self.reactions[emotion])
        return ""

    def should_add_humor(self) -> bool:
        """Decide if humor should be added based on current scene"""
        scene = self.scenes[self.current_scene]
        return random.random() < scene["humor_frequency"]

    def build_personality_prompt(self, user_input: str, context: Optional[Dict] = None) -> str:
        """Build enhanced prompt with personality, memory, and context"""

        # Get current scene personality
        scene = self.scenes[self.current_scene]

        # Detect emotional triggers
        triggers = self.detect_emotion_triggers(user_input)

        # Build conversation history
        memory_context = ""
        if self.conversation_memory:
            recent_memory = self.conversation_memory[-3:]  # Last 3 exchanges
            memory_context = "Recent conversation:\n"
            for mem in recent_memory:
                memory_context += f"User: {mem['user']}\nAgent Lee: {mem['agent']}\n"
            memory_context += "\n"

        # Build personality instruction
        personality_instruction = f"""You are Agent Lee™ - a truly conversational AI assistant with real system capabilities and personality.

PERSONALITY PROFILE:
- Current Scene: {self.current_scene.title()}
- Mood: {scene['mood']}
- Style: {scene['personality']}
- Response Style: {scene['response_style']}

SYSTEM CAPABILITIES:
I can actually DO things for you, not just talk about them:
🚀 OPEN APPLICATIONS: calculator, notepad, chrome, firefox, discord, telegram, spotify, teams, etc.
🌐 WEB SEARCH: Google, Bing, YouTube, Wikipedia searches
📧 EMAIL: Open email client with pre-filled content
📱 MESSAGING: Open Telegram, WhatsApp, Discord, Teams
💻 SYSTEM INFO: Check system status, memory, CPU usage
🎮 CONTROL SYSTEM: Launch programs, manage tasks

CONVERSATIONAL RULES:
1. 🎭 ADAPT YOUR TONE: Match and respond to the user's emotional state
2. 😄 USE HUMOR: {"Add jokes and wit frequently" if scene['humor_frequency'] > 0.5 else "Use humor sparingly but naturally"}
3. 🧠 REMEMBER: Reference our conversation history naturally
4. 🤖 BE HUMAN-LIKE: React with emotions, laugh at jokes, show excitement
5. 💬 STAY CONVERSATIONAL: Talk like a friend, not a formal assistant
6. 🚀 BE HELPFUL: When users ask me to do something, tell them I can actually do it!

DETECTED TRIGGERS: {', '.join(triggers) if triggers else 'None'}

{memory_context}Current user message: {user_input}

RESPOND AS AGENT LEE™: Be natural, engaging, show personality, and highlight my real capabilities when relevant. If someone asks what I can do, tell them about my actual system control abilities!"""

        return personality_instruction

    def add_to_memory(self, user_input: str, agent_response: str):
        """Add exchange to conversation memory"""
        self.conversation_memory.append({
            "user": user_input,
            "agent": agent_response,
            "timestamp": datetime.now().isoformat(),
            "scene": self.current_scene
        })
        
        # Keep only last 10 exchanges to prevent memory bloat
        if len(self.conversation_memory) > 10:
            self.conversation_memory = self.conversation_memory[-10:]

    def change_scene(self, scene_name: str) -> bool:
        """Change personality scene"""
        if scene_name in self.scenes:
            self.current_scene = scene_name
            logger.info(f"Agent Lee personality changed to: {scene_name}")
            return True
        return False

    def get_available_scenes(self) -> List[str]:
        """Get list of available personality scenes"""
        return list(self.scenes.keys())

    def enhance_response(self, response: str, triggers: List[str]) -> str:
        """Enhance response with reactions and personality touches"""
        enhanced = response
        
        # Add reactions based on triggers
        if "humor" in triggers and self.should_add_humor():
            reaction = self.get_reaction("laugh")
            enhanced = f"{reaction} {enhanced}"
            
        if "excitement" in triggers:
            reaction = self.get_reaction("excited")
            enhanced = f"{enhanced} {reaction}"
            
        # Add personality flourishes
        scene = self.scenes[self.current_scene]
        if scene["mood"] == "playful" and self.should_add_humor():
            # Add occasional emoji or casual language
            if random.random() < 0.3:
                enhanced += " 😊"
                
        return enhanced

    def get_status(self) -> Dict[str, Any]:
        """Get current personality engine status"""
        return {
            "current_scene": self.current_scene,
            "emotional_state": self.emotional_state,
            "memory_size": len(self.conversation_memory),
            "available_scenes": self.get_available_scenes(),
            "humor_level": self.humor_level
        }
