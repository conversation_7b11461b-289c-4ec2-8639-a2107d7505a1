# Agent Lee: Unified Startup & Diagnostic Script

Write-Host "`n🚀 Booting Agent Lee Enhanced AI System..." -ForegroundColor Cyan

# 1. Set working directory
Set-Location -Path "$PSScriptRoot"

# 2. Define paths
$venvScripts = ".\.venv\Scripts"
$backend = ".\backend"
$requirements = "$backend\requirements.txt"
$entry = "$backend\start_enhanced.py"

# 3. Ensure we're inside the virtual environment
if (-not $env:VIRTUAL_ENV) {
    Write-Host "`n❌ You are not in a virtual environment!" -ForegroundColor Red
    Write-Host "💡 Run: ``.venv\Scripts\Activate.ps1`` then re-run this script."
    exit 1
}

# 4. Validate required files
$requiredFiles = @($requirements, $entry)
foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "`n❌ Missing file: $file" -ForegroundColor Red
        exit 1
    }
}

# 5. Check for required Python modules using `python -c`
$requiredModules = @(
    @{name="python-dotenv"; module="dotenv"},
    @{name="fastapi"; module="fastapi"},
    @{name="uvicorn"; module="uvicorn"}
)

$missingModules = @()
foreach ($mod in $requiredModules) {
    $code = "import $($mod.module)"
    python -c $code 2>$null
    if ($LASTEXITCODE -ne 0) {
        $missingModules += $mod.name
    }
}

if ($missingModules.Count -gt 0) {
    Write-Host "`n📦 Installing missing packages: $($missingModules -join ', ')" -ForegroundColor Yellow
    pip install $missingModules
} else {
    Write-Host "`n✅ All required Python packages are installed." -ForegroundColor Green
}

# 6. Start backend service
Write-Host "`n⚙️ Starting FastAPI backend at http://127.0.0.1:8000..." -ForegroundColor Green
python $entry
