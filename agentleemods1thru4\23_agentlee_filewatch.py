# Module 23: File monitoring and change detection

import os
import time

class FileWatcher:
    def __init__(self, path):
        self.path = path
        self.last_modified = os.path.getmtime(path)

    def has_changed(self):
        current_mod = os.path.getmtime(self.path)
        if current_mod != self.last_modified:
            self.last_modified = current_mod
            return True
        return False
