Agent Lee System v2.0 Simple Startup
==================================================
[OK] Environment variables loaded
[OK] GEMINI_API_KEY loaded
[OK] Configuration: 127.0.0.1:8000 (debug: False)

[INFO] Starting Agent Lee System v2.0...
2025-05-31 06:52:32,754 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 06:52:34,817 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-pro', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-pro'}
[INFO] Server starting at http://127.0.0.1:8000
[INFO] API documentation: http://localhost:8000/docs
[INFO] Press Ctrl+C to stop
--------------------------------------------------
2025-05-31 06:52:34,874 [INFO] agentlee.controller: Agent Lee™ System Controller v2.0 starting up...
2025-05-31 06:52:34,948 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 06:52:34,948 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 06:52:34,948 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
2025-05-31 06:53:14,648 [INFO] agentlee.controller: Agent Lee™ System Controller shutting down...
