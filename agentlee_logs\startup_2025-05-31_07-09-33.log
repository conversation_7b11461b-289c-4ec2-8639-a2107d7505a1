[OK] Environment variables loaded
Agent Lee System v2.0 - Single Startup
==================================================
[OK] Python version: 3.13.2
[OK] backend/agentlee_controller_v2.py
[OK] backend/config.py
[OK] backend/models.py
[OK] backend/llm_service.py
[OK] backend/.env.example
[OK] fastapi
[OK] uvicorn
[OK] pydantic
[OK] psutil
[OK] sqlalchemy
[OK] python-dotenv
[OK] google-generativeai
[OK] .env file found

[INFO] Starting Agent Lee System v2.0...
==================================================
2025-05-31 07:09:35,154 [INFO] agentlee.llm: Gemini API initialized successfully
2025-05-31 07:09:37,216 [INFO] agentlee.controller: LLM Service initialized: {'gemini_available': True, 'ollama_available': True, 'available_models': ['gemini-pro', 'ollama-llama2', 'fallback'], 'primary_model': 'gemini-pro'}
[INFO] Server starting at http://127.0.0.1:8000
[INFO] API documentation: http://localhost:8000/docs
[INFO] Press Ctrl+C to stop
--------------------------------------------------
2025-05-31 07:09:37,266 [INFO] agentlee.controller: Agent LeeΓäó System Controller v2.0 starting up...
2025-05-31 07:09:37,268 [INFO] agentlee.controller: Database tables created/verified
2025-05-31 07:09:37,268 [INFO] agentlee.controller: Background health monitoring started
2025-05-31 07:09:37,268 [INFO] agentlee.controller: Configuration: {'debug': False, 'host': '127.0.0.1', 'port': 8000, 'allowed_origins': ['http://localhost', 'http://127.0.0.1'], 'database_type': 'sqlite', 'gemini_configured': True, 'ollama_url': 'http://localhost:11434', 'ollama_model': 'llama2', 'log_level': 'info', 'tts_engine': 'pyttsx3', 'chrome_available': False, 'firefox_available': False, 'platform': 'Windows'}
