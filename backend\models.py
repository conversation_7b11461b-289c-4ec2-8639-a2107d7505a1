"""
Database models for Agent Lee™ System
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, JSON, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
import os
from dotenv import load_dotenv

# Load environment variables if not already loaded
try:
    load_dotenv()
except Exception:
    pass  # Environment variables may already be loaded

Base = declarative_base()

class Conversation(Base):
    """Store conversation history"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), index=True)
    user_message = Column(Text)
    agent_response = Column(Text)
    model_used = Column(String(100))
    confidence = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON)

class Task(Base):
    """Store task assignments and status"""
    __tablename__ = "tasks"
    
    id = Column(String(255), primary_key=True, index=True)
    task_type = Column(String(100))
    priority = Column(Integer, default=1)
    assigned_to = Column(String(255))
    description = Column(Text)
    status = Column(String(50), default="queued")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    metadata = Column(JSON)

class SystemMetric(Base):
    """Store system health metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(100))
    metric_value = Column(Float)
    metric_unit = Column(String(50))
    recorded_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON)

class VoiceCommand(Base):
    """Store voice command history"""
    __tablename__ = "voice_commands"
    
    id = Column(Integer, primary_key=True, index=True)
    command_text = Column(Text)
    confidence = Column(Float)
    action_taken = Column(String(255))
    success = Column(Boolean)
    processed_at = Column(DateTime, default=datetime.utcnow)
    response = Column(Text)
    metadata = Column(JSON)

class AgentSession(Base):
    """Store agent session information"""
    __tablename__ = "agent_sessions"
    
    id = Column(String(255), primary_key=True, index=True)
    user_id = Column(String(255), nullable=True)
    started_at = Column(DateTime, default=datetime.utcnow)
    last_activity = Column(DateTime, default=datetime.utcnow)
    session_data = Column(JSON)
    is_active = Column(Boolean, default=True)

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./agentlee.db")

engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables():
    """Create all database tables"""
    Base.metadata.create_all(bind=engine)

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
