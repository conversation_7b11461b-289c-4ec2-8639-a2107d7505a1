# Quick Start Script for Agent Lee™ - Fixes PowerShell Logging Issue
# Simple version with split stdout/stderr logging

Write-Host "🚀 Agent Lee™ Quick Start" -ForegroundColor Cyan

# Create logs directory and setup split logging
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$logDir = ".\agentlee_logs"
$stdoutLog = "$logDir\startup_${timestamp}_out.log"
$stderrLog = "$logDir\startup_${timestamp}_err.log"

if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir | Out-Null
}

Write-Host "📄 Logging to separate files:"
Write-Host "   STDOUT: $stdoutLog"
Write-Host "   STDERR: $stderrLog"

# Run with split logging (fixes PowerShell limitation)
try {
    Start-Process -NoNewWindow -FilePath "python.exe" `
        -ArgumentList "backend\start_simple.py" `
        -RedirectStandardOutput $stdoutLog `
        -RedirectStandardError $stderrLog `
        -Wait
    
    # Post-launch log check
    $hasErrors = (Test-Path $stderrLog) -and ((Get-Content $stderrLog -ErrorAction SilentlyContinue).Length -gt 0)
    
    if ($hasErrors) {
        Write-Host "`n⚠️ Errors detected in STDERR log!" -ForegroundColor Red
        Write-Host "Error details:" -ForegroundColor Yellow
        Get-Content $stderrLog | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
    } else {
        Write-Host "`n✅ Agent Lee started successfully!" -ForegroundColor Green
        Write-Host "🌐 Visit: http://localhost:8000/docs" -ForegroundColor Cyan
    }
    
    # Show last few lines of stdout
    if (Test-Path $stdoutLog) {
        Write-Host "`nLast output lines:" -ForegroundColor Gray
        Get-Content $stdoutLog | Select-Object -Last 5 | ForEach-Object {
            Write-Host "   $_" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "❌ Failed to start: $_" -ForegroundColor Red
}

Write-Host "`n🎯 Logs saved to: $logDir"
