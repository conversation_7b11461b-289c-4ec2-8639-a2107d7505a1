# Agent Lee™ Enhanced Startup Script with Proper Logging
# Fixes PowerShell stdout/stderr redirection issue

param(
    [switch]$Simple,
    [switch]$Test,
    [switch]$OpenBrowser,
    [string]$LogLevel = "INFO"
)

Write-Host "🤖 Agent Lee™ System v2.0 PowerShell Launcher" -ForegroundColor Cyan
Write-Host "=" * 50

# Create logs directory
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$logDir = ".\agentlee_logs"
$stdoutLog = "$logDir\startup_${timestamp}_out.log"
$stderrLog = "$logDir\startup_${timestamp}_err.log"

if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir | Out-Null
    Write-Host "📁 Created logs directory: $logDir" -ForegroundColor Green
}

# Check if virtual environment is activated
if (-not $env:VIRTUAL_ENV) {
    Write-Host "⚠️ Virtual environment not detected. Attempting to activate..." -ForegroundColor Yellow
    
    $venvPaths = @(".\venv\Scripts\Activate.ps1", ".\.venv\Scripts\Activate.ps1")
    $activated = $false
    
    foreach ($venvPath in $venvPaths) {
        if (Test-Path $venvPath) {
            Write-Host "🔄 Activating virtual environment: $venvPath"
            & $venvPath
            $activated = $true
            break
        }
    }
    
    if (-not $activated) {
        Write-Host "❌ No virtual environment found. Please activate manually." -ForegroundColor Red
        Write-Host "   Try: .\.venv\Scripts\Activate.ps1" -ForegroundColor Yellow
        exit 1
    }
}

# Determine which script to run
$scriptToRun = if ($Simple) {
    "backend\start_simple.py"
} elseif ($Test) {
    "backend\test_dotenv_fix.py"
} else {
    "backend\start_enhanced.py"
}

Write-Host "`n🚀 Running: $scriptToRun"
Write-Host "📄 Logging output to:"
Write-Host "   STDOUT: $stdoutLog" -ForegroundColor Gray
Write-Host "   STDERR: $stderrLog" -ForegroundColor Gray

# Set environment variable for log level
$env:AGENTLEE_LOG_LEVEL = $LogLevel

try {
    # Run the Python script with split logging (fixes PowerShell limitation)
    $process = Start-Process -NoNewWindow -FilePath "python.exe" `
        -ArgumentList $scriptToRun `
        -RedirectStandardOutput $stdoutLog `
        -RedirectStandardError $stderrLog `
        -PassThru
    
    # Wait for process to complete
    $process.WaitForExit()
    $exitCode = $process.ExitCode
    
    Write-Host "`n📊 Process completed with exit code: $exitCode"
    
    # Check logs for errors
    $hasStdout = (Test-Path $stdoutLog) -and ((Get-Content $stdoutLog -ErrorAction SilentlyContinue).Length -gt 0)
    $hasStderr = (Test-Path $stderrLog) -and ((Get-Content $stderrLog -ErrorAction SilentlyContinue).Length -gt 0)
    
    if ($hasStdout) {
        Write-Host "`n📄 STDOUT Output:" -ForegroundColor Green
        Get-Content $stdoutLog | Select-Object -Last 10 | ForEach-Object {
            Write-Host "   $_" -ForegroundColor Gray
        }
    }
    
    if ($hasStderr) {
        Write-Host "`n⚠️ STDERR Output:" -ForegroundColor Red
        Get-Content $stderrLog | Select-Object -Last 10 | ForEach-Object {
            Write-Host "   $_" -ForegroundColor Red
        }
    }
    
    # Success/failure determination
    if ($exitCode -eq 0 -and -not $hasStderr) {
        Write-Host "`n✅ Agent Lee started successfully!" -ForegroundColor Green
        
        if ($OpenBrowser -and -not $Test) {
            Write-Host "🌐 Opening browser to API documentation..."
            Start-Process "http://localhost:8000/docs"
        }
        
        if (-not $Test) {
            Write-Host "`n🔗 Available endpoints:"
            Write-Host "   • Main API: http://localhost:8000" -ForegroundColor Cyan
            Write-Host "   • Documentation: http://localhost:8000/docs" -ForegroundColor Cyan
            Write-Host "   • Health Check: http://localhost:8000/api/system_status" -ForegroundColor Cyan
        }
    } else {
        Write-Host "`n❌ Agent Lee startup encountered issues." -ForegroundColor Red
        Write-Host "   Check the error log: $stderrLog" -ForegroundColor Yellow
        
        if ($hasStderr) {
            Write-Host "`n💡 Common solutions:"
            Write-Host "   • Install dependencies: pip install -r backend\requirements.txt"
            Write-Host "   • Check .env configuration"
            Write-Host "   • Try simple mode: .\start_agentlee_enhanced.ps1 -Simple"
        }
    }
    
} catch {
    Write-Host "`n❌ Failed to start process: $_" -ForegroundColor Red
    exit 1
}

# Cleanup old logs (keep last 5)
try {
    $oldLogs = Get-ChildItem $logDir -Filter "startup_*" | Sort-Object CreationTime -Descending | Select-Object -Skip 10
    if ($oldLogs) {
        $oldLogs | Remove-Item -Force
        Write-Host "`n🧹 Cleaned up $($oldLogs.Count) old log files" -ForegroundColor Gray
    }
} catch {
    # Ignore cleanup errors
}

Write-Host "`n" + "=" * 50
Write-Host "🤖 Agent Lee™ PowerShell Launcher Complete" -ForegroundColor Cyan

# Usage examples
if ($Test) {
    Write-Host "`n💡 Next steps:"
    Write-Host "   • Run full system: .\start_agentlee_enhanced.ps1"
    Write-Host "   • Run simple mode: .\start_agentlee_enhanced.ps1 -Simple"
    Write-Host "   • Open browser: .\start_agentlee_enhanced.ps1 -OpenBrowser"
}
