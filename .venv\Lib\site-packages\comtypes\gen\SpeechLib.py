from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    <PERSON>p<PERSON><PERSON><PERSON>, DISPID_SOTGetAttribute, SAFTADPCM_8kHzStereo,
    SVSFUnusedFlags, DISPIDSPTSI_ActiveLength,
    SpeechPropertyNormalConfidenceThreshold, SpeechCategoryAudioIn,
    ISpLexicon, DISPID_SRGState, SAFT12kHz8BitStereo, SVP_13,
    DISPID_SAFGetWaveFormatEx, ISpeechCustomStream, SPWORD,
    DISPID_SRGRecoContext, SpeechTokenIdUserLexicon, IStream,
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS, SP_VISEME_20,
    DIS<PERSON><PERSON>_SRIsShared, SAFTGSM610_8k<PERSON>zM<PERSON>, SPWF_SRENGINE,
    ISpeechXMLRecoResult, VARIANT_BOOL, SAFT11kHz8BitMono,
    ISpeechFileStream, DISPID_SRAudioInput,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, SAFTGSM610_11kHzMono,
    DISPID_SLWPronunciations, DISPID_SRCERecognition, SAFTDefault,
    SPPS_Interjection, ISpPhoneConverter, SPINTERFERENCE_NOSIGNAL,
    DISPID_SMSSetData, GUID, ISpRecognizer2, ISpPhraseAlt,
    DISPID_SDKDeleteValue, SREPrivate, ISpeechPhraseInfo,
    STSF_FlagCreate, CoClass, SPINTERFERENCE_TOOLOUD,
    DISPID_SVIsUISupported, SPCT_SUB_DICTATION, ISpXMLRecoResult,
    SVP_21, eLEXTYPE_PRIVATE20, DISPID_SPRuleFirstElement,
    DISPID_SGRSTText, SPEI_VISEME, DISPID_SBSFormat, SVEVoiceChange,
    SRTStandard, ISpShortcut, DISPID_SASFreeBufferSpace, SVF_None,
    SDKLCurrentConfig, SRTAutopause, typelib_path, DISPID_SVDisplayUI,
    SPFM_OPEN_READWRITE, ISpPhrase, DISPID_SPAStartElementInResult,
    SREPropertyStringChange, SVF_Emphasis,
    DISPID_SVSLastStreamNumberQueued, SPSHORTCUTPAIRLIST,
    DISPID_SRGCmdLoadFromProprietaryGrammar, SAFTCCITT_uLaw_44kHzMono,
    SSFMOpenForRead, DISPID_SRCVoice, SpFileStream,
    SAFTGSM610_22kHzMono, SAFTCCITT_uLaw_11kHzStereo,
    SPEI_TTS_BOOKMARK, SAFT32kHz16BitStereo, SPSModifier,
    SP_VISEME_18, DISPID_SRAllowVoiceFormatMatchingOnNextSet,
    SVSFIsNotXML, _lcid, ISpPhoneticAlphabetConverter,
    _RemotableHandle, SPVOICESTATUS, SPEI_RECO_STATE_CHANGE,
    SPEI_MAX_SR, SECLowConfidence, SVSFNLPSpeakPunc, SLTApp,
    SpeechTokenKeyUI, ISpeechMMSysAudio, DISPID_SLGenerationId,
    SPSHT_OTHER, SGDSActive, DISPID_SPCIdToPhone,
    DISPID_SPAPhraseInfo, ISpeechGrammarRules, SVSFNLPMask,
    WAVEFORMATEX, SpMemoryStream, SpNullPhoneConverter,
    eLEXTYPE_PRIVATE3, DISPID_SRGReset, SAFTCCITT_ALaw_44kHzMono,
    DISPID_SOTDataKey, ISpeechDataKey,
    ISpeechGrammarRuleStateTransitions, DISPID_SRSetPropertyString,
    STSF_CommonAppData, DISPID_SOTRemoveStorageFileName,
    ISpeechAudioFormat, SpInprocRecognizer, SPDKL_CurrentConfig,
    SPVPRI_OVER, ISpeechPhraseAlternate, SPEVENT, ISpRecoGrammar2,
    SRSEIsSpeaking, SP_VISEME_16, SpeechAudioFormatGUIDWave,
    DISPID_SLAddPronunciationByPhoneIds, DISPID_SPEAudioStreamOffset,
    SGRSTTDictation, DISPID_SMSAMMHandle, DISPID_SRCEEndStream,
    DISPID_SPACommit, DISPID_SPEsItem, DISPID_SREmulateRecognition,
    DISPID_SGRSTsCount, IEnumSpObjectTokens,
    DISPID_SRGDictationSetState, DISPID_SASState,
    DISPID_SRRSaveToMemory, DISPID_SRGDictationLoad,
    SDA_Consume_Leading_Spaces, SVP_8, DISPID_SVGetVoices,
    DISPID_SPRFirstElement, SpVoice, DISPID_SRCEEnginePrivate,
    DISPID_SDKGetBinaryValue, SPSHT_NotOverriden,
    SAFTCCITT_uLaw_8kHzMono, SP_VISEME_14, SP_VISEME_5,
    SpeechRegistryUserRoot, ISpeechPhoneConverter, DISPID_SPPId,
    SWTAdded, DISPID_SPPs_NewEnum, ISpNotifyTranslator, SWTDeleted,
    SPPHRASERULE, SAFTADPCM_8kHzMono, ISpRecognizer3,
    DISPID_SRGCmdSetRuleIdState, SREPropertyNumChange, UINT_PTR,
    SVPAlert, SPINTERFERENCE_TOOQUIET, DISPID_SRGSetWordSequenceData,
    DISPID_SPPNumberOfElements, DISPID_SPIEngineId,
    SpeechCategoryVoices, SSFMCreate, eLEXTYPE_PRIVATE12,
    SDKLDefaultLocation, SECFIgnoreKanaType, SINone, SP_VISEME_2,
    ISpGrammarBuilder, SPWORDPRONUNCIATION, SPEI_SR_AUDIO_LEVEL,
    SPFM_CREATE_ALWAYS, SP_VISEME_12, SpeechPropertyAdaptationOn,
    SpWaveFormatEx, SVP_19, Speech_Max_Word_Length, SVP_4, helpstring,
    SPBO_TIME_UNITS, ISpAudio, SPRST_INACTIVE_WITH_PURGE,
    SECNormalConfidence, SREPhraseStart, DISPID_SVSpeakCompleteEvent,
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE, ISpeechRecognizer, SPPS_LMA,
    DISPID_SRCERecognitionForOtherContext, DISPID_SOTCategory,
    DISPID_SDKCreateKey, SpeechTokenValueCLSID, DISPID_SAFType,
    SpLexicon, SRARoot, eLEXTYPE_RESERVED4, SP_VISEME_9,
    ISpeechPhraseInfoBuilder, SpResourceManager,
    SpeechCategoryAppLexicons, DISPID_SPAsCount,
    eLEXTYPE_VENDORLEXICON, DISPID_SRCSetAdaptationData,
    SPSMF_SRGS_SAPIPROPERTIES, SRERecoOtherContext,
    DISPID_SRIsUISupported, SAFT48kHz8BitStereo, ISpeechObjectTokens,
    SGRSTTWord, SRSInactiveWithPurge, SAFTADPCM_11kHzMono,
    SpeechGrammarTagUnlimitedDictation, SPCT_SUB_COMMAND,
    SPINTERFERENCE_NONE, SWPKnownWordPronounceable,
    SAFT16kHz8BitStereo, SPEI_RESERVED5, SVP_16,
    SpeechCategoryPhoneConverters, ISpRecoCategory, SGSEnabled,
    DISPID_SADefaultFormat, SAFT22kHz8BitMono,
    DISPID_SPRuleNumberOfElements, SRAInterpreter, SPAR_Medium,
    SAFTCCITT_ALaw_11kHzStereo, SAFT48kHz16BitMono, ISpeechLexicon,
    DISPID_SABufferInfo, DISPID_SRSNumberOfActiveRules, STCAll,
    SPEI_PROPERTY_NUM_CHANGE, SPPS_Verb, DISPID_SPPsItem,
    SSFMOpenReadWrite, DISPID_SDKOpenKey, DISPID_SPRs_NewEnum,
    eLEXTYPE_PRIVATE14, eLEXTYPE_USER, DISPID_SVPriority,
    DISPID_SPEs_NewEnum, DISPID_SRSClsidEngine, SVEViseme,
    DISPID_SVEStreamEnd, DISPID_SRCreateRecoContext,
    eLEXTYPE_PRIVATE1, SRESoundStart, DISPID_SRCRetainedAudio,
    DISPID_SRGCmdLoadFromResource, DISPID_SBSWrite,
    DISPID_SRSCurrentStreamPosition, SpPhoneConverter,
    DISPID_SPEAudioSizeBytes, SINoise, DISPID_SPPParent,
    SpeechGrammarTagDictation, SPTEXTSELECTIONINFO,
    DISPID_SRRAlternates, SVSFIsXML,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, DISPID_SVResume,
    ISpeechTextSelectionInformation, SpeechVoiceSkipTypeSentence,
    SVP_18, _FILETIME, eLEXTYPE_PRIVATE16,
    DISPID_SRCAudioInInterferenceStatus, ISpRecoContext2,
    SPPS_Unknown, SPEI_VOICE_CHANGE, ISpeechRecoGrammar,
    SpeechVoiceCategoryTTSRate, SPEI_WORD_BOUNDARY, BSTR,
    ISpeechObjectToken, SPPS_RESERVED2, eLEXTYPE_RESERVED8,
    ISpeechRecoResultTimes, DISPID_SVSCurrentStreamNumber,
    DISPID_SRCERecognizerStateChange, DISPID_SDKDeleteKey,
    SPEI_SR_RETAINEDAUDIO, DISPID_SRCEPhraseStart, SVEPrivate,
    SDTLexicalForm, SRAONone, SPEI_UNDEFINED, ISpeechRecoResult,
    DISPID_SGRClear, SPSHORTCUTPAIR, DISPID_SOTGetDescription,
    ISpeechResourceLoader, DISPID_SPRuleParent,
    SpeechPropertyResponseSpeed, DISPID_SRGRules, DISPID_SOTCSetId,
    Speech_Default_Weight, SPSNotOverriden, SVP_5, SPEI_ADAPTATION,
    DISPID_SRRRecoContext, SPEI_MIN_TTS, DISPID_SVSLastBookmark,
    DISPID_SPANumberOfElementsInResult, DISPID_SAEventHandle, SASStop,
    ISpeechRecognizerStatus, DISPID_SRRTLength, DISPID_SPRuleName,
    SAFTCCITT_ALaw_8kHzMono, DISPID_SPIAudioSizeBytes,
    DISPID_SPPFirstElement, eLEXTYPE_RESERVED7, SGPronounciation,
    ISpStreamFormatConverter, SAFTCCITT_ALaw_8kHzStereo, SPPS_Noun,
    DISPID_SPAs_NewEnum, SPSMF_UPS, SPSLMA, SpeechAudioFormatGUIDText,
    SGDSInactive, DISPID_SGRSTPropertyName, SVSFParseAutodetect,
    DISPID_SLGetPronunciations, DISPID_SRGetRecognizers, SRSActive,
    SPRS_ACTIVE_USER_DELIMITED, DISPID_SVSpeakStream,
    DISPID_SVWaitUntilDone, SPLO_DYNAMIC, DISPID_SDKSetLongValue,
    SDA_One_Trailing_Space, SREStateChange, SPPS_Noncontent,
    SP_VISEME_17, ISpeechVoice, SPINTERFERENCE_TOOSLOW,
    ISpeechObjectTokenCategory, SP_VISEME_1, SPAUDIOSTATUS,
    SFTSREngine, DISPIDSPTSI_SelectionLength, DISPID_SRRGetXMLResult,
    SPINTERFERENCE_LATENCY_WARNING, DISPID_SVEEnginePrivate,
    SpeechCategoryRecognizers, SFTInput, SPEI_SOUND_START,
    SPRECOGNIZERSTATUS, DISPID_SRDisplayUI,
    DISPID_SVSInputSentencePosition, SAFTCCITT_uLaw_22kHzMono,
    SPPS_Modifier, DISPID_SLPSymbolic, SPWORDLIST,
    SAFT44kHz8BitStereo, DISPID_SRCESoundEnd, SPSERIALIZEDRESULT,
    SVEEndInputStream, SREHypothesis, ISpObjectWithToken, Library,
    eLEXTYPE_RESERVED10, SGDisplay, SVP_15, STCInprocServer,
    DISPID_SLGetWords, DISPID_SPIGetDisplayAttributes,
    DISPID_SVEBookmark, ISpProperties, SP_VISEME_6,
    DISPID_SVEAudioLevel, DISPID_SVGetProfiles, SPEI_START_SR_STREAM,
    SPEI_END_SR_STREAM, ISpeechGrammarRuleStateTransition,
    SSTTWildcard, DISPID_SPPBRestorePhraseFromMemory,
    eLEXTYPE_LETTERTOSOUND, SPPHRASEPROPERTY,
    DISPID_SPRuleEngineConfidence, DISPID_SRRAudioFormat, SRAExport,
    SPCT_DICTATION, DISPID_SRRDiscardResultInfo, SRCS_Disabled,
    SAFT8kHz8BitStereo, DISPID_SOTsCount, ISpObjectTokenCategory,
    DISPID_SVEPhoneme, SPRS_ACTIVE,
    __MIDL___MIDL_itf_sapi_0000_0020_0002, SREStreamEnd,
    ISpeechLexiconPronunciations, SP_VISEME_4, SpeechUserTraining,
    DISPID_SLPPhoneIds, SpTextSelectionInformation, DISPID_SPCLangId,
    SPEI_RESERVED1, SpeechAddRemoveWord, SVSFDefault, DISPID_SFSClose,
    SASClosed, SPEI_REQUEST_UI, DISPID_SRCEHypothesis,
    DISPID_SABIEventBias, SVP_10, SPRECORESULTTIMES,
    SAFT22kHz16BitMono, DISPID_SGRsAdd, SGSExclusive,
    DISPID_SRGCmdSetRuleState, SPGS_DISABLED, eLEXTYPE_PRIVATE6,
    SPAS_CLOSED, DISPID_SRRSpeakAudio, SpeechAudioProperties,
    SpSharedRecognizer, SpCompressedLexicon, DISPID_SRCEStartStream,
    SAFTADPCM_22kHzMono, DISPID_SDKGetlongValue, SVP_20,
    DISPID_SPELexicalForm, SAFTText, SpSharedRecoContext,
    SPDKL_DefaultLocation, SpStreamFormatConverter, SITooQuiet,
    DISPID_SRGCmdLoadFromMemory, DISPID_SGRSTNextState,
    IServiceProvider, SDKLCurrentUser, eLEXTYPE_USER_SHORTCUT,
    ISpeechLexiconPronunciation, SECFIgnoreCase,
    DISPID_SRCCmdMaxAlternates, SGLexicalNoSpecialChars,
    SPAUDIOBUFFERINFO, DISPID_SPERequiredConfidence, DISPID_SLPsCount,
    SRTEmulated, SDTAlternates, SBONone, SVPNormal,
    SPPHRASEREPLACEMENT, DISPID_SLAddPronunciation, SPEI_RESERVED6,
    DISPID_SASCurrentDevicePosition, SAFT16kHz16BitStereo,
    _check_version, DISPID_SOTCDefault, Speech_Max_Pron_Length,
    SAFT12kHz16BitMono, SVP_11, SREBookmark, DISPID_SRProfile,
    DISPID_SRGetPropertyNumber, SpeechRegistryLocalMachineRoot,
    SPSEMANTICERRORINFO, SDTProperty, SSSPTRelativeToEnd, SP_VISEME_3,
    ISpStreamFormat, DISPID_SRCEFalseRecognition, SPDKL_LocalMachine,
    SPSFunction, DISPID_SOTs_NewEnum, DISPID_SGRAddState,
    SPPS_Function, SAFTExtendedAudioFormat, DISPID_SAFSetWaveFormatEx,
    SLODynamic, SGDSActiveWithAutoPause, SRCS_Enabled,
    DISPID_SAStatus, SRATopLevel, DISPID_SOTCGetDataKey,
    DISPID_SVRate, __MIDL_IWinTypes_0009, SPAS_STOP, SpMMAudioIn,
    DISPID_SLPType, SPAS_RUN, DISPID_SPEsCount, SGRSTTTextBuffer,
    SREFalseRecognition, SGDSActiveUserDelimited, SPVPRI_ALERT,
    eLEXTYPE_PRIVATE17, DISPID_SPPConfidence, SRERecognition,
    DISPID_SRStatus, SAFTCCITT_ALaw_44kHzStereo,
    DISPID_SRCCreateGrammar, SPINTERFERENCE_LATENCY_TRUNCATE_END,
    SASPause, DISPID_SPIEnginePrivateData, DISPID_SRState,
    SPGS_EXCLUSIVE, SP_VISEME_10, DISPID_SPCPhoneToId,
    STSF_LocalAppData, DISPID_SRCEAdaptation, DISPID_SPAsItem,
    DISPID_SVSLastBookmarkId, DISPID_SPRText,
    DISPID_SRCRetainedAudioFormat, SVP_1, SPSSuppressWord,
    SDA_Two_Trailing_Spaces, SPINTERFERENCE_NOISE,
    DISPID_SVAlertBoundary, DISPID_SGRSAddRuleTransition,
    SPWP_KNOWN_WORD_PRONOUNCEABLE, DISPID_SABIBufferSize,
    SSSPTRelativeToStart, DISPID_SRCBookmark, DISPID_SRRecognizer,
    DISPID_SRRAudio, SPCS_DISABLED, ISpeechPhraseReplacement,
    SpInProcRecoContext, DISPID_SPRDisplayAttributes, SBOPause,
    SAFT11kHz8BitStereo, eLEXTYPE_PRIVATE11, DISPID_SGRSTRule,
    SRSEDone, SECFEmulateResult, DISPID_SRCEAudioLevel,
    DISPID_SRGSetTextSelection, DISPID_SRSSupportedLanguages,
    DISPID_SPIRetainedSizeBytes, SPEI_FALSE_RECOGNITION,
    DISPID_SGRSRule, ISpNotifySink, ISpeechRecoContext, SRSInactive,
    DISPID_SVGetAudioInputs, DISPID_SPIStartTime, SECFNoSpecialChars,
    SPEI_MAX_TTS, dispid, DISPID_SVSVisemeId,
    DISPID_SGRsCommitAndSave, DISPID_SRCESoundStart, SECFIgnoreWidth,
    SPPHRASEELEMENT, eLEXTYPE_RESERVED9, ISpeechGrammarRule,
    DISPID_SVEVoiceChange, SLOStatic, DISPID_SVVolume,
    SAFT12kHz8BitMono, DISPID_SRCPause, eLEXTYPE_PRIVATE15,
    DISPID_SPRulesItem, SPAR_High, ISpSerializeState, SPPS_RESERVED1,
    DISPID_SGRSTPropertyValue, DISPID_SRSAudioStatus, SVSFParseSapi,
    SPRST_ACTIVE_ALWAYS, SpeechMicTraining, DISPID_SVPause,
    SPRS_INACTIVE, Speech_StreamPos_Asap, eLEXTYPE_PRIVATE10,
    SPBO_NONE, SPEI_RECO_OTHER_CONTEXT, tagSPTEXTSELECTIONINFO,
    SpShortcut, SAFT44kHz16BitMono, SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE,
    SPFM_CREATE, SAFTADPCM_11kHzStereo, DISPID_SOTsItem,
    SPEI_RESERVED2, SPRST_NUM_STATES, SAFT11kHz16BitMono,
    SPGS_ENABLED, ISpeechAudioStatus, SAFT32kHz8BitStereo,
    DISPID_SPRuleConfidence, SRADynamic, SPRULE,
    SAFTCCITT_uLaw_22kHzStereo, DISPID_SRCState,
    SPSMF_SAPI_PROPERTIES, SREAllEvents, SPEI_START_INPUT_STREAM,
    SPSHT_EMAIL, __MIDL___MIDL_itf_sapi_0000_0020_0001,
    DISPID_SLWWord, SDTAll, SVSFPurgeBeforeSpeak,
    SPXRO_Alternates_SML, SPEI_RECOGNITION, SAFTADPCM_44kHzStereo,
    SpeechPropertyResourceUsage, eLEXTYPE_PRIVATE4, DISPID_SBSRead,
    DISPID_SRAudioInputStream, DISPID_SMSGetData,
    DISPID_SPARecoResult, SP_VISEME_11, SPAR_Unknown,
    DISPID_SRRTTickCount, ISpeechPhraseRules,
    DISPID_SRGCmdLoadFromObject, SpNotifyTranslator, _ULARGE_INTEGER,
    DISPID_SRRGetXMLErrorInfo, SpStream, ISpeechAudio,
    SECHighConfidence, ISpNotifySource, SPRST_ACTIVE, DISPID_SVSpeak,
    SAFTGSM610_44kHzMono, SPBO_AHEAD, STCRemoteServer,
    DISPID_SOTRemove, SVSFParseMask, DISPID_SPPName, SPFM_NUM_MODES,
    SAFT24kHz8BitStereo, SpeechAllElements, SREAdaptation,
    SAFT16kHz16BitMono, DISPID_SGRAddResource,
    ISpPhoneticAlphabetSelection, Speech_StreamPos_RealTime,
    DISPMETHOD, SpeechPropertyHighConfidenceThreshold,
    SDA_No_Trailing_Space, DISPID_SABufferNotifySize,
    ISpeechMemoryStream, SPEI_TTS_AUDIO_LEVEL,
    ISpeechPhraseAlternates, ISpeechBaseStream, SGRSTTEpsilon,
    SpMMAudioEnum, SSFMCreateForWrite, SPSUnknown,
    DISPID_SPISaveToMemory, ISpeechPhraseRule, DISPID_SLPs_NewEnum,
    SpeechEngineProperties, ISpeechAudioBufferInfo, SITooLoud,
    SPINTERFERENCE_TOOFAST, DISPID_SOTCreateInstance,
    DISPID_SPEAudioSizeTime, SAFT8kHz16BitMono, eLEXTYPE_PRIVATE9,
    DISPID_SRCEBookmark, eLEXTYPE_PRIVATE7, DISPIDSPTSI_ActiveOffset,
    ISpeechRecoResultDispatch, SVSFVoiceMask, DISPID_SOTCId,
    SAFT8kHz16BitStereo, DISPID_SLPsItem, eLEXTYPE_PRIVATE13,
    DISPID_SVVoice, SAFT11kHz16BitStereo,
    SSSPTRelativeToCurrentPosition, SVP_0, DISPID_SPRsItem,
    DISPID_SVEStreamStart, DISPID_SVSPhonemeId, SP_VISEME_7, VARIANT,
    eWORDTYPE_ADDED, SRTReSent, ISpeechRecoResult2, SP_VISEME_0,
    ULONG_PTR, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    SAFTNonStandardFormat, SAFT44kHz16BitStereo,
    DISPID_SVSInputWordLength, SVP_2, SPPS_SuppressWord, SITooSlow,
    SAFTCCITT_ALaw_22kHzStereo, DISPID_SRGCommit,
    DISPID_SOTCEnumerateTokens, DISPID_SRRTimes, SAFT24kHz8BitMono,
    SpAudioFormat, SVPOver, SP_VISEME_13, ISpRecoGrammar,
    DISPID_SWFEExtraData, SP_VISEME_21,
    DISPID_SRCEPropertyStringChange, SPEI_END_INPUT_STREAM,
    DISPID_SLWLangId, DISPID_SVSRunningState, ISpDataKey,
    DISPID_SPEDisplayAttributes, SRAORetainAudio, SVEBookmark,
    DISPID_SMSALineId, DISPID_SLWType, DISPID_SVAudioOutputStream,
    DISPID_SPILanguageId, SPSERIALIZEDPHRASE, SITooFast,
    SPEI_SR_PRIVATE, SVP_6, DISPIDSPTSI_SelectionOffset,
    DISPID_SRCRequestedUIType, DISPID_SPIAudioSizeTime, SVSFlagsAsync,
    SpCustomStream, DISPID_SVGetAudioOutputs, DISPID_SGRsDynamic,
    DISPID_SPEDisplayText, SDTDisplayText, SAFTCCITT_ALaw_22kHzMono,
    SSTTDictation, DISPID_SASetState, SPEI_PHRASE_START,
    SPPS_RESERVED3, DISPID_SPPEngineConfidence, ISpRecoResult,
    SPEI_ACTIVE_CATEGORY_CHANGED, SPCT_SLEEP, SVP_14, SREStreamStart,
    SPPS_NotOverriden, DISPID_SRCEventInterests,
    DISPID_SPIReplacements, SRAImport, SpeechAudioVolume,
    SAFT12kHz16BitStereo, SP_VISEME_15, DISPID_SVEViseme,
    DISPID_SPEEngineConfidence, DISPID_SRCERequestUI,
    DISPID_SRCEPropertyNumberChange, SVEAllEvents, ISpeechLexiconWord,
    SAFTCCITT_uLaw_8kHzStereo, DISPID_SPRuleId, SPBO_PAUSE,
    DISPID_SGRSTPropertyId, SVP_9, DISPID_SRCRecognizer,
    eLEXTYPE_PRIVATE19, SPEI_PROPERTY_STRING_CHANGE,
    DISPID_SPEAudioTimeOffset, SPPROPERTYINFO, DISPID_SGRsFindRule,
    DISPID_SGRSAddSpecialTransition, DISPID_SGRSTType,
    DISPID_SGRSAddWordTransition, DISPID_SBSSeek, SRSActiveAlways,
    DISPID_SOTGetStorageFileName, DISPID_SRGetPropertyString,
    SAFTCCITT_ALaw_11kHzMono, DISPID_SRGetFormat, SPCS_ENABLED,
    SPCT_COMMAND, SpUnCompressedLexicon,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet, SPEVENTSOURCEINFO,
    SGSDisabled, DISPID_SPERetainedSizeBytes, SAFT8kHz8BitMono,
    DISPID_SPIElements, DISPID_SVSInputWordPosition,
    IInternetSecurityMgrSite, eLEXTYPE_PRIVATE5,
    DISPID_SWFEBitsPerSample, DISPID_SLRemovePronunciationByPhoneIds,
    SRTSMLTimeout, DISPID_SGRSTWeight, SREInterference,
    ISpeechPhraseElements, DISPID_SGRSTransitions, SPSVerb,
    DISPID_SRGIsPronounceable, SpeechCategoryAudioOut, SVP_7,
    DISPID_SLPPartOfSpeech, SECFDefault, SVP_3, ISpStream,
    SREAudioLevel, DISPID_SRCCreateResultFromMemory,
    ISpeechPhraseProperty, SP_VISEME_8, IUnknown, _ISpeechVoiceEvents,
    DISPID_SRRSetTextFeedback, wireHWND, ISpRecognizer, SRESoundEnd,
    SpeechTokenKeyFiles, DISPID_SABIMinNotification,
    SpPhraseInfoBuilder, DISPID_SLGetGenerationChange, WSTRING,
    SAFT22kHz8BitStereo, ISpeechWaveFormatEx, tagSPPROPERTYINFO,
    LONG_PTR, DISPID_SOTId, SPEI_SR_BOOKMARK,
    DISPID_SVSInputSentenceLength, SpeechPropertyComplexResponseSpeed,
    SpeechRecoProfileProperties, SPAO_NONE, DISPID_SRGDictationUnload,
    SAFT24kHz16BitStereo, SWPUnknownWordUnpronounceable,
    DISPID_SVSyncronousSpeakTimeout, SGRSTTRule, DISPID_SOTSetId,
    ISpeechGrammarRuleState, SAFTTrueSpeech_8kHz1BitMono,
    SpeechDictationTopicSpelling, SPRS_ACTIVE_WITH_AUTO_PAUSE,
    DISPID_SPEPronunciation, DISPID_SRGCmdLoadFromFile,
    DISPID_SAVolume, _ISpeechRecoContextEvents, SPVPRI_NORMAL, SVP_12,
    DISPID_SRCResume, DISPID_SLWs_NewEnum, SSTTTextBuffer, SPAR_Low,
    SPEI_SOUND_END, DISPID_SLPLangId, SAFT32kHz16BitMono,
    DISPID_SPRulesCount, DISPID_SPERetainedStreamOffset,
    SPFM_OPEN_READONLY, ISequentialStream, DISPID_SVSLastResult,
    SpMMAudioOut, SAFT48kHz8BitMono, SPDKL_CurrentUser,
    ISpeechPhraseReplacements, eLEXTYPE_MORPHOLOGY, DISPID_SGRsCount,
    SVEPhoneme, SpObjectToken, DISPID_SPIGetText,
    DISPID_SDKEnumValues, DISPID_SRSetPropertyNumber,
    ISpResourceManager, SPEI_INTERFERENCE, ISpEventSource,
    eLEXTYPE_PRIVATE18, DISPID_SRCEInterference,
    DISPID_SRCVoicePurgeEvent, SPRST_INACTIVE, SVSFIsFilename,
    DISPID_SWFESamplesPerSec, IInternetSecurityManager,
    ISpObjectToken, SPEI_MIN_SR, SpeechGrammarTagWildcard,
    SpPhoneticAlphabetConverter, DISPID_SVAudioOutput,
    DISPID_SPEActualConfidence, SPWT_PRONUNCIATION, STSF_AppData,
    SVSFPersistXML, DISPID_SGRSTs_NewEnum, DISPID_SGRsItem,
    DISPID_SDKSetBinaryValue, SAFTNoAssignedFormat, SRERequestUI,
    DISPID_SWFEBlockAlign, DISPID_SPIRule,
    DISPID_SRSCurrentStreamNumber, ISpMMSysAudio,
    DISPID_SASCurrentSeekPosition, SVEStartInputStream, SDTAudio,
    SAFT44kHz8BitMono, SpObjectTokenCategory, ISpeechLexiconWords,
    SPBINARYGRAMMAR, DISPID_SFSOpen, eLEXTYPE_RESERVED6,
    DISPID_SPPValue, DISPID_SDKGetStringValue,
    ISpeechPhraseProperties, SPXRO_SML, DISPID_SLWsItem,
    SAFTADPCM_22kHzStereo, ISpeechVoiceStatus, SAFT16kHz8BitMono,
    DISPID_SRRTStreamTime, SWPUnknownWordPronounceable, SP_VISEME_19,
    ISpeechPhraseElement, DISPID_SPIGrammarId, SAFT32kHz8BitMono,
    SPPHRASE, DISPID_SRGId, DISPID_SGRId, SAFTCCITT_uLaw_44kHzStereo,
    SPRECOCONTEXTSTATUS, SVESentenceBoundary, eLEXTYPE_APP,
    SRTExtendableParse, SPEI_RESERVED3, HRESULT,
    SpeechPropertyLowConfidenceThreshold, ISpRecoContext,
    DISPID_SOTMatchesAttributes, DISPID_SGRInitialState,
    SAFT22kHz16BitStereo, SpeechTokenKeyAttributes,
    SPEI_SENTENCE_BOUNDARY, SPEI_PHONEME, SPEI_HYPOTHESIS,
    SDTPronunciation, DISPID_SRRPhraseInfo, SPSNoun,
    DISPID_SVEventInterests, SGRSTTWildcard, DISPID_SMSADeviceId,
    SPWORDPRONUNCIATIONLIST, SDTReplacement, DISPID_SCSBaseStream,
    SpeechCategoryRecoProfiles, SPWF_INPUT,
    DISPID_SPRNumberOfElements, DISPID_SLWsCount, SAFT24kHz16BitMono,
    SVEWordBoundary, DISPID_SVESentenceBoundary, DISPID_SPPChildren,
    DISPID_SOTIsUISupported, SPLO_STATIC, SASRun, DISPID_SGRs_NewEnum,
    _LARGE_INTEGER, SPAO_RETAIN_AUDIO, SRADefaultToActive,
    SPSInterjection, DISPID_SPIProperties, DISPID_SVSkip, SVP_17,
    eWORDTYPE_DELETED, SPAS_PAUSE, STCLocalServer, eLEXTYPE_PRIVATE2,
    DISPID_SDKSetStringValue, SPPS_RESERVED4, SAFT48kHz16BitStereo,
    SAFTCCITT_uLaw_11kHzMono, DISPID_SASNonBlockingIO,
    DISPID_SDKEnumKeys, SGLexical, DISPID_SPRuleChildren,
    DISPID_SPRules_NewEnum, IEnumString, SINoSignal, SPEI_TTS_PRIVATE,
    tagSTATSTG, DISPID_SPRsCount, STCInprocHandler, SPSHT_Unknown,
    SDTRule, DISPID_SVEWord, DISPID_SGRName, eLEXTYPE_PRIVATE8,
    SDKLLocalMachine, SAFTADPCM_44kHzMono, SVSFParseSsml,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, DISPID_SWFEChannels,
    DISPID_SOTDisplayUI, DISPID_SWFEAvgBytesPerSec, DISPID_SGRSTsItem,
    COMMETHOD, DISPID_SGRAttributes, SPWT_DISPLAY, DISPID_SPPsCount,
    DISPID_SLRemovePronunciation, SPWT_LEXICAL,
    DISPID_SRRTOffsetFromStart, SVEAudioLevel,
    DISPID_SPIAudioStreamPosition, SVF_Stressed, SLTUser,
    DISPID_SGRsCommit, DISPID_SAFGuid, DISPID_SWFEFormatTag,
    ISpEventSink, DISPID_SVStatus
)


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'DISPID_SVRate', '__MIDL_IWinTypes_0009', 'ISpVoice',
    'DISPID_SOTGetAttribute', 'SPAS_STOP', 'SpeechInterference',
    'SpMMAudioIn', 'SpeechFormatType', 'DISPID_SLPType', 'SPAS_RUN',
    'SAFTADPCM_8kHzStereo', 'SVSFUnusedFlags', 'DISPID_SPEsCount',
    'DISPIDSPTSI_ActiveLength',
    'SpeechPropertyNormalConfidenceThreshold',
    'SpeechCategoryAudioIn', 'ISpLexicon', 'SPAUDIOSTATE',
    'SGRSTTTextBuffer', 'SREFalseRecognition',
    'SGDSActiveUserDelimited', 'DISPID_SRGState',
    'SAFT12kHz8BitStereo', 'SPVPRI_ALERT', 'eLEXTYPE_PRIVATE17',
    'SVP_13', 'DISPID_SAFGetWaveFormatEx', 'DISPID_SPPConfidence',
    'ISpeechCustomStream', 'SPWORD', 'SRERecognition',
    'DISPID_SRStatus', 'DISPID_SRGRecoContext',
    'SAFTCCITT_ALaw_44kHzStereo', 'DISPID_SRCCreateGrammar',
    'DISPID_SpeechLexicon', 'SPINTERFERENCE_LATENCY_TRUNCATE_END',
    'SASPause', 'DISPID_SPIEnginePrivateData',
    'SpeechTokenIdUserLexicon', 'IStream', 'DISPID_SRState',
    'SPGS_EXCLUSIVE', 'SP_VISEME_10',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS', 'DISPID_SPCPhoneToId',
    'STSF_LocalAppData', 'DISPID_SRCEAdaptation', 'DISPID_SPAsItem',
    'SP_VISEME_20', 'DISPID_SVSLastBookmarkId', 'DISPID_SRIsShared',
    'SAFTGSM610_8kHzMono', 'SPWF_SRENGINE', 'SPWORDTYPE',
    'DISPID_SRCRetainedAudioFormat', 'SVP_1', 'SPSSuppressWord',
    'SpeechVisemeType', 'SAFT11kHz8BitMono', 'ISpeechFileStream',
    'ISpeechXMLRecoResult', 'DISPID_SPRText', 'DISPID_SRAudioInput',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN',
    'SDA_Two_Trailing_Spaces', 'SAFTGSM610_11kHzMono',
    'DISPID_SLWPronunciations', 'DISPID_SRCERecognition',
    'SPINTERFERENCE_NOISE', 'DISPID_SVAlertBoundary',
    'DISPID_SGRSAddRuleTransition', 'SAFTDefault',
    'SPWP_KNOWN_WORD_PRONOUNCEABLE', 'DISPID_SABIBufferSize',
    'DISPID_SpeechMMSysAudio', 'SPPS_Interjection',
    'ISpPhoneConverter', 'DISPID_SpeechLexiconWords',
    'SSSPTRelativeToStart', 'DISPID_SRCBookmark',
    'DISPID_SRRecognizer', 'SPINTERFERENCE_NOSIGNAL',
    'DISPID_SMSSetData', 'DISPID_SpeechRecoContextEvents',
    'DISPID_SRRAudio', 'SPCS_DISABLED', 'ISpeechPhraseReplacement',
    'DISPID_SpeechRecognizer', 'SpInProcRecoContext',
    'ISpRecognizer2', 'DISPID_SPRDisplayAttributes', 'ISpPhraseAlt',
    'SBOPause', 'DISPID_SDKDeleteValue', 'SREPrivate',
    'ISpeechPhraseInfo', 'STSF_FlagCreate', 'SAFT11kHz8BitStereo',
    'eLEXTYPE_PRIVATE11', 'SPINTERFERENCE_TOOLOUD',
    'DISPID_SVIsUISupported', 'SRSEDone', 'DISPID_SGRSTRule',
    'SpeechEngineProperties', 'SPCT_SUB_DICTATION',
    'SECFEmulateResult', 'DISPID_SRCEAudioLevel', 'ISpXMLRecoResult',
    'SVP_21', 'SpeechRecoEvents', 'eLEXTYPE_PRIVATE20',
    'DISPID_SRSSupportedLanguages', 'DISPID_SRGSetTextSelection',
    'DISPID_SPIRetainedSizeBytes', 'DISPID_SGRSRule',
    'DISPID_SPRuleFirstElement', 'DISPID_SGRSTText', 'SPEI_VISEME',
    'SPEI_FALSE_RECOGNITION', 'DISPID_SBSFormat',
    'DISPID_SpeechLexiconProns', 'SVEVoiceChange',
    'DISPID_SpeechRecognizerStatus', 'ISpNotifySink', 'ISpShortcut',
    'SRTStandard', 'SRSInactive', 'DISPID_SASFreeBufferSpace',
    'DISPID_SVStatus', 'ISpeechRecoContext',
    'DISPID_SVGetAudioInputs', 'DISPID_SPIStartTime', 'SVF_None',
    'SDKLCurrentConfig', 'SRTAutopause', 'SECFNoSpecialChars',
    'DISPID_SpeechPhraseBuilder', 'SPEI_MAX_TTS', 'typelib_path',
    'DISPID_SVSVisemeId', 'DISPID_SGRsCommitAndSave',
    'DISPID_SRCESoundStart', 'DISPID_SVDisplayUI',
    'SPFM_OPEN_READWRITE', 'ISpPhrase', 'SECFIgnoreWidth',
    'DISPID_SPAStartElementInResult', 'SPPHRASEELEMENT',
    'SREPropertyStringChange', 'SPDATAKEYLOCATION', 'SVF_Emphasis',
    'DISPID_SVSLastStreamNumberQueued', 'SPSHORTCUTPAIRLIST',
    'DISPID_SRGCmdLoadFromProprietaryGrammar',
    'SAFTCCITT_uLaw_44kHzMono', 'SSFMOpenForRead', 'DISPID_SRCVoice',
    'eLEXTYPE_RESERVED9', 'ISpeechGrammarRule', 'SpeechWordType',
    'SpFileStream', 'DISPID_SVEVoiceChange', 'SLOStatic',
    'SAFTGSM610_22kHzMono', 'SAFTCCITT_uLaw_11kHzStereo',
    'DISPID_SpeechPhraseAlternates', 'DISPID_SVVolume',
    'SPEI_TTS_BOOKMARK', 'SAFT32kHz16BitStereo', 'SPSModifier',
    'SAFT12kHz8BitMono', 'SP_VISEME_18',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet', 'DISPID_SRCPause',
    'eLEXTYPE_PRIVATE15', 'SVSFIsNotXML', 'DISPID_SPRulesItem',
    'SPGRAMMARWORDTYPE', 'ISpPhoneticAlphabetConverter', 'SPAR_High',
    'DISPID_SpeechAudio', 'ISpSerializeState',
    'SpeechGrammarRuleStateTransitionType', 'SPPS_RESERVED1',
    'DISPID_SGRSTPropertyValue', 'SPVOICESTATUS', '_RemotableHandle',
    'SpeechDisplayAttributes', 'SPEI_RECO_STATE_CHANGE',
    'DISPID_SRSAudioStatus', 'SVSFParseSapi', 'SPEI_MAX_SR',
    'SPRST_ACTIVE_ALWAYS', 'SpeechMicTraining', 'DISPID_SVPause',
    'SPRS_INACTIVE', 'Speech_StreamPos_Asap', 'SECLowConfidence',
    'eLEXTYPE_PRIVATE10', 'SPBO_NONE', 'SPEI_RECO_OTHER_CONTEXT',
    'tagSPTEXTSELECTIONINFO', 'SVSFNLPSpeakPunc', 'SpShortcut',
    'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE', 'SAFT44kHz16BitMono',
    'SLTApp', 'SPFM_CREATE', 'SPCONTEXTSTATE', 'SpeechTokenKeyUI',
    'SAFTADPCM_11kHzStereo', 'ISpeechMMSysAudio',
    'DISPID_SLGenerationId', 'SPSHT_OTHER', 'DISPID_SOTsItem',
    'SPPARTOFSPEECH', 'SGDSActive', 'DISPID_SPCIdToPhone',
    'SPEI_RESERVED2', 'DISPID_SPAPhraseInfo', 'ISpeechGrammarRules',
    'DISPID_SpeechLexiconWord', 'SPRST_NUM_STATES',
    'SAFT11kHz16BitMono', 'SPGS_ENABLED', 'SVSFNLPMask',
    'WAVEFORMATEX', 'SpMemoryStream', 'SpNullPhoneConverter',
    'eLEXTYPE_PRIVATE3', 'ISpeechAudioStatus', 'DISPID_SRGReset',
    'SAFT32kHz8BitStereo', 'DISPID_SPRuleConfidence',
    'SAFTCCITT_ALaw_44kHzMono', 'DISPID_SOTDataKey', 'SRADynamic',
    'SPRULE', 'ISpeechDataKey', 'SAFTCCITT_uLaw_22kHzStereo',
    'DISPID_SRCState', 'ISpeechGrammarRuleStateTransitions',
    'SPSMF_SAPI_PROPERTIES', 'DISPID_SRSetPropertyString',
    'STSF_CommonAppData', 'SREAllEvents',
    'DISPID_SpeechLexiconPronunciation',
    'DISPID_SOTRemoveStorageFileName', 'ISpeechAudioFormat',
    'SpInprocRecognizer', 'SPDKL_CurrentConfig',
    'SPEI_START_INPUT_STREAM', 'SPVPRI_OVER', 'SPSHT_EMAIL',
    '__MIDL___MIDL_itf_sapi_0000_0020_0001', 'ISpeechPhraseAlternate',
    'DISPID_SLWWord', 'SPEVENT', 'SDTAll', 'SVSFPurgeBeforeSpeak',
    'DISPID_SpeechPhraseReplacements', 'SPXRO_Alternates_SML',
    'SPEI_RECOGNITION', 'SpeechRuleState', 'SAFTADPCM_44kHzStereo',
    'ISpRecoGrammar2', 'SpeechPropertyResourceUsage',
    'eLEXTYPE_PRIVATE4', 'SPSEMANTICFORMAT', 'DISPID_SBSRead',
    'DISPID_SRAudioInputStream', 'SRSEIsSpeaking',
    'DISPID_SMSGetData', 'DISPID_SPARecoResult', 'SPSHORTCUTTYPE',
    'SP_VISEME_11', 'SP_VISEME_16', 'SPAR_Unknown',
    'DISPID_SRRTTickCount', 'ISpeechPhraseRules',
    'DISPID_SRGCmdLoadFromObject', 'SpNotifyTranslator',
    'SpeechAudioFormatGUIDWave',
    'DISPID_SLAddPronunciationByPhoneIds',
    'DISPID_SPEAudioStreamOffset', 'SGRSTTDictation',
    'DISPID_SMSAMMHandle', 'DISPID_SRCEEndStream',
    'DISPID_SRRGetXMLErrorInfo', 'DISPID_SPACommit', 'SpStream',
    'SpMMAudioOut', 'DISPID_SPEsItem', 'DISPID_SREmulateRecognition',
    'ISpeechAudio', 'DISPID_SGRSTsCount', 'SECHighConfidence',
    'IEnumSpObjectTokens', 'DISPID_SRGDictationSetState',
    'ISpNotifySource', 'SPRST_ACTIVE', 'DISPID_SVSpeak',
    'DISPID_SpeechXMLRecoResult', 'SAFTGSM610_44kHzMono',
    'SPBO_AHEAD', 'DISPID_SASState', 'STCRemoteServer',
    'SPAUDIOOPTIONS', 'DISPID_SRRSaveToMemory', 'DISPID_SOTRemove',
    'SVSFParseMask', 'DISPID_SRGDictationLoad',
    'SDA_Consume_Leading_Spaces', 'SVP_8', 'DISPID_SPPName',
    'SPFM_NUM_MODES', 'SAFT24kHz8BitStereo', 'SpeechAllElements',
    'DISPID_SVGetVoices', 'SREAdaptation', 'SAFT16kHz16BitMono',
    'DISPID_SPRFirstElement', 'DISPID_SGRAddResource', 'SpVoice',
    'ISpPhoneticAlphabetSelection', 'DISPID_SRCEEnginePrivate',
    'Speech_StreamPos_RealTime', 'SpeechLoadOption',
    'DISPID_SDKGetBinaryValue', 'SPSHT_NotOverriden',
    'SAFTCCITT_uLaw_8kHzMono', 'SPCATEGORYTYPE',
    'SpeechPropertyHighConfidenceThreshold', 'SDA_No_Trailing_Space',
    'SP_VISEME_14', 'DISPID_SABufferNotifySize',
    'ISpeechMemoryStream', 'DISPID_SpeechGrammarRuleState',
    'SP_VISEME_5', 'ISpeechPhoneConverter', 'SPEI_TTS_AUDIO_LEVEL',
    'ISpeechPhraseAlternates', 'SWTAdded', 'DISPID_SPPId',
    'SpeechRegistryUserRoot', 'ISpeechBaseStream', 'SGRSTTEpsilon',
    'SpeechAudioState', 'SpMMAudioEnum', 'SSFMCreateForWrite',
    'SPSUnknown', 'ISpProperties', 'DISPID_SPISaveToMemory',
    'DISPID_SPPs_NewEnum', 'ISpeechPhraseRule', 'DISPID_SLPs_NewEnum',
    'ISpNotifyTranslator', 'SWTDeleted', 'SPPHRASERULE',
    'SpeechWordPronounceable', 'SAFTADPCM_8kHzMono', 'ISpRecognizer3',
    'DISPID_SRGCmdSetRuleIdState', 'ISpeechAudioBufferInfo',
    'SITooLoud', 'SPINTERFERENCE_TOOFAST', 'SREPropertyNumChange',
    'DISPIDSPRG', 'DISPID_SpeechVoiceStatus', 'UINT_PTR', 'SVPAlert',
    'DISPID_SOTCreateInstance', 'SPINTERFERENCE_TOOQUIET',
    'DISPID_SRGSetWordSequenceData', 'DISPID_SPEAudioSizeTime',
    'SAFT8kHz16BitMono', 'SPWAVEFORMATTYPE', 'eLEXTYPE_PRIVATE9',
    'DISPID_SPIEngineId', 'DISPID_SPPNumberOfElements',
    'SpeechCategoryVoices', 'DISPID_SRCEBookmark',
    'eLEXTYPE_PRIVATE7', 'DISPIDSPTSI_ActiveOffset', 'SSFMCreate',
    'eLEXTYPE_PRIVATE12', 'ISpeechRecoResultDispatch',
    'SDKLDefaultLocation', 'SVSFVoiceMask', 'SECFIgnoreKanaType',
    'SINone', 'DISPID_SOTCId', 'SP_VISEME_2', 'SAFT8kHz16BitStereo',
    'DISPID_SLPsItem', 'SpeechRetainedAudioOptions',
    'eLEXTYPE_PRIVATE13', 'DISPID_SVVoice', 'SAFT11kHz16BitStereo',
    'SSSPTRelativeToCurrentPosition', 'ISpGrammarBuilder',
    'SPWORDPRONUNCIATION', 'SVP_0', 'DISPID_SVEStreamStart',
    'DISPID_SVSPhonemeId', 'DISPID_SPRsItem',
    'DISPID_SpeechPhraseInfo', 'SP_VISEME_7', 'SPFM_CREATE_ALWAYS',
    'SPEI_SR_AUDIO_LEVEL', 'eWORDTYPE_ADDED', 'SP_VISEME_12',
    'SpeechPropertyAdaptationOn', 'SRTReSent', 'SpWaveFormatEx',
    'ISpeechRecoResult2', 'SVP_19', 'SP_VISEME_0',
    'Speech_Max_Word_Length', 'SVP_4',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'SAFTNonStandardFormat',
    'SAFT44kHz16BitStereo', 'SPBO_TIME_UNITS',
    'DISPID_SVSInputWordLength', 'SVP_2', 'ISpAudio',
    'SECNormalConfidence', 'SPRST_INACTIVE_WITH_PURGE',
    'SREPhraseStart', 'SPPS_SuppressWord',
    'DISPID_SVSpeakCompleteEvent', 'SITooSlow',
    'SAFTCCITT_ALaw_22kHzStereo', 'SPWP_UNKNOWN_WORD_PRONOUNCEABLE',
    'ISpeechRecognizer', 'DISPID_SRGCommit', 'SPPS_LMA',
    'DISPID_SOTCEnumerateTokens',
    'DISPID_SRCERecognitionForOtherContext', 'DISPID_SOTCategory',
    'DISPID_SRRTimes', 'DISPID_SpeechWaveFormatEx',
    'SAFT24kHz8BitMono', 'DISPID_SDKCreateKey', 'SpAudioFormat',
    'SpeechTokenValueCLSID', 'SVPOver', 'SP_VISEME_13',
    'DISPID_SAFType', 'ISpRecoGrammar', 'SpLexicon',
    'DISPID_SWFEExtraData', 'SP_VISEME_21', 'SRARoot',
    'eLEXTYPE_RESERVED4', 'DISPID_SRCEPropertyStringChange',
    'SP_VISEME_9', 'SpeechVoicePriority', 'SPEI_END_INPUT_STREAM',
    'ISpeechPhraseInfoBuilder', 'DISPID_SLWLangId',
    'DISPID_SVSRunningState', 'ISpDataKey', 'SpResourceManager',
    'SpeechCategoryAppLexicons', 'DISPID_SPAsCount',
    'DISPID_SpeechAudioStatus', 'eLEXTYPE_VENDORLEXICON',
    'DISPID_SRCSetAdaptationData', 'DISPID_SPEDisplayAttributes',
    'SRAORetainAudio', 'SVEBookmark', 'DISPID_SpeechObjectToken',
    'SPSMF_SRGS_SAPIPROPERTIES', 'SRERecoOtherContext',
    'DISPID_SMSALineId', 'DISPID_SRIsUISupported',
    'SAFT48kHz8BitStereo', 'ISpeechObjectTokens', 'SGRSTTWord',
    'SRSInactiveWithPurge', 'SAFTADPCM_11kHzMono', 'DISPID_SLWType',
    'SpeechGrammarTagUnlimitedDictation', 'SPCT_SUB_COMMAND',
    'SPINTERFERENCE_NONE', 'SWPKnownWordPronounceable',
    'SAFT16kHz8BitStereo', 'SPEI_RESERVED5',
    'DISPID_SVAudioOutputStream', 'SVP_16', 'SPSERIALIZEDPHRASE',
    'SITooFast', 'DISPID_SPILanguageId',
    'SpeechCategoryPhoneConverters', 'ISpRecoCategory',
    'SPEI_SR_PRIVATE', 'SVP_6', 'SGSEnabled',
    'DISPIDSPTSI_SelectionOffset', 'DISPID_SRCRequestedUIType',
    'SpeechTokenContext', 'SpeechAudioFormatType',
    'DISPID_SADefaultFormat', 'DISPID_SPIAudioSizeTime',
    'SVSFlagsAsync', 'SAFT22kHz8BitMono', 'SpCustomStream',
    'DISPID_SVGetAudioOutputs', 'DISPID_SGRsDynamic',
    'DISPID_SPEDisplayText', 'SDTDisplayText',
    'DISPID_SPRuleNumberOfElements', 'SRAInterpreter',
    'SAFTCCITT_ALaw_22kHzMono', 'SSTTDictation', 'SPAR_Medium',
    'SAFTCCITT_ALaw_11kHzStereo', 'DISPID_SASetState',
    'SAFT48kHz16BitMono', 'ISpeechLexicon', 'DISPID_SABufferInfo',
    'SPEI_PHRASE_START', 'SPPS_RESERVED3',
    'DISPID_SPPEngineConfidence', 'ISpRecoResult', 'SPVISEMES',
    'SPEI_ACTIVE_CATEGORY_CHANGED', 'SPCT_SLEEP',
    'DISPID_SRSNumberOfActiveRules', 'DISPID_SpeechBaseStream',
    'STCAll', 'SPEI_PROPERTY_NUM_CHANGE', 'SpeechGrammarState',
    'SPPS_Verb', 'SREStreamStart', 'SPPS_NotOverriden',
    'DISPID_SRCEventInterests', 'SVP_14', 'SRAImport',
    'SSFMOpenReadWrite', 'DISPID_SPIReplacements',
    'SpeechDataKeyLocation', 'DISPID_SPPsItem',
    'SpeechVoiceSpeakFlags', 'SPRECOSTATE', 'DISPID_SDKOpenKey',
    'SAFT12kHz16BitStereo', 'SpeechRecognitionType',
    'DISPID_SPRs_NewEnum', 'eLEXTYPE_PRIVATE14', 'SP_VISEME_15',
    'eLEXTYPE_USER', 'DISPID_SVEViseme', 'DISPID_SVPriority',
    'DISPID_SPEEngineConfidence', 'DISPID_SPEs_NewEnum',
    'DISPID_SRCERequestUI', 'DISPID_SRSClsidEngine',
    'DISPID_SpeechPhraseAlternate', 'DISPID_SRCEPropertyNumberChange',
    'SVEViseme', 'DISPID_SpeechPhraseRule', 'SVEAllEvents',
    'ISpeechLexiconWord', 'DISPID_SVEStreamEnd',
    'SAFTCCITT_uLaw_8kHzStereo', 'DISPID_SRCreateRecoContext',
    'eLEXTYPE_PRIVATE1', 'SRESoundStart', 'DISPID_SPRuleId',
    'DISPID_SRCRetainedAudio', 'SPBO_PAUSE',
    'DISPID_SRGCmdLoadFromResource', 'DISPID_SGRSTPropertyId',
    'SVP_9', 'DISPID_SRCRecognizer', 'DISPID_SBSWrite',
    'DISPID_SRSCurrentStreamPosition', 'SpPhoneConverter',
    'DISPID_SPEAudioSizeBytes', 'SINoise', 'eLEXTYPE_PRIVATE19',
    'DISPID_SPPParent', 'SPEI_PROPERTY_STRING_CHANGE',
    'SpeechGrammarTagDictation', 'DISPID_SPEAudioTimeOffset',
    'SPTEXTSELECTIONINFO', 'DISPID_SRRAlternates',
    'DISPID_SpeechPhraseReplacement', 'SPPROPERTYINFO', 'SVSFIsXML',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'DISPID_SVResume', 'DISPID_SGRsFindRule',
    'ISpeechTextSelectionInformation',
    'DISPID_SGRSAddSpecialTransition', 'DISPID_SGRSTType', 'SVP_18',
    'SpeechVoiceSkipTypeSentence', 'eLEXTYPE_PRIVATE16',
    'DISPID_SRCAudioInInterferenceStatus',
    'DISPID_SGRSAddWordTransition', 'ISpRecoContext2', 'SPPS_Unknown',
    'SPEI_VOICE_CHANGE', 'ISpeechRecoGrammar',
    'SpeechVoiceCategoryTTSRate', 'SPEI_WORD_BOUNDARY',
    'DISPID_SBSSeek', 'SRSActiveAlways',
    'DISPID_SOTGetStorageFileName', 'SpeechPartOfSpeech',
    'DISPID_SRGetPropertyString', 'SAFTCCITT_ALaw_11kHzMono',
    'DISPID_SRGetFormat', 'ISpeechObjectToken', 'SPCS_ENABLED',
    'SPCT_COMMAND', 'SpUnCompressedLexicon', 'SPPS_RESERVED2',
    'eLEXTYPE_RESERVED8', 'ISpeechRecoResultTimes',
    'DISPID_SVSCurrentStreamNumber',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'DISPID_SRCERecognizerStateChange', 'SPEVENTSOURCEINFO',
    'DISPID_SDKDeleteKey', 'SGSDisabled', 'DISPID_SRCEPhraseStart',
    'DISPID_SPERetainedSizeBytes', 'SPEI_SR_RETAINEDAUDIO',
    'SVEPrivate', 'SpeechEmulationCompareFlags', 'SAFT8kHz8BitMono',
    'SDTLexicalForm', 'SpeechDiscardType', 'DISPID_SPIElements',
    'SRAONone', 'SPEI_UNDEFINED', 'DISPID_SpeechMemoryStream',
    'DISPID_SVSInputWordPosition', 'SPADAPTATIONRELEVANCE',
    'IInternetSecurityMgrSite', 'ISpeechRecoResult',
    'DISPID_SpeechPhraseRules', 'eLEXTYPE_PRIVATE5',
    'DISPID_SWFEBitsPerSample', 'DISPID_SGRClear', 'SPSHORTCUTPAIR',
    'DISPID_SGRSTWeight', 'SREInterference', 'ISpeechPhraseElements',
    'DISPID_SLRemovePronunciationByPhoneIds', 'SRTSMLTimeout',
    'ISpeechResourceLoader', 'DISPID_SOTGetDescription',
    'DISPID_SGRSTransitions', 'DISPID_SPRuleParent', 'SPSVerb',
    'DISPID_SRGIsPronounceable', 'SpeechCategoryAudioOut',
    'SpeechPropertyResponseSpeed', 'SpeechBookmarkOptions',
    'DISPID_SRGRules', 'DISPID_SOTCSetId', 'SVP_7',
    'Speech_Default_Weight', 'DISPID_SLPPartOfSpeech', 'SECFDefault',
    'SPLEXICONTYPE', 'SPSNotOverriden', 'SVP_5', 'SPEI_ADAPTATION',
    'DISPID_SRRRecoContext', 'SVP_3', 'ISpStream', 'SPEI_MIN_TTS',
    'DISPID_SVSLastBookmark', 'SREAudioLevel',
    'DISPID_SRCCreateResultFromMemory',
    'DISPID_SPANumberOfElementsInResult', 'DISPID_SAEventHandle',
    'SASStop', 'ISpeechPhraseProperty', 'SP_VISEME_8',
    'ISpeechRecognizerStatus', 'DISPID_SpeechGrammarRule',
    '_ISpeechVoiceEvents', 'DISPID_SRRTLength',
    'DISPID_SRRSetTextFeedback', 'DISPID_SPRuleName', 'ISpRecognizer',
    'SRESoundEnd', 'SpeechTokenKeyFiles',
    'DISPID_SABIMinNotification', 'SpPhraseInfoBuilder',
    'DISPID_SLGetGenerationChange', 'SAFT22kHz8BitStereo',
    'SAFTCCITT_ALaw_8kHzMono', 'ISpeechWaveFormatEx',
    'DISPID_SPIAudioSizeBytes', 'DISPID_SPPFirstElement',
    'tagSPPROPERTYINFO', 'eLEXTYPE_RESERVED7', 'LONG_PTR',
    'DISPID_SpeechObjectTokenCategory', 'SGPronounciation',
    'ISpStreamFormatConverter', 'DISPID_SOTId', 'SPEI_SR_BOOKMARK',
    'SAFTCCITT_ALaw_8kHzStereo', 'SPPS_Noun',
    'DISPID_SVSInputSentenceLength', 'DISPID_SPAs_NewEnum',
    'SpeechAudioVolume', 'SpeechPropertyComplexResponseSpeed',
    'SPSMF_UPS', 'SPSLMA', 'SpeechAudioFormatGUIDText',
    'SpeechRecoProfileProperties', 'SGDSInactive',
    'DISPID_SGRSTPropertyName', 'SVSFParseAutodetect', 'SPAO_NONE',
    'DISPID_SRGDictationUnload', 'SAFT24kHz16BitStereo',
    'DISPID_SLGetPronunciations', 'DISPID_SRGetRecognizers',
    'SRSActive', 'SPRS_ACTIVE_USER_DELIMITED',
    'DISPID_SpeechObjectTokens', 'SWPUnknownWordUnpronounceable',
    'DISPID_SVSpeakStream', 'DISPID_SVSyncronousSpeakTimeout',
    'DISPID_SVWaitUntilDone', 'SGRSTTRule', 'DISPID_SOTSetId',
    'ISpeechGrammarRuleState', 'SAFTTrueSpeech_8kHz1BitMono',
    'SPLO_DYNAMIC', 'SpeechEngineConfidence',
    'SpeechDictationTopicSpelling', 'DISPID_SDKSetLongValue',
    'DISPID_SpeechGrammarRuleStateTransitions',
    'DISPID_SpeechFileStream', 'SDA_One_Trailing_Space',
    'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'DISPID_SPEPronunciation',
    'DISPID_SpeechGrammarRules', 'DISPID_SRGCmdLoadFromFile',
    'SREStateChange', 'DISPID_SAVolume', '_ISpeechRecoContextEvents',
    'SPVPRI_NORMAL', 'SVP_12', 'SPPS_Noncontent', 'SP_VISEME_17',
    'ISpeechVoice', 'DISPID_SRCResume', 'DISPID_SLWs_NewEnum',
    'SPINTERFERENCE_TOOSLOW', 'SSTTTextBuffer', 'SPAR_Low',
    'ISpeechObjectTokenCategory', 'SPEI_SOUND_END',
    'DISPID_SLPLangId', 'SpeechRunState', 'SAFT32kHz16BitMono',
    'SP_VISEME_1', 'DISPID_SPRulesCount', 'SPAUDIOSTATUS',
    'DISPID_SPERetainedStreamOffset', 'SPFM_OPEN_READONLY',
    'DISPID_SVSLastResult', 'SpeechRuleAttributes',
    'SAFT48kHz8BitMono', 'SFTSREngine', 'DISPID_SpeechVoice',
    'ISpeechPhraseReplacements', 'DISPIDSPTSI_SelectionLength',
    'eLEXTYPE_MORPHOLOGY', 'DISPID_SGRsCount', 'SVEPhoneme',
    'DISPID_SRRGetXMLResult', 'SPDKL_CurrentUser',
    'DISPID_SPIGetText', 'SpObjectToken', 'DISPID_SDKEnumValues',
    'DISPID_SRSetPropertyNumber', 'ISpResourceManager',
    'SPINTERFERENCE_LATENCY_WARNING', 'SPEI_INTERFERENCE',
    'ISpEventSource', 'DISPID_SVEEnginePrivate', 'eLEXTYPE_PRIVATE18',
    'SpeechCategoryRecognizers', 'DISPID_SRCVoicePurgeEvent',
    'SFTInput', 'DISPID_SRCEInterference', 'SPINTERFERENCE',
    'SVSFIsFilename', 'SpeechGrammarWordType', 'DISPID_SRDisplayUI',
    'SPRECOGNIZERSTATUS', 'SPEI_SOUND_START', 'SPRST_INACTIVE',
    'DISPID_SWFESamplesPerSec', 'IInternetSecurityManager',
    'SAFTCCITT_uLaw_22kHzMono', 'DISPID_SVSInputSentencePosition',
    'DISPID_SpeechPhraseElement', 'ISpObjectToken', 'SPPS_Modifier',
    'DISPID_SLPSymbolic', 'SPEI_MIN_SR', 'SpeechGrammarTagWildcard',
    'SpPhoneticAlphabetConverter', 'SpeechVoiceEvents', 'SPWORDLIST',
    'DISPID_SVAudioOutput', 'SAFT44kHz8BitStereo',
    'DISPID_SPEActualConfidence', 'DISPID_SRCESoundEnd',
    'SPSERIALIZEDRESULT', 'SVEEndInputStream', 'SPWT_PRONUNCIATION',
    'SREHypothesis', 'ISpObjectWithToken', 'Library',
    'eLEXTYPE_RESERVED10', 'SGDisplay', 'SVP_15', 'STCInprocServer',
    'STSF_AppData', 'DISPID_SLGetWords', 'SVSFPersistXML',
    'DISPID_SGRSTs_NewEnum', 'DISPID_SGRsItem',
    'DISPID_SPIGetDisplayAttributes', 'DISPID_SVEBookmark',
    'DISPID_SDKSetBinaryValue', 'SAFTNoAssignedFormat', 'SP_VISEME_6',
    'SRERequestUI', 'DISPID_SpeechPhraseProperties',
    'DISPID_SWFEBlockAlign', 'DISPID_SVEAudioLevel',
    'DISPID_SRSCurrentStreamNumber', 'DISPID_SVGetProfiles',
    'DISPID_SPIRule', '_SPAUDIOSTATE', 'ISpMMSysAudio',
    'SPEI_START_SR_STREAM', 'DISPID_SASCurrentSeekPosition',
    'SVEStartInputStream', 'SDTAudio', 'SAFT44kHz8BitMono',
    'SpObjectTokenCategory', 'SPEI_END_SR_STREAM',
    'SPSTREAMFORMATTYPE', 'ISpeechLexiconWords',
    'ISpeechGrammarRuleStateTransition', 'SSTTWildcard',
    'DISPID_SPPBRestorePhraseFromMemory', 'eLEXTYPE_LETTERTOSOUND',
    'SPPHRASEPROPERTY', 'DISPID_SPRuleEngineConfidence',
    'DISPID_SRRAudioFormat', 'SPBINARYGRAMMAR', 'DISPID_SFSOpen',
    'SRAExport', 'SPCT_DICTATION', 'eLEXTYPE_RESERVED6',
    'DISPID_SPPValue', 'DISPID_SDKGetStringValue',
    'ISpeechPhraseProperties', 'DISPID_SRRDiscardResultInfo',
    'SRCS_Disabled', 'SPXRO_SML', 'DISPID_SLWsItem',
    'SAFTADPCM_22kHzStereo', 'SAFT8kHz8BitStereo',
    'ISpeechVoiceStatus', 'DISPID_SOTsCount',
    'ISpObjectTokenCategory', 'SpeechRecoContextState',
    'DISPID_SRRTStreamTime', 'DISPID_SVEPhoneme', 'SPRS_ACTIVE',
    'SWPUnknownWordPronounceable',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'SREStreamEnd',
    'ISpeechLexiconPronunciations', 'SP_VISEME_4', 'SP_VISEME_19',
    'ISpeechPhraseElement', 'DISPID_SLPPhoneIds',
    'DISPID_SPIGrammarId', 'SpeechUserTraining', 'SAFT32kHz8BitMono',
    'SpTextSelectionInformation', 'SPPHRASE', 'DISPID_SRGId',
    'DISPID_SPCLangId', 'SPEI_RESERVED1', 'SpeechAddRemoveWord',
    'DISPID_SGRId', 'SVSFDefault', 'SAFTCCITT_uLaw_44kHzStereo',
    'SPRECOCONTEXTSTATUS', 'SVESentenceBoundary', 'eLEXTYPE_APP',
    'DISPID_SFSClose', 'SASClosed', 'SpeechTokenShellFolder',
    'SRTExtendableParse', 'SPEI_REQUEST_UI', 'SPEI_RESERVED3',
    'SpeechPropertyLowConfidenceThreshold', 'DISPID_SRCEHypothesis',
    'ISpRecoContext', 'DISPID_SOTMatchesAttributes',
    'SpeechSpecialTransitionType', 'SPRULESTATE',
    'DISPID_SABIEventBias', 'DISPID_SGRInitialState', 'SVP_10',
    'SPRECORESULTTIMES', 'SAFT22kHz16BitMono', 'SAFT22kHz16BitStereo',
    'SpeechTokenKeyAttributes', 'DISPID_SGRsAdd',
    'SPEI_SENTENCE_BOUNDARY', 'SPEI_PHONEME', 'SGSExclusive',
    'DISPID_SpeechCustomStream', 'SPEI_HYPOTHESIS',
    'SDTPronunciation', 'DISPID_SRGCmdSetRuleState', 'SPGS_DISABLED',
    'eLEXTYPE_PRIVATE6', 'SPAS_CLOSED', 'DISPID_SRRPhraseInfo',
    'DISPID_SRRSpeakAudio', 'SpeechAudioProperties',
    'SpeechRecognizerState', 'SPSNoun', 'SpSharedRecognizer',
    'SpCompressedLexicon', 'DISPID_SVEventInterests',
    'SGRSTTWildcard', 'DISPID_SMSADeviceId',
    'SPWORDPRONUNCIATIONLIST', 'SDTReplacement',
    'DISPID_SCSBaseStream', 'DISPID_SRCEStartStream',
    'SpeechCategoryRecoProfiles', 'SPBOOKMARKOPTIONS', 'SPWF_INPUT',
    'DISPID_SPRNumberOfElements', 'SAFTADPCM_22kHzMono',
    'DISPID_SLWsCount', 'DISPID_SDKGetlongValue',
    'SAFT24kHz16BitMono', 'SVP_20', 'SVEWordBoundary',
    'DISPID_SVESentenceBoundary', 'SAFTText', 'SpSharedRecoContext',
    'DISPID_SPELexicalForm', 'SPDKL_DefaultLocation',
    'SpStreamFormatConverter', 'DISPID_SPPChildren', 'SITooQuiet',
    'DISPID_SOTIsUISupported', 'SPLO_STATIC',
    'DISPID_SpeechPhraseProperty', 'SASRun',
    'DISPID_SRGCmdLoadFromMemory', 'DISPID_SGRs_NewEnum',
    'DISPID_SGRSTNextState', 'SpeechStreamFileMode',
    'SDKLCurrentUser', 'eLEXTYPE_USER_SHORTCUT', 'SPAO_RETAIN_AUDIO',
    'ISpeechLexiconPronunciation', 'SECFIgnoreCase',
    'SRADefaultToActive', 'SPSInterjection',
    'DISPID_SpeechRecoResultTimes', 'DISPID_SRCCmdMaxAlternates',
    'SGLexicalNoSpecialChars', 'DISPID_SPIProperties',
    'SPAUDIOBUFFERINFO', 'DISPID_SVSkip',
    'DISPID_SPERequiredConfidence', 'SPVPRIORITY', 'SVP_17',
    'eWORDTYPE_DELETED', 'SPAS_PAUSE', 'SPGRAMMARSTATE',
    'SPWORDPRONOUNCEABLE', 'STCLocalServer', 'eLEXTYPE_PRIVATE2',
    'DISPID_SpeechPhraseElements', 'DISPID_SLPsCount', 'DISPIDSPTSI',
    'DISPID_SDKSetStringValue', 'SRTEmulated', 'SPPS_RESERVED4',
    'SDTAlternates', 'SAFT48kHz16BitStereo', 'SBONone', 'SVPNormal',
    'SAFTCCITT_uLaw_11kHzMono', 'DISPID_SpeechVoiceEvent',
    'SPPHRASEREPLACEMENT', 'DISPID_SASNonBlockingIO',
    'DISPID_SLAddPronunciation', 'SGLexical', 'SPEI_RESERVED6',
    'SpeechVisemeFeature', 'DISPID_SDKEnumKeys',
    'DISPID_SPRuleChildren', 'DISPID_SPRules_NewEnum', 'IEnumString',
    'SINoSignal', 'DISPID_SASCurrentDevicePosition',
    'SPEI_TTS_PRIVATE', 'tagSTATSTG', 'DISPID_SPRsCount',
    'STCInprocHandler', 'SPSHT_Unknown', 'SDTRule',
    'SAFT16kHz16BitStereo', 'DISPID_SVEWord', 'eLEXTYPE_PRIVATE8',
    'DISPID_SOTCDefault', 'DISPID_SGRName', 'Speech_Max_Pron_Length',
    'SAFT12kHz16BitMono', 'SVP_11', 'SDKLLocalMachine',
    'DISPID_SpeechPhoneConverter', 'SAFTADPCM_44kHzMono',
    'SVSFParseSsml', 'SREBookmark', 'DISPID_SpeechAudioBufferInfo',
    'DISPID_SRProfile', 'DISPID_SRGetPropertyNumber',
    'SpeechRegistryLocalMachineRoot', 'SpeechLexiconType',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'DISPID_SWFEChannels',
    'SPSEMANTICERRORINFO', 'SPFILEMODE', 'DISPID_SWFEAvgBytesPerSec',
    'DISPID_SGRSTsItem', 'DISPID_SOTDisplayUI',
    'DISPID_SGRAttributes', 'DISPID_SpeechRecoResult2',
    'SPXMLRESULTOPTIONS', 'SDTProperty', 'SSSPTRelativeToEnd',
    'SP_VISEME_3', 'ISpStreamFormat', 'SPWT_DISPLAY',
    'DISPID_SRCEFalseRecognition',
    'DISPID_SpeechGrammarRuleStateTransition', 'DISPID_SPPsCount',
    'DISPID_SLRemovePronunciation', 'SPDKL_LocalMachine',
    'SpeechStreamSeekPositionType', 'SPWT_LEXICAL',
    'DISPID_SRRTOffsetFromStart', 'SVEAudioLevel', 'SPSFunction',
    'DISPID_SOTs_NewEnum', 'DISPID_SGRAddState', 'SPPS_Function',
    'DISPID_SPIAudioStreamPosition', 'SAFTExtendedAudioFormat',
    'SPEVENTENUM', 'DISPID_SAFSetWaveFormatEx', 'SLODynamic',
    'SVF_Stressed', 'SGDSActiveWithAutoPause',
    'DISPID_SpeechAudioFormat', 'SRCS_Enabled', 'SLTUser',
    'DISPID_SAStatus', 'DISPID_SGRsCommit', 'DISPID_SAFGuid',
    'DISPID_SpeechDataKey', 'DISPID_SpeechRecoContext', 'SRATopLevel',
    'SPLOADOPTIONS', 'DISPID_SWFEFormatTag',
    'DISPID_SpeechRecoResult', 'ISpEventSink',
    'DISPID_SOTCGetDataKey', 'SAFT16kHz8BitMono'
]

