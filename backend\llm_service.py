"""
LLM Service for Agent Lee™ System
Handles integration with Google Gemini and local models
"""

import os
import logging
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime
import google.generativeai as genai
import requests
from dotenv import load_dotenv

# Load environment variables if not already loaded
try:
    load_dotenv()
except Exception:
    pass  # Environment variables may already be loaded

logger = logging.getLogger("agentlee.llm")

class LLMService:
    """Service for handling LLM interactions"""
    
    def __init__(self):
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.ollama_model = os.getenv("OLLAMA_MODEL", "llama2")
        
        # Initialize Gemini if API key is available
        self.gemini_available = False
        if self.gemini_api_key:
            try:
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                self.gemini_available = True
                logger.info("Gemini API initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Gemini API: {e}")
        
        # Check Ollama availability
        self.ollama_available = self._check_ollama_availability()
    
    def _check_ollama_availability(self) -> bool:
        """Check if Ollama is available"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.warning(f"Ollama not available: {e}")
            return False
    
    async def generate_response(
        self, 
        prompt: str, 
        model: str = "auto",
        context: Optional[Dict[str, Any]] = None,
        max_tokens: int = 1000
    ) -> Dict[str, Any]:
        """
        Generate a response using the specified model
        
        Args:
            prompt: The input prompt
            model: Model to use ("gemini", "ollama", "auto")
            context: Additional context for the conversation
            max_tokens: Maximum tokens in response
            
        Returns:
            Dict containing response, model used, confidence, etc.
        """
        start_time = datetime.now()
        
        # Auto-select model if not specified
        if model == "auto":
            if self.gemini_available:
                model = "gemini"
            elif self.ollama_available:
                model = "ollama"
            else:
                return self._get_fallback_response(prompt)
        
        try:
            if model.startswith("gemini") and self.gemini_available:
                return await self._generate_gemini_response(prompt, context, max_tokens, start_time)
            elif model.startswith("ollama") and self.ollama_available:
                return await self._generate_ollama_response(prompt, context, max_tokens, start_time)
            else:
                logger.warning(f"Requested model '{model}' not available, using fallback")
                return self._get_fallback_response(prompt)
                
        except Exception as e:
            logger.error(f"LLM generation error: {e}")
            return self._get_fallback_response(prompt, error=str(e))
    
    async def _generate_gemini_response(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]], 
        max_tokens: int,
        start_time: datetime
    ) -> Dict[str, Any]:
        """Generate response using Gemini API"""
        try:
            # Enhance prompt with context if provided
            enhanced_prompt = self._enhance_prompt_with_context(prompt, context)
            
            # Generate response
            response = await asyncio.to_thread(
                self.gemini_model.generate_content, 
                enhanced_prompt
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "status": "success",
                "prompt": prompt,
                "response": response.text,
                "model": "gemini-1.5-flash",
                "confidence": 0.95,  # Gemini typically has high confidence
                "processing_time": processing_time,
                "tokens_used": len(response.text.split()),  # Approximate
                "context_used": context is not None
            }
            
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            raise
    
    async def _generate_ollama_response(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]], 
        max_tokens: int,
        start_time: datetime
    ) -> Dict[str, Any]:
        """Generate response using Ollama local model"""
        try:
            enhanced_prompt = self._enhance_prompt_with_context(prompt, context)
            
            payload = {
                "model": self.ollama_model,
                "prompt": enhanced_prompt,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": 0.7
                }
            }
            
            response = await asyncio.to_thread(
                requests.post,
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                processing_time = (datetime.now() - start_time).total_seconds()
                
                return {
                    "status": "success",
                    "prompt": prompt,
                    "response": result.get("response", ""),
                    "model": f"ollama-{self.ollama_model}",
                    "confidence": 0.85,  # Local models typically have good confidence
                    "processing_time": processing_time,
                    "tokens_used": len(result.get("response", "").split()),
                    "context_used": context is not None
                }
            else:
                raise Exception(f"Ollama API returned status {response.status_code}")
                
        except Exception as e:
            logger.error(f"Ollama API error: {e}")
            raise
    
    def _enhance_prompt_with_context(self, prompt: str, context: Optional[Dict[str, Any]]) -> str:
        """Enhance prompt with context information"""
        if not context:
            return f"You are Agent Lee, an AI assistant. User: {prompt}"
        
        context_str = ""
        if context.get("conversation_history"):
            context_str += f"Previous conversation: {context['conversation_history']}\n"
        if context.get("system_status"):
            context_str += f"System status: {context['system_status']}\n"
        if context.get("user_preferences"):
            context_str += f"User preferences: {context['user_preferences']}\n"
        
        return f"""You are Agent Lee, an AI assistant with access to system information.

Context:
{context_str}

Current user message: {prompt}

Please provide a helpful and contextually appropriate response."""
    
    def _get_fallback_response(self, prompt: str, error: Optional[str] = None) -> Dict[str, Any]:
        """Generate a fallback response when LLM services are unavailable"""
        fallback_responses = [
            "I understand your request. While my advanced AI capabilities are currently limited, I'm here to help with basic system operations.",
            "I'm processing your request with my backup systems. How can I assist you with Agent Lee's core functions?",
            "My main AI processing is temporarily unavailable, but I can still help you navigate the system and perform basic tasks.",
            "I'm operating in simplified mode right now. Let me know what specific task you'd like me to help with.",
            "While my full AI capabilities are offline, I can still assist with system monitoring, task management, and basic operations."
        ]
        
        import random
        response = random.choice(fallback_responses)
        
        if error:
            response += f" (Note: {error})"
        
        return {
            "status": "fallback",
            "prompt": prompt,
            "response": response,
            "model": "fallback",
            "confidence": 0.5,
            "processing_time": 0.1,
            "tokens_used": len(response.split()),
            "context_used": False,
            "error": error
        }
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        models = []
        if self.gemini_available:
            models.append("gemini-1.5-flash")
        if self.ollama_available:
            models.append(f"ollama-{self.ollama_model}")
        models.append("fallback")
        return models
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of LLM services"""
        return {
            "gemini_available": self.gemini_available,
            "ollama_available": self.ollama_available,
            "available_models": self.get_available_models(),
            "primary_model": "gemini-1.5-flash" if self.gemini_available else "ollama" if self.ollama_available else "fallback"
        }
