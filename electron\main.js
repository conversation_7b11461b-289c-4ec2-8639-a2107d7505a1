const { app, BrowserWindow, ipcMain, shell, dialog } = require('electron');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');
const fs = require('fs');
const os = require('os');

// Keep a global reference of the mainWindow object
let mainWindow;
let backendProcess = null;
const isWindows = process.platform === 'win32';
const isMac = process.platform === 'darwin';
const isLinux = process.platform === 'linux';

// Define backend paths
const backendPath = path.join(app.getAppPath(), '..', 'backend');
const backendScript = path.join(app.getAppPath(), '..', 'start.py');

/**
 * Create the main application window
 */
function createMainWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 400,
    height: 600,
    minWidth: 350,
    minHeight: 500,
    maxWidth: 500,
    maxHeight: 700,
    title: 'Agent Lee™ AI Assistant',
    frame: false, // Remove window frame for floating effect
    transparent: true, // Transparent background
    alwaysOnTop: true, // Keep on top of other windows
    resizable: true,
    movable: true,
    minimizable: true,
    maximizable: false,
    closable: true,
    skipTaskbar: false, // Show in taskbar
    icon: path.join(app.getAppPath(), 'assets/icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: !app.isPackaged,
    },
    show: false, // Don't show until ready
  });

  // Load the original index.html file
  mainWindow.loadFile(path.join(app.getAppPath(), '..', 'index.html'));

  // Show window when ready to avoid flashing
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open external links in default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Handle window close
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

/**
 * Start the backend server
 */
async function startBackendServer() {
  try {
    console.log('Starting backend server...');

    // Check if script exists
    if (!fs.existsSync(backendScript)) {
      console.error(`Backend script not found: ${backendScript}`);
      dialog.showErrorBox(
        'Backend Error',
        `Could not find backend script at ${backendScript}`
      );
      return false;
    }

    // Spawn backend process using Python
    const pythonCmd = isWindows ? 'python' : 'python3';
    backendProcess = spawn(pythonCmd, ['start.py'], {
      cwd: path.join(app.getAppPath(), '..'),
      stdio: 'pipe',
      shell: true,
    });

    // Log output
    backendProcess.stdout.on('data', (data) => {
      console.log(`Backend stdout: ${data}`);
    });

    backendProcess.stderr.on('data', (data) => {
      console.error(`Backend stderr: ${data}`);
    });

    backendProcess.on('error', (err) => {
      console.error('Failed to start backend process:', err);
      dialog.showErrorBox(
        'Backend Error',
        `Failed to start backend: ${err.message}`
      );
    });

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
      backendProcess = null;
    });

    // Wait for backend to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    return true;
  } catch (error) {
    console.error('Error starting backend:', error);
    return false;
  }
}

/**
 * App initialization
 */
app.whenReady().then(async () => {
  // Start backend server
  const backendStarted = await startBackendServer();
  
  // Create main window whether backend started or not
  // The UI will handle connectivity errors
  createMainWindow();
  
  // Set app menu
  if (isMac) {
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
      }
    });
  }
});

/**
 * Handle app shutdown
 */
app.on('window-all-closed', () => {
  if (backendProcess) {
    console.log('Killing backend process...');
    if (isWindows) {
      spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
    } else {
      backendProcess.kill('SIGTERM');
    }
  }
  
  if (!isMac) {
    app.quit();
  }
});

/**
 * IPC Message Handlers
 */
// Handler for checking backend status
ipcMain.handle('check-backend', async () => {
  try {
    const response = await fetch('http://localhost:8000/api/system_status');
    return response.ok;
  } catch (error) {
    console.error('Backend check error:', error);
    return false;
  }
});

// Handler for restarting backend
ipcMain.handle('restart-backend', async () => {
  if (backendProcess) {
    if (isWindows) {
      spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
    } else {
      backendProcess.kill('SIGTERM');
    }
    backendProcess = null;
  }
  
  return await startBackendServer();
});