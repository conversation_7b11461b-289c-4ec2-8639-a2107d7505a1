#!/usr/bin/env python3
"""
Simple startup script for Agent Lee™ System v2.0
Minimal version with proper dotenv loading
"""

import os
from dotenv import load_dotenv

print("🤖 Agent Lee™ System v2.0 Simple Startup")
print("=" * 50)

# Load environment variables FIRST
load_dotenv()
print("✅ Environment variables loaded")

# Check for API key
gemini_key = os.getenv("GEMINI_API_KEY")
if not gemini_key:
    print("⚠️ GEMINI_API_KEY not found in .env file")
    print("   The system will use fallback mode")
else:
    print("✅ GEMINI_API_KEY loaded")

# Check basic configuration
debug_mode = os.getenv("AGENTLEE_DEBUG", "False").lower() == "true"
host = os.getenv("AGENTLEE_HOST", "127.0.0.1")
port = int(os.getenv("AGENTLEE_PORT", "8000"))

print(f"✅ Configuration: {host}:{port} (debug: {debug_mode})")

# Now import and start the enhanced controller
try:
    print("\n🚀 Starting Agent Lee™ System v2.0...")
    
    from agentlee_controller_v2 import app
    import uvicorn
    
    print(f"🌐 Server starting at http://{host}:{port}")
    print("📚 API documentation: http://localhost:8000/docs")
    print("🛑 Press Ctrl+C to stop")
    print("-" * 50)
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        reload=debug_mode
    )
    
except KeyboardInterrupt:
    print("\n🛑 Server stopped by user")
except Exception as e:
    print(f"❌ Startup failed: {e}")
    print("\n💡 Try running the test first:")
    print("   python test_dotenv_fix.py")
