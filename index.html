<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Lee™ System</title>
  <style>
    /* Basic Reset */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      background-color: #0a0e29; /* Dark blue background */
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      position: relative;
      overflow: hidden;
    }
    
    /* Animated background grid */
    .grid-background {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
          linear-gradient(rgba(0, 242, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 242, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      z-index: -1;
      animation: gridMove 20s linear infinite;
    }

    @keyframes gridMove {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }
    
    /* Boot screen overlay */
    .boot-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(10, 14, 41, 0.95);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      transition: opacity 1s ease;
    }

    .boot-overlay.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .boot-logo {
      font-size: 4rem;
      font-weight: 900;
      color: #00f2ff;
      text-shadow: 0 0 30px rgba(0, 242, 255, 0.6);
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
    }
    
    .boot-logo-image {
      width: 120px;
      height: 120px;
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
      border-radius: 20px;
      box-shadow: 0 0 30px rgba(0, 242, 255, 0.6);
    }

    .boot-status {
      font-size: 1.2rem;
      color: #e0e7ff;
      margin-bottom: 3rem;
      text-align: center;
    }

    .boot-progress {
      width: 400px;
      height: 4px;
      background: rgba(0, 242, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 2rem;
    }

    .boot-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #00f2ff, #9854ff);
      width: 0%;
      border-radius: 2px;
      transition: width 0.5s ease;
    }

    .boot-console {
      width: 500px;
      height: 200px;
      background: rgba(0, 0, 0, 0.8);
      border: 1px solid #00f2ff;
      border-radius: 8px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      overflow-y: auto;
      color: #e0e7ff;
    }

    .console-line {
      margin-bottom: 5px;
      opacity: 0;
      animation: fadeIn 0.5s forwards;
    }

    .console-line.success { color: #06d6a0; }
    .console-line.warning { color: #ffd166; }
    .console-line.error { color: #ff416c; }
    .console-line.info { color: #00f2ff; }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    /* Agent Card Styles - MAIN INTERFACE */
    #agent-card {
      width: 320px;
      background-color: #1e293b;
      color: white;
      border-radius: 16px;
      border: 4px solid #3b82f6;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2), 0 0 30px rgba(59, 130, 246, 0.3);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 16px;
      z-index: 1000;
      transition: all 0.3s ease-in-out;
    }
    
    /* Card Header */
    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: move;
      margin-bottom: 16px;
    }
    
    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid #93c5fd;
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
    }
    
    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .agent-details h3 {
      color: #93c5fd;
      font-size: 18px;
      margin-bottom: 4px;
    }
    
    .agent-details p {
      color: #bfdbfe;
      font-size: 14px;
    }
    
    /* Navigation Grid */
    .navigation-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .nav-button {
      background-color: #334155;
      border: none;
      color: white;
      padding: 8px 4px;
      text-align: center;
      text-decoration: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .nav-button:hover {
      background-color: #475569;
    }
    
    .nav-button span {
      font-size: 16px;
      margin-bottom: 4px;
      color: #60a5fa;
    }
    
    /* Chat Area */
    .chat-area {
      height: 144px;
      background-color: #334155;
      border-radius: 8px;
      padding: 8px;
      margin-bottom: 8px;
      overflow-y: auto;
    }
    
    .message {
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 8px;
    }
    
    .user-message {
      background-color: #475569;
      margin-left: 16px;
    }
    
    .agent-message {
      background-color: #3b82f6;
      margin-right: 16px;
    }
    
    .empty-chat {
      color: #94a3b8;
      text-align: center;
      font-style: italic;
      margin-top: 48px;
    }
    
    /* Message Input */
    .message-input {
      width: 100%;
      padding: 8px;
      border-radius: 8px;
      border: 1px solid #475569;
      background-color: #475569;
      color: white;
      resize: none;
      margin-bottom: 12px;
    }
    
    .message-input::placeholder {
      color: #94a3b8;
    }
    
    /* Control Buttons */
    .control-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .control-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 4px;
      border: none;
      border-radius: 8px;
      color: white;
      font-size: 12px;
      cursor: pointer;
    }
    
    .send-btn { background-color: #2563eb; }
    .send-btn:hover { background-color: #3b82f6; }
    
    .listen-btn { background-color: #16a34a; }
    .listen-btn:hover { background-color: #22c55e; }
    .listen-btn.active { background-color: #22c55e; box-shadow: 0 0 10px #22c55e; }
    
    .stop-btn { background-color: #dc2626; }
    .stop-btn:hover { background-color: #ef4444; }
    
    .finish-btn { background-color: #ca8a04; }
    .finish-btn:hover { background-color: #eab308; }
    
    .email-btn { background-color: #4f46e5; }
    .email-btn:hover { background-color: #6366f1; }
    
    .phone-btn { background-color: #0d9488; }
    .phone-btn:hover { background-color: #14b8a6; }
    
    .chat-btn { background-color: #3b82f6; }
    .chat-btn:hover { background-color: #60a5fa; }
    
    .camera-btn { background-color: #9333ea; }
    .camera-btn:hover { background-color: #a855f7; }
    
    /* Permissions modal */
    .permissions-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .permissions-modal.active {
      opacity: 1;
      visibility: visible;
    }

    

    .modal-title {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #00f2ff;
    }

    .modal-description {
      margin-bottom: 30px;
      line-height: 1.6;
      color: rgba(224, 231, 255, 0.9);
    }

    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px;
      margin-bottom: 10px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      border: 1px solid rgba(0, 242, 255, 0.2);
      color: white; /* Added white text color */
    }

    .permission-item strong {
      color: #e0e7ff; /* Lighter color for strong text */
      font-weight: 600;
    }

    .permission-item small {
      color: #bfdbfe; /* Light blue for small description text */
      display: block;
      margin-top: 5px;
    }

    .permission-toggle {
      width: 50px;
      height: 25px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      position: relative;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .permission-toggle.active {
      background: #06d6a0;
    }

    .permission-toggle::after {
      content: '';
      position: absolute;
      width: 21px;
      height: 21px;
      background: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: transform 0.3s ease;
    }

    .permission-toggle.active::after {
      transform: translateX(25px);
    }

    .modal-buttons {
      display: flex;
      gap: 15px;
      margin-top: 30px;
    }

    .modal-button {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: 10px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .modal-button.primary {
      background: linear-gradient(135deg, #00f2ff, #9854ff);
      color: white;
    }

    .modal-button.secondary {
      background: transparent;
      color: #e0e7ff;
      border: 1px solid rgba(0, 242, 255, 0.2);
    }

    .modal-button:hover {
      transform: translateY(-2px);
    }

    /* Help link for permissions */
    .help-link {
      color: #00f2ff;
      text-decoration: underline;
      cursor: pointer;
      margin-top: 10px;
      display: inline-block;
      font-size: 0.9rem;
    }

    .help-link:hover {
      text-decoration: none;
      color: #9854ff;
    }
    
    /* Camera feed */
    .camera-container {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 320px;
      height: 240px;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #3b82f6;
      z-index: 900;
      display: none;
    }
    
    .camera-container.active {
      display: block;
    }
    
    #camera-feed {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    /* System status indicator */
    
    
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #06d6a0;
      box-shadow: 0 0 10px #06d6a0;
    }
    
    .status-dot.warning {
      background: #ffd166;
      box-shadow: 0 0 10px #ffd166;
    }
    
    .status-dot.error {
      background: #ff416c;
      box-shadow: 0 0 10px #ff416c;
    }
  </style>
</head>
<body>
  <div class="grid-background"></div>
  
  <!-- Boot Screen -->
  <div class="boot-overlay" id="bootOverlay">
    <img src="r239gfoi0w.png" alt="Agent Lee" class="boot-logo-image">
    <div class="boot-status" id="bootStatus">Initializing Agent Lee Protocol...</div>
    <div class="boot-progress">
      <div class="boot-progress-bar" id="bootProgress"></div>
    </div>
    <div class="boot-console" id="bootConsole">
      <div class="console-line info">[SYSTEM] Starting Agent Lee™ Operating System...</div>
    </div>
  </div>
  
  <!-- System status indicator -->
  <div class="system-status" id="systemStatus">
    <div class="status-dot" id="statusDot"></div>
    <span id="statusText">Initializing...</span>
  </div>
  
  <!-- Agent Card - Main Interface -->
  <div id="agent-card">
    <!-- Card Header -->
    <div class="card-header" id="drag-handle">
      <div class="avatar">
        <img src="frontend/AgetleeAvatar.png" alt="Agent Lee" id="agent-avatar">
      </div>
      <div class="agent-details">
        <h3>Agent Lee</h3>
        <p>Your AI Assistant</p>
      </div>
    </div>
    
    <!-- Navigation Grid -->
    <div class="navigation-grid">
      <button class="nav-button" data-dashboard="todo">
        <span>📋</span>
        To-Do List
      </button>
      <button class="nav-button" data-dashboard="agents">
        <span>👥</span>
        Agents
      </button>
      <button class="nav-button" data-dashboard="database">
        <span>🗄️</span>
        Database
      </button>
      <button class="nav-button" data-dashboard="llm">
        <span>🧠</span>
        LLM Brain
      </button>
      <button class="nav-button" data-dashboard="workers">
        <span>⚙️</span>
        Workers
      </button>
      <button class="nav-button" id="diagnosticsBtn">
        <span>🔍</span>
        Diagnostics
      </button>
    </div>
    
    <!-- Chat Area -->
    <div class="chat-area" id="chat-messages">
      <div class="empty-chat" id="empty-message">
        Agent Lee initialized and ready to assist
      </div>
      <!-- Messages will be added here dynamically -->
    </div>
    
    <!-- Message Input -->
    <textarea 
      class="message-input" 
      id="message-input" 
      rows="2" 
      placeholder="Type your message or command..."></textarea>
    
    <!-- Control Buttons - First Row -->
    <div class="control-row">
      <button class="control-button send-btn" id="send-button">
        <span class="icon">✉️</span> Send
      </button>
      <button class="control-button listen-btn" id="listen-button">
        <span class="icon">🎤</span> Listen
      </button>
      <button class="control-button stop-btn" id="stop-button">
        <span class="icon">⏹️</span> Stop
      </button>
      <button class="control-button finish-btn" id="finish-button">
        <span class="icon">✅</span> Finish
      </button>
    </div>
    
    <!-- Control Buttons - Second Row -->
    <div class="control-row">
      <button class="control-button email-btn" id="email-button">
        <span class="icon">📧</span> Email
      </button>
      <button class="control-button phone-btn" id="phone-button">
        <span class="icon">📱</span> Phone
      </button>
      <button class="control-button chat-btn" id="chat-button">
        <span class="icon">💬</span> Chat
      </button>
      <button class="control-button camera-btn" id="camera-button">
        <span class="icon">📷</span> Camera
      </button>
    </div>

    <!-- Personality Controls -->
    <div class="personality-panel">
      <h3>🎭 Agent Lee's Personality</h3>
      <div class="personality-controls">
        <label for="personality-select">Current Mode:</label>
        <select id="personality-select">
          <option value="default">Default - Balanced & Helpful</option>
          <option value="funny">Funny - Jokes & Humor</option>
          <option value="serious">Serious - Professional</option>
          <option value="excited">Excited - Enthusiastic</option>
          <option value="chill">Chill - Laid-back</option>
        </select>
        <button id="memory-button" class="personality-btn">
          <span class="icon">🧠</span> View Memory
        </button>
      </div>
      <div id="personality-status" class="personality-status"></div>
    </div>
  </div>

  <!-- Permissions Modal -->
  <div class="permissions-modal" id="permissionsModal">
    <div class="modal-content">
      <h2 class="modal-title">System Permissions</h2>
      <p class="modal-description">
        Agent Lee™ requires certain permissions to function properly. Please review and enable the permissions you'd like to grant.
      </p>
      
      <div class="permission-item">
        <div>
          <strong>Microphone Access</strong>
          <br><small>For voice commands and speech recognition</small>
        </div>
        <div class="permission-toggle" data-permission="microphone"></div>
      </div>
      
      <div class="permission-item">
        <div>
          <strong>Camera Access</strong>
          <br><small>For visual input and monitoring</small>
        </div>
        <div class="permission-toggle" data-permission="camera"></div>
      </div>
      
      <div class="permission-item">
        <div>
          <strong>File Access</strong>
          <br><small>For document processing and storage</small>
        </div>
        <div class="permission-toggle" data-permission="files"></div>
      </div>
      
      <div class="permission-item">
        <div>
          <strong>Background Tasks</strong>
          <br><small>For continuous monitoring and updates</small>
        </div>
        <div class="permission-toggle" data-permission="background"></div>
      </div>

      <p><a class="help-link" id="browser-help-link">Need help with browser permissions?</a></p>
      
      <div class="modal-buttons">
        <button class="modal-button secondary" id="cancelPermissions">Cancel</button>
        <button class="modal-button primary" id="savePermissions">Save & Continue</button>
      </div>
    </div>
  </div>

  <!-- Camera Container -->
  <div class="camera-container" id="cameraContainer">
    <video id="camera-feed" autoplay muted></video>
  </div>

  <!-- JavaScript -->
  <script type="module">
    // Import API client
    import apiClient from './js/api-client.js';

    // Global state
    let isListening = false;
    let recognition = null;
    let cameraStream = null;
    let bootSequenceComplete = false;

    // DOM elements
    const agentCard = document.getElementById('agent-card');
    const bootOverlay = document.querySelector('.boot-overlay');
    const bootStatus = document.querySelector('.boot-status');
    const bootProgress = document.querySelector('.boot-progress-bar');
    const bootConsole = document.querySelector('.boot-console');
    const messageInput = document.getElementById('message-input');
    const chatArea = document.querySelector('.chat-area');
    const permissionsModal = document.getElementById('permissionsModal');

    // Boot sequence
    async function startBootSequence() {
      const steps = [
        { text: 'Initializing Agent Lee™ System...', progress: 10 },
        { text: 'Loading neural networks...', progress: 25 },
        { text: 'Connecting to backend API...', progress: 40 },
        { text: 'Checking system permissions...', progress: 60 },
        { text: 'Initializing voice recognition...', progress: 80 },
        { text: 'System ready!', progress: 100 }
      ];

      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        bootStatus.textContent = step.text;
        bootProgress.style.width = step.progress + '%';

        // Add console line
        const consoleLine = document.createElement('div');
        consoleLine.className = `console-line ${step.progress === 100 ? 'success' : 'info'}`;
        consoleLine.textContent = `[${new Date().toLocaleTimeString()}] ${step.text}`;
        bootConsole.appendChild(consoleLine);
        bootConsole.scrollTop = bootConsole.scrollHeight;

        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Check API connection
      const connected = await apiClient.checkConnection();
      if (!connected) {
        const warningLine = document.createElement('div');
        warningLine.className = 'console-line warning';
        warningLine.textContent = '[WARNING] Backend API not available - running in demo mode';
        bootConsole.appendChild(warningLine);
      }

      // Hide boot overlay
      setTimeout(() => {
        bootOverlay.classList.add('hidden');
        bootSequenceComplete = true;
        initializeInterface();
      }, 1000);
    }

    // Initialize interface
    function initializeInterface() {
      // Show agent card
      agentCard.style.display = 'block';

      // Add Agent Lee's proper welcome message
      setTimeout(() => {
        addMessage('agent', '🎉 Hello! I\'m Agent Lee™, your AI assistant!');
        setTimeout(() => {
          addMessage('agent', '🚀 I can actually control your system - open apps, search the web, manage your email, and much more!');
          setTimeout(() => {
            addMessage('agent', '💬 Try asking me to "open calculator", "search for something", or just chat with me. Let\'s get started! 😊');
          }, 1500);
        }, 1000);
      }, 500);

      // Initialize voice recognition if available
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        initializeVoiceRecognition();
      }
    }

    // Voice recognition setup
    function initializeVoiceRecognition() {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        const confidence = event.results[0][0].confidence;

        addMessage('user', transcript);
        processVoiceCommand(transcript, confidence);
      };

      recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
        isListening = false;
        updateListenButton();
      };

      recognition.onend = function() {
        isListening = false;
        updateListenButton();
      };
    }

    // Add message to chat
    function addMessage(sender, text) {
      const chatArea = document.querySelector('.chat-area');
      const emptyChat = chatArea.querySelector('.empty-chat');

      if (emptyChat) {
        emptyChat.remove();
      }

      const message = document.createElement('div');
      message.className = `message ${sender}-message`;
      message.textContent = text;

      chatArea.appendChild(message);
      chatArea.scrollTop = chatArea.scrollHeight;
    }

    // Process voice command
    async function processVoiceCommand(command, confidence) {
      try {
        const response = await apiClient.processVoiceCommand(command, confidence);
        addMessage('agent', response.response || 'Command processed.');

        if (response.action_taken) {
          await apiClient.speak(response.response);
        }
      } catch (error) {
        console.error('Voice command processing failed:', error);
        addMessage('agent', 'I had trouble processing that command. Please try again.');
      }
    }

    // Update listen button state
    function updateListenButton() {
      const listenBtn = document.getElementById('listen-button');
      if (isListening) {
        listenBtn.classList.add('active');
        listenBtn.innerHTML = '<span class="icon">🎤</span> Listening...';
      } else {
        listenBtn.classList.remove('active');
        listenBtn.innerHTML = '<span class="icon">🎤</span> Listen';
      }
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Start boot sequence
      startBootSequence();

      // Send button - Enhanced with system commands
      document.getElementById('send-button').addEventListener('click', async function() {
        const message = messageInput.value.trim();
        if (message) {
          addMessage('user', message);
          messageInput.value = '';

          try {
            // Check for system commands first
            if (await handleSystemCommand(message)) {
              return; // System command handled
            }

            // Regular LLM conversation
            const response = await apiClient.sendLLMMessage(message);
            addMessage('agent', response.response);
            await apiClient.speak(response.response);
          } catch (error) {
            addMessage('agent', 'I apologize, but I\'m having trouble right now. Please try again.');
          }
        }
      });

      // Handle system commands
      async function handleSystemCommand(message) {
        const lowerMessage = message.toLowerCase();

        // App opening commands
        if (lowerMessage.includes('open ') || lowerMessage.includes('launch ') || lowerMessage.includes('start ')) {
          const appName = lowerMessage.replace(/^(open|launch|start)\s+/, '');
          try {
            const response = await fetch('http://localhost:8000/api/system/open_app', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ app_name: appName })
            });
            const result = await response.json();
            addMessage('agent', result.message || `🚀 Opening ${appName}...`);
            return true;
          } catch (error) {
            return false;
          }
        }

        // Search commands
        if (lowerMessage.includes('search for ') || lowerMessage.includes('google ') || lowerMessage.includes('find ')) {
          const query = lowerMessage.replace(/^(search for|google|find)\s+/, '');
          try {
            const response = await fetch('http://localhost:8000/api/system/web_search', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ query: query, engine: 'google' })
            });
            const result = await response.json();
            addMessage('agent', result.message || `🌐 Searching for "${query}"...`);
            return true;
          } catch (error) {
            return false;
          }
        }

        return false; // Not a system command
      }

      // Stop button - stops current speech
      document.getElementById('stop-button').addEventListener('click', function() {
        if ('speechSynthesis' in window) {
          speechSynthesis.cancel();
          addMessage('agent', 'Speech stopped.');
        }
        if (isListening && recognition) {
          recognition.stop();
        }
      });

      // Finish button - continues from where stopped
      document.getElementById('finish-button').addEventListener('click', function() {
        addMessage('agent', 'Continuing from where I left off...');
        // Add logic to continue previous response if needed
      });

      // Email button - REAL functionality
      document.getElementById('email-button').addEventListener('click', async function() {
        addMessage('agent', '📧 Opening your email client...');
        try {
          const response = await fetch('http://localhost:8000/api/system/open_email', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
          });
          const result = await response.json();
          if (result.status === 'success') {
            addMessage('agent', result.message);
          }
        } catch (error) {
          // Fallback to default email
          window.open('mailto:', '_blank');
        }
      });

      // Phone button - REAL functionality
      document.getElementById('phone-button').addEventListener('click', async function() {
        addMessage('agent', '📱 Opening Telegram...');
        try {
          const response = await fetch('http://localhost:8000/api/system/open_messaging', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ app: 'telegram' })
          });
          const result = await response.json();
          if (result.status === 'success') {
            addMessage('agent', result.message);
          }
        } catch (error) {
          addMessage('agent', '📱 Trying to open messaging apps...');
        }
      });

      // Listen button
      document.getElementById('listen-button').addEventListener('click', function() {
        if (!recognition) {
          alert('Voice recognition is not available in your browser.');
          return;
        }

        if (isListening) {
          recognition.stop();
        } else {
          isListening = true;
          updateListenButton();
          recognition.start();
        }
      });

      // Navigation buttons
      document.querySelectorAll('.nav-button').forEach(button => {
        button.addEventListener('click', function() {
          const dashboard = this.getAttribute('data-dashboard');
          const action = this.textContent.trim();

          addMessage('agent', `Opening ${action}...`);

          // Navigate to specific frontend files
          switch(dashboard) {
            case 'todo':
              window.open('frontend/Agent Lee\'s Dynamic To-Do List.html', '_blank');
              break;
            case 'agents':
              window.open('frontend/Agent Lee\'s Agent Center.html', '_blank');
              break;
            case 'database':
              window.open('frontend/AgentLee\'sDB.html', '_blank');
              break;
            case 'llm':
              window.open('frontend/LLM BRAIN CENTER.html', '_blank');
              break;
            case 'workers':
              window.open('frontend/Agent Lee\'s Integrated Workers Center.html', '_blank');
              break;
            default:
              addMessage('agent', `${action} interface coming soon...`);
          }
        });
      });

      // Enter key in message input
      messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          document.getElementById('send-button').click();
        }
      });

      // Camera button
      document.getElementById('camera-button').addEventListener('click', async function() {
        const cameraContainer = document.getElementById('cameraContainer');
        const cameraFeed = document.getElementById('camera-feed');

        if (cameraStream) {
          // Stop camera
          cameraStream.getTracks().forEach(track => track.stop());
          cameraStream = null;
          cameraContainer.classList.remove('active');
          this.innerHTML = '<span class="icon">📷</span> Camera';
        } else {
          // Start camera
          try {
            cameraStream = await navigator.mediaDevices.getUserMedia({ video: true });
            cameraFeed.srcObject = cameraStream;
            cameraContainer.classList.add('active');
            this.innerHTML = '<span class="icon">📷</span> Stop';
          } catch (error) {
            console.error('Camera access failed:', error);
            alert('Camera access denied or not available.');
          }
        }
      });

      // Personality controls
      document.getElementById('personality-select').addEventListener('change', async function() {
        const scene = this.value;
        try {
          const response = await fetch('/api/personality/change_scene', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ scene: scene })
          });

          const result = await response.json();
          if (result.status === 'success') {
            addMessage('agent', `🎭 Personality changed to ${scene} mode! ${getPersonalityMessage(scene)}`);
            updatePersonalityStatus();
          } else {
            addMessage('agent', `Failed to change personality: ${result.message}`);
          }
        } catch (error) {
          console.error('Personality change failed:', error);
          addMessage('agent', 'Sorry, I had trouble changing my personality mode.');
        }
      });

      // Memory button
      document.getElementById('memory-button').addEventListener('click', async function() {
        try {
          const response = await fetch('/api/personality/memory');
          const result = await response.json();

          if (result.status === 'success') {
            const memoryCount = result.memory_size;
            addMessage('agent', `🧠 I remember our last ${memoryCount} conversation exchanges. My memory helps me understand context and maintain our relationship!`);
          }
        } catch (error) {
          console.error('Memory fetch failed:', error);
          addMessage('agent', 'I had trouble accessing my memory right now.');
        }
      });

      // Load initial personality status
      updatePersonalityStatus();
    });

    // Helper functions for personality
    function getPersonalityMessage(scene) {
      const messages = {
        'default': 'I\'m ready to help with a balanced approach! 😊',
        'funny': 'Time for some laughs! I\'m feeling witty today! 😄',
        'serious': 'I\'m in professional mode. Let\'s get things done efficiently. 💼',
        'excited': 'I\'m super pumped and ready for anything! Let\'s go! 🚀',
        'chill': 'Just taking it easy and going with the flow. What\'s up? 😎'
      };
      return messages[scene] || 'Ready for whatever comes next!';
    }

    async function updatePersonalityStatus() {
      try {
        const response = await fetch('/api/personality/status');
        const result = await response.json();

        if (result.status === 'success') {
          const status = result.personality_status;
          const statusDiv = document.getElementById('personality-status');
          statusDiv.innerHTML = `
            <small>
              Current: ${status.current_scene} |
              Memory: ${status.memory_size} exchanges |
              Humor Level: ${Math.round(status.humor_level * 100)}%
            </small>
          `;
        }
      } catch (error) {
        console.error('Status update failed:', error);
      }
    }
  </script>
</body>
</html>