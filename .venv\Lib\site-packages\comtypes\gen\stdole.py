from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    OLE_XSIZE_PIXELS, Unchecked, Library, IUnknown, Monochrome,
    FONTNAME, IPicture, <PERSON>LE_CANCELBOOL, FONTUNDERSCORE,
    OLE_YSIZE_HIMETRIC, FONTSIZE, StdPicture, _lcid, Font,
    FONTSTRIKETHROUGH, DISPPROPERTY, Default, StdFont, IPictureDisp,
    Color, OLE_ENABLEDEFAULTBOOL, O<PERSON>_COLOR, OLE_YPOS_CONTAINER,
    VgaColor, BSTR, <PERSON><PERSON>PMETHOD, DISPPARAMS, VARIANT_BOOL,
    _check_version, OLE_OPTEXCLUSIVE, EXCEPINFO, IFontEventsDisp,
    <PERSON><PERSON>_XSIZE_HIMETRI<PERSON>, HRESULT, Checked, OLE_XSIZE_CONTAINER, GUID,
    COMMETHOD, OLE_YSIZE_PIXELS, FONTBOLD, IFont, CoClass, Picture,
    OLE_HANDLE, IDispatch, OLE_YPOS_PIXELS, OLE_XPOS_CONTAINER,
    IEnumVARIANT, OLE_XPOS_HIMETRIC, FONTITALIC, OLE_YSIZE_CONTAINER,
    FontEvents, IFontDisp, OLE_XPOS_PIXELS, Gray, OLE_YPOS_HIMETRIC,
    dispid, typelib_path
)


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


__all__ = [
    'OLE_XSIZE_PIXELS', 'Unchecked', 'IFontEventsDisp', 'Library',
    'Monochrome', 'OLE_XSIZE_HIMETRIC', 'FONTNAME', 'IPicture',
    'Checked', 'OLE_XSIZE_CONTAINER', 'OLE_CANCELBOOL',
    'OLE_YSIZE_PIXELS', 'FONTUNDERSCORE', 'FONTBOLD', 'IFont',
    'OLE_YSIZE_HIMETRIC', 'Picture', 'OLE_HANDLE',
    'LoadPictureConstants', 'FONTSIZE', 'OLE_YPOS_PIXELS',
    'StdPicture', 'OLE_XPOS_CONTAINER', 'Font', 'OLE_TRISTATE',
    'FONTSTRIKETHROUGH', 'OLE_XPOS_HIMETRIC', 'Default', 'StdFont',
    'FONTITALIC', 'IPictureDisp', 'Color', 'OLE_YSIZE_CONTAINER',
    'OLE_ENABLEDEFAULTBOOL', 'OLE_COLOR', 'OLE_YPOS_CONTAINER',
    'VgaColor', 'FontEvents', 'IFontDisp', 'OLE_XPOS_PIXELS', 'Gray',
    'OLE_YPOS_HIMETRIC', 'typelib_path', 'OLE_OPTEXCLUSIVE'
]

