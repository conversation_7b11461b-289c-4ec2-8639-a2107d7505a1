/**
 * Agent Lee™ API Client
 * Provides a unified interface for communicating with the backend API
 */

class AgentLeeApiClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
        this.isConnected = false;
        this.lastConnectionCheck = null;
        this.connectionStatus = 'unknown';
    }

    /**
     * Check if the backend API is available
     * @returns {Promise<boolean>} True if connected, false otherwise
     */
    async checkConnection() {
        try {
            const now = Date.now();
            
            // Only check once every 5 seconds to avoid too many requests
            if (this.lastConnectionCheck && now - this.lastConnectionCheck < 5000) {
                return this.isConnected;
            }
            
            const response = await fetch(`${this.baseUrl}/api/system_status`, {
                method: 'GET',
                headers: { 'Accept': 'application/json' },
                timeout: 3000
            });
            
            this.isConnected = response.ok;
            this.lastConnectionCheck = now;
            
            if (response.ok) {
                this.connectionStatus = 'connected';
                console.log('API connection successful');
                return true;
            } else {
                this.connectionStatus = 'error';
                console.warn('API connection failed with status:', response.status);
                return false;
            }
        } catch (error) {
            this.isConnected = false;
            this.lastConnectionCheck = Date.now();
            this.connectionStatus = 'unavailable';
            console.warn('API connection error:', error);
            return false;
        }
    }

    /**
     * Get system status from backend
     * @returns {Promise<Object>} System status data
     */
    async getSystemStatus() {
        try {
            const response = await this.request('/api/system_status');
            return response;
        } catch (error) {
            console.error('Failed to get system status:', error);
            return this.getFallbackSystemStatus();
        }
    }

    /**
     * Get module status and metrics
     * @returns {Promise<Object>} Module metrics data
     */
    async getModuleMetrics() {
        try {
            const response = await this.request('/api/check_modules');
            return response;
        } catch (error) {
            console.error('Failed to get module metrics:', error);
            return this.getFallbackModuleMetrics();
        }
    }

    /**
     * Get API version information
     * @returns {Promise<Object>} Version info
     */
    async getVersion() {
        try {
            const response = await this.request('/api/version');
            return response;
        } catch (error) {
            console.error('Failed to get version:', error);
            return { 
                version: 'Unknown',
                build: 'Demo Mode',
                powered_by: 'Frontend Only', 
                author: 'Agent Lee System Core'
            };
        }
    }

    /**
     * Send text to speech
     * @param {string} text - Text to speak
     * @param {string} voice - Voice to use (optional)
     * @param {number} rate - Speech rate (optional)
     * @returns {Promise<Object>} Result
     */
    async speak(text, voice = 'default', rate = 1.0) {
        try {
            const response = await this.request('/api/speak', 'POST', {
                text,
                voice,
                rate
            });
            return response;
        } catch (error) {
            console.error('Failed to speak text:', error);
            
            // Fallback to browser speech API if available
            if ('speechSynthesis' in window) {
                try {
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.rate = rate;
                    speechSynthesis.speak(utterance);
                    return { status: 'success', message: 'Using browser TTS fallback' };
                } catch (speechError) {
                    console.error('Browser TTS failed:', speechError);
                }
            }
            
            return { status: 'error', message: 'Speech synthesis failed' };
        }
    }

    /**
     * Process a voice command
     * @param {string} command - Voice command text
     * @param {number} confidence - Recognition confidence (0-1)
     * @returns {Promise<Object>} Command processing result
     */
    async processVoiceCommand(command, confidence = 1.0) {
        try {
            const response = await this.request('/api/voice_command', 'POST', {
                command,
                confidence,
                context: {}
            });
            return response;
        } catch (error) {
            console.error('Failed to process voice command:', error);
            return { 
                status: 'error',
                command,
                error: 'Command processing failed',
                action_taken: false
            };
        }
    }

    /**
     * Assign a task
     * @param {Object} task - Task data
     * @returns {Promise<Object>} Task assignment result
     */
    async assignTask(task) {
        try {
            const taskData = {
                task_id: task.id || crypto.randomUUID(),
                task_type: task.type || 'general',
                priority: task.priority || 1,
                assigned_to: task.assigned_to,
                description: task.description,
                metadata: task.metadata || {}
            };
            
            const response = await this.request('/api/assign_tasks', 'POST', taskData);
            return response;
        } catch (error) {
            console.error('Failed to assign task:', error);
            return { 
                status: 'error',
                message: 'Task assignment failed'
            };
        }
    }

    /**
     * Get the current task queue
     * @returns {Promise<Object>} Task queue data
     */
    async getTaskQueue() {
        try {
            const response = await this.request('/api/task_queue');
            return response;
        } catch (error) {
            console.error('Failed to get task queue:', error);
            return { 
                queue_length: 0,
                tasks: [],
                total_processed: 0
            };
        }
    }

    /**
     * Open a URL in Chrome
     * @param {string} url - URL to open
     * @returns {Promise<Object>} Result
     */
    async openChrome(url) {
        try {
            const response = await this.request('/api/open_chrome', 'POST', {
                url,
                browser: 'chrome',
                new_window: true
            });
            return response;
        } catch (error) {
            console.error('Failed to open Chrome:', error);
            
            // Fallback to window.open in browser
            window.open(url, '_blank');
            
            return { 
                status: 'partial',
                message: 'Opened in browser tab instead of Chrome application'
            };
        }
    }

    /**
     * Generic request method
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {Object} data - Request data
     * @returns {Promise<Object>} Response data
     */
    async request(endpoint, method = 'GET', data = null) {
        const url = `${this.baseUrl}${endpoint}`;
        const options = {
            method,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        };
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }
        
        return await response.json();
    }
    
    // Fallback data generators for when API is unavailable
    
    getFallbackSystemStatus() {
        return {
            llm_ready: true,
            llm_fallback: false,
            agents_ready: true,
            workers_ready: true,
            db_ready: true,
            notepad_ready: true,
            system_health: 95.0,
            last_update: new Date().toISOString()
        };
    }
    
    getFallbackModuleMetrics() {
        return {
            todo_count: 7,
            agent_count: 144,
            worker_count: 711,
            memory: 45.5,
            cpu: 32.1,
            hostname: 'localhost',
            platform: 'Browser',
            disk_usage: 0,
            network_status: 'online'
        };
    }
}

// Create and export singleton instance
const apiClient = new AgentLeeApiClient();
export default apiClient;