
# Module 28: Profile Loader
# Loads and stores user profiles for personalization
import json

class ProfileLoader:
    def __init__(self, path="user_profile.json"):
        self.path = path
        self.profile = {}

    def load(self):
        try:
            with open(self.path, "r") as f:
                self.profile = json.load(f)
        except FileNotFoundError:
            self.profile = {}

    def save(self):
        with open(self.path, "w") as f:
            json.dump(self.profile, f)
