{"version": 3, "file": "asyncTaskManager.js", "sourceRoot": "", "sources": ["../src/asyncTaskManager.ts"], "names": [], "mappings": ";;;AACA,+BAA2B;AAC3B,uCAAuC;AAEvC,MAAa,gBAAgB;IAI3B,YAA6B,iBAAoC;QAApC,sBAAiB,GAAjB,iBAAiB,CAAmB;QAHxD,UAAK,GAAwB,EAAE,CAAA;QACvB,WAAM,GAAiB,EAAE,CAAA;IAE0B,CAAC;IAErE,GAAG,CAAC,IAAwB;QAC1B,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACxE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAA;QACtB,CAAC;IACH,CAAC;IAED,OAAO,CAAC,OAAqB;QAC3B,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACrC,SAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE,sBAAsB,CAAC,CAAA;YACpF,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;gBACxB,CAAC;gBAAC,OAAe,CAAC,MAAM,EAAE,CAAA;YAC5B,CAAC;YACD,OAAM;QACR,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;YACjB,SAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAA;YACrE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACpB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9B,CAAC,CAAC,CACH,CAAA;IACH,CAAC;IAED,WAAW;QACT,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,CAAC;gBAAC,IAAY,CAAC,MAAM,EAAE,CAAA;YACzB,CAAC;QACH,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;IACvB,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,OAAO,EAAE,CAAA;QACX,CAAC;QAED,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,EAAE,CAAA;gBAClB,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBACvB,OAAM;YACR,CAAC;QACH,CAAC,CAAA;QAED,WAAW,EAAE,CAAA;QAEb,IAAI,MAAM,GAAsB,IAAI,CAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACxB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QAChB,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YACzC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAC9D,WAAW,EAAE,CAAA;YACb,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAK;YACP,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;oBACrC,IAAI,CAAC,WAAW,EAAE,CAAA;oBAClB,OAAO,EAAE,CAAA;gBACX,CAAC;gBAED,mEAAmE;gBACnE,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;gBACpB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;QACD,OAAO,MAAM,IAAI,EAAE,CAAA;IACrB,CAAC;CACF;AA9ED,4CA8EC;AAED,SAAS,UAAU,CAAC,MAAoB;IACtC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,MAAM,MAAM,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;SAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,qBAAW,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAA;IACnD,CAAC;AACH,CAAC", "sourcesContent": ["import { CancellationToken } from \"builder-util-runtime\"\nimport { log } from \"./log\"\nimport { NestedError } from \"./promise\"\n\nexport class AsyncTaskManager {\n  readonly tasks: Array<Promise<any>> = []\n  private readonly errors: Array<Error> = []\n\n  constructor(private readonly cancellationToken: CancellationToken) {}\n\n  add(task: () => Promise<any>) {\n    if (this.cancellationToken == null || !this.cancellationToken.cancelled) {\n      this.addTask(task())\n    }\n  }\n\n  addTask(promise: Promise<any>) {\n    if (this.cancellationToken.cancelled) {\n      log.debug({ reason: \"cancelled\", stack: new Error().stack }, \"async task not added\")\n      if (\"cancel\" in promise) {\n        ;(promise as any).cancel()\n      }\n      return\n    }\n\n    this.tasks.push(\n      promise.catch(it => {\n        log.debug({ error: it.message || it.toString() }, \"async task error\")\n        this.errors.push(it)\n        return Promise.resolve(null)\n      })\n    )\n  }\n\n  cancelTasks() {\n    for (const task of this.tasks) {\n      if (\"cancel\" in task) {\n        ;(task as any).cancel()\n      }\n    }\n    this.tasks.length = 0\n  }\n\n  async awaitTasks(): Promise<Array<any>> {\n    if (this.cancellationToken.cancelled) {\n      this.cancelTasks()\n      return []\n    }\n\n    const checkErrors = () => {\n      if (this.errors.length > 0) {\n        this.cancelTasks()\n        throwError(this.errors)\n        return\n      }\n    }\n\n    checkErrors()\n\n    let result: Array<any> | null = null\n    const tasks = this.tasks\n    let list = tasks.slice()\n    tasks.length = 0\n    while (list.length > 0) {\n      const subResult = await Promise.all(list)\n      result = result == null ? subResult : result.concat(subResult)\n      checkErrors()\n      if (tasks.length === 0) {\n        break\n      } else {\n        if (this.cancellationToken.cancelled) {\n          this.cancelTasks()\n          return []\n        }\n\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        list = tasks.slice()\n        tasks.length = 0\n      }\n    }\n    return result || []\n  }\n}\n\nfunction throwError(errors: Array<Error>) {\n  if (errors.length === 1) {\n    throw errors[0]\n  } else if (errors.length > 1) {\n    throw new NestedError(errors, \"Cannot cleanup: \")\n  }\n}\n"]}