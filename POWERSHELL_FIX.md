# PowerShell Logging Fix for Agent Lee™

## 🔧 **Problem Solved**

PowerShell has a limitation where `RedirectStandardOutput` and `RedirectStandardError` cannot point to the **same file path**. This was causing startup failures when trying to capture both stdout and stderr to a single log file.

## ✅ **Solution Implemented**

### **Split Logging Approach**
Instead of redirecting both streams to one file, we now use **separate log files**:

```powershell
$stdoutLog = ".\agentlee_logs\startup_${timestamp}_out.log"
$stderrLog = ".\agentlee_logs\startup_${timestamp}_err.log"

Start-Process -NoNewWindow -FilePath "python.exe" `
    -ArgumentList "backend\start_enhanced.py" `
    -RedirectStandardOutput $stdoutLog `
    -RedirectStandardError $stderrLog `
    -Wait
```

## 🚀 **New PowerShell Scripts**

### **1. Enhanced Launcher: `start_agentlee_enhanced.ps1`**

**Features:**
- ✅ Split stdout/stderr logging (fixes PowerShell limitation)
- ✅ Automatic virtual environment detection
- ✅ Multiple startup modes (Enhanced/Simple/Test)
- ✅ Log cleanup (keeps last 10 runs)
- ✅ Browser auto-open option
- ✅ Detailed error reporting

**Usage:**
```powershell
# Full enhanced startup
.\start_agentlee_enhanced.ps1

# Simple mode (minimal dependencies)
.\start_agentlee_enhanced.ps1 -Simple

# Test mode (verify setup)
.\start_agentlee_enhanced.ps1 -Test

# Auto-open browser
.\start_agentlee_enhanced.ps1 -OpenBrowser

# Custom log level
.\start_agentlee_enhanced.ps1 -LogLevel DEBUG
```

### **2. Quick Start: `quick_start.ps1`**

**Features:**
- ✅ Minimal script for fast testing
- ✅ Split logging fix applied
- ✅ Clear success/error reporting
- ✅ Shows last output lines

**Usage:**
```powershell
.\quick_start.ps1
```

## 📁 **Log File Structure**

```
agentlee_logs/
├── startup_2024-01-15_14-30-25_out.log    # Standard output
├── startup_2024-01-15_14-30-25_err.log    # Error output
├── startup_2024-01-15_14-25-10_out.log    # Previous run
└── startup_2024-01-15_14-25-10_err.log    # Previous run
```

## 🔍 **Error Detection Logic**

The scripts now intelligently detect issues:

```powershell
# Check if stderr has content
$hasErrors = (Test-Path $stderrLog) -and ((Get-Content $stderrLog).Length -gt 0)

if ($hasErrors) {
    Write-Host "⚠️ Errors detected!" -ForegroundColor Red
    # Show error details
} else {
    Write-Host "✅ Success!" -ForegroundColor Green
}
```

## 🛠️ **Troubleshooting**

### **Common Issues & Solutions**

1. **"Virtual environment not detected"**
   ```powershell
   # Manually activate first
   .\.venv\Scripts\Activate.ps1
   .\start_agentlee_enhanced.ps1
   ```

2. **"python.exe not found"**
   ```powershell
   # Use full path
   Start-Process -FilePath "C:\Python\python.exe" ...
   ```

3. **"Access denied to logs directory"**
   ```powershell
   # Run as administrator or change log location
   $logDir = "$env:TEMP\agentlee_logs"
   ```

### **Debug Mode**

For detailed troubleshooting:
```powershell
.\start_agentlee_enhanced.ps1 -LogLevel DEBUG -Test
```

## 📊 **Log Analysis**

### **Check Recent Logs**
```powershell
# View latest stdout
Get-Content .\agentlee_logs\startup_*_out.log | Select-Object -Last 20

# View latest errors
Get-Content .\agentlee_logs\startup_*_err.log | Select-Object -Last 10

# Find all error logs with content
Get-ChildItem .\agentlee_logs\*_err.log | Where-Object { (Get-Content $_).Length -gt 0 }
```

### **Automated Log Monitoring**
```powershell
# Watch for new errors in real-time
Get-Content .\agentlee_logs\startup_*_err.log -Wait -Tail 10
```

## 🔄 **Migration from Old Scripts**

### **Before (Problematic)**
```powershell
# This FAILS in PowerShell
Start-Process python.exe -ArgumentList "script.py" `
    -RedirectStandardOutput "log.txt" `
    -RedirectStandardError "log.txt"    # ❌ Same file = ERROR
```

### **After (Fixed)**
```powershell
# This WORKS in PowerShell
Start-Process python.exe -ArgumentList "script.py" `
    -RedirectStandardOutput "out.log" `
    -RedirectStandardError "err.log"    # ✅ Different files = SUCCESS
```

## 🎯 **Best Practices**

1. **Always use split logging** for PowerShell process redirection
2. **Check both stdout and stderr** for complete status
3. **Use timestamps** in log filenames to avoid conflicts
4. **Clean up old logs** to prevent disk space issues
5. **Test with `-Test` mode** before full deployment

## 🚀 **Quick Start Guide**

1. **Test the fix:**
   ```powershell
   .\start_agentlee_enhanced.ps1 -Test
   ```

2. **Start Agent Lee:**
   ```powershell
   .\start_agentlee_enhanced.ps1 -OpenBrowser
   ```

3. **Check logs if issues:**
   ```powershell
   Get-Content .\agentlee_logs\startup_*_err.log
   ```

## 📈 **Performance Benefits**

- ✅ **No more startup failures** due to logging conflicts
- ✅ **Separate error tracking** for better debugging
- ✅ **Automatic log rotation** prevents disk bloat
- ✅ **Real-time error detection** during startup
- ✅ **Cross-platform compatibility** maintained

---

**The PowerShell logging issue is now completely resolved!** 🎉

Your Agent Lee™ system will start reliably with proper error tracking and logging.
