{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,yDAA0E;AAAjE,sHAAA,iBAAiB,OAAA;AAAE,sHAAA,iBAAiB,OAAA;AAC7C,+CAauB;AAZrB,yGAAA,SAAS,OAAA;AACT,+GAAA,eAAe,OAAA;AACf,4GAAA,YAAY,OAAA;AAEZ,+GAAA,eAAe,OAAA;AAEf,6GAAA,aAAa,OAAA;AACb,uHAAA,uBAAuB,OAAA;AACvB,8HAAA,8BAA8B,OAAA;AAC9B,iHAAA,iBAAiB,OAAA;AACjB,yGAAA,SAAS,OAAA;AACT,mHAAA,mBAAmB,OAAA;AAErB,mDAeyB;AAJvB,0HAAA,wBAAwB,OAAA;AACxB,2GAAA,SAAS,OAAA;AAKX,iDAAyC;AAAhC,wGAAA,OAAO,OAAA;AAChB,+BAA6B;AAApB,4FAAA,IAAI,OAAA;AACb,yEAAqF;AAA5E,sIAAA,yBAAyB,OAAA;AAClC,6BAA0C;AAAjC,+FAAA,QAAQ,OAAA;AAAE,+FAAA,QAAQ,OAAA;AAG3B,OAAO;AACM,QAAA,+BAA+B,GAAG,eAAe,CAAA;AAC9D,WAAW;AACE,QAAA,6BAA6B,GAAG,YAAY,CAAA;AAEzD,SAAgB,OAAO,CAAI,CAAkC;IAC3D,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;QACd,OAAO,EAAE,CAAA;IACX,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,OAAO,CAAC,CAAA;IACV,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,CAAC,CAAC,CAAA;IACZ,CAAC;AACH,CAAC;AARD,0BAQC;AAED,SAAgB,QAAQ,CAAC,OAAe,EAAE,IAAY;IACpD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAC/B;IAAC,KAA+B,CAAC,IAAI,GAAG,IAAI,CAAA;IAC7C,OAAO,KAAK,CAAA;AACd,CAAC;AAJD,4BAIC", "sourcesContent": ["export { CancellationToken, CancellationError } from \"./CancellationToken\"\nexport {\n  HttpError,\n  createHttpError,\n  HttpExecutor,\n  DownloadOptions,\n  DigestTransform,\n  RequestHeaders,\n  safeGetHeader,\n  configureRequestOptions,\n  configureRequestOptionsFromUrl,\n  safeStringifyJson,\n  parseJson,\n  configureRequestUrl,\n} from \"./httpExecutor\"\nexport {\n  CustomPublishOptions,\n  GenericServerOptions,\n  GithubOptions,\n  KeygenOptions,\n  BitbucketOptions,\n  SnapStoreOptions,\n  PublishConfiguration,\n  S3Options,\n  SpacesOptions,\n  BaseS3Options,\n  getS3LikeProviderBaseUrl,\n  githubUrl,\n  PublishProvider,\n  AllPublishOptions,\n} from \"./publishOptions\"\nexport { UpdateInfo, UpdateFileInfo, WindowsUpdateInfo, BlockMapDataHolder, PackageFileInfo, ReleaseNoteInfo } from \"./updateInfo\"\nexport { parseDn } from \"./rfc2253Parser\"\nexport { UUID } from \"./uuid\"\nexport { ProgressCallbackTransform, ProgressInfo } from \"./ProgressCallbackTransform\"\nexport { parseXml, XElement } from \"./xml\"\nexport { BlockMap } from \"./blockMapApi\"\n\n// nsis\nexport const CURRENT_APP_INSTALLER_FILE_NAME = \"installer.exe\"\n// nsis-web\nexport const CURRENT_APP_PACKAGE_FILE_NAME = \"package.7z\"\n\nexport function asArray<T>(v: null | undefined | T | Array<T>): Array<T> {\n  if (v == null) {\n    return []\n  } else if (Array.isArray(v)) {\n    return v\n  } else {\n    return [v]\n  }\n}\n\nexport function newError(message: string, code: string) {\n  const error = new Error(message)\n  ;(error as NodeJS.ErrnoException).code = code\n  return error\n}\n"]}