"""
System Controller for Agent Lee™
Handles real system integration - opening apps, controlling system, web searches, etc.
"""

import os
import sys
import subprocess
import webbrowser
import platform
import logging
from typing import Dict, Any, List, Optional
import json

logger = logging.getLogger("agentlee.system")

class SystemController:
    def __init__(self):
        self.platform = platform.system().lower()
        self.common_apps = self._load_common_apps()
        
    def _load_common_apps(self) -> Dict[str, Dict[str, str]]:
        """Load common application paths for different platforms"""
        return {
            "windows": {
                "notepad": "notepad.exe",
                "calculator": "calc.exe",
                "paint": "mspaint.exe",
                "cmd": "cmd.exe",
                "powershell": "powershell.exe",
                "explorer": "explorer.exe",
                "chrome": r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                "firefox": r"C:\Program Files\Mozilla Firefox\firefox.exe",
                "edge": "msedge.exe",
                "outlook": "outlook.exe",
                "word": "winword.exe",
                "excel": "excel.exe",
                "powerpoint": "powerpnt.exe",
                "teams": "ms-teams:",
                "discord": "discord:",
                "telegram": "tg:",
                "whatsapp": "whatsapp:",
                "spotify": "spotify:",
                "steam": "steam:",
                "vscode": "code",
                "sublime": "subl"
            },
            "darwin": {  # macOS
                "calculator": "Calculator",
                "textedit": "TextEdit",
                "finder": "Finder",
                "terminal": "Terminal",
                "chrome": "Google Chrome",
                "firefox": "Firefox",
                "safari": "Safari",
                "mail": "Mail",
                "messages": "Messages",
                "facetime": "FaceTime",
                "spotify": "Spotify",
                "discord": "Discord",
                "telegram": "Telegram",
                "whatsapp": "WhatsApp",
                "vscode": "Visual Studio Code"
            },
            "linux": {
                "calculator": "gnome-calculator",
                "gedit": "gedit",
                "nautilus": "nautilus",
                "terminal": "gnome-terminal",
                "firefox": "firefox",
                "chrome": "google-chrome",
                "thunderbird": "thunderbird",
                "telegram": "telegram-desktop",
                "discord": "discord",
                "spotify": "spotify",
                "vscode": "code"
            }
        }

    def open_application(self, app_name: str) -> Dict[str, Any]:
        """Open an application by name"""
        try:
            app_name = app_name.lower().strip()
            
            # Remove common prefixes
            app_name = app_name.replace("open ", "").replace("launch ", "").replace("start ", "")
            
            platform_apps = self.common_apps.get(self.platform, {})
            
            if app_name in platform_apps:
                app_path = platform_apps[app_name]
                
                if self.platform == "windows":
                    if app_path.startswith(("http:", "https:", "mailto:", "ms-", "tg:", "discord:", "whatsapp:", "spotify:", "steam:")):
                        # Protocol handler
                        os.startfile(app_path)
                    else:
                        # Regular executable
                        subprocess.Popen(app_path, shell=True)
                        
                elif self.platform == "darwin":
                    subprocess.Popen(["open", "-a", app_path])
                    
                elif self.platform == "linux":
                    subprocess.Popen([app_path])
                
                return {
                    "status": "success",
                    "message": f"✅ Opened {app_name.title()}",
                    "action": "app_opened",
                    "app": app_name
                }
            else:
                # Try to find the app in system PATH
                try:
                    subprocess.Popen([app_name])
                    return {
                        "status": "success", 
                        "message": f"✅ Opened {app_name}",
                        "action": "app_opened",
                        "app": app_name
                    }
                except FileNotFoundError:
                    return {
                        "status": "error",
                        "message": f"❌ Could not find application: {app_name}",
                        "available_apps": list(platform_apps.keys())
                    }
                    
        except Exception as e:
            logger.error(f"Error opening application {app_name}: {e}")
            return {
                "status": "error",
                "message": f"❌ Failed to open {app_name}: {str(e)}"
            }

    def web_search(self, query: str, engine: str = "google") -> Dict[str, Any]:
        """Perform web search"""
        try:
            search_urls = {
                "google": f"https://www.google.com/search?q={query}",
                "bing": f"https://www.bing.com/search?q={query}",
                "duckduckgo": f"https://duckduckgo.com/?q={query}",
                "youtube": f"https://www.youtube.com/results?search_query={query}",
                "wikipedia": f"https://en.wikipedia.org/wiki/Special:Search?search={query}"
            }
            
            url = search_urls.get(engine, search_urls["google"])
            webbrowser.open(url)
            
            return {
                "status": "success",
                "message": f"🌐 Searching {engine} for: {query}",
                "action": "web_search",
                "query": query,
                "engine": engine,
                "url": url
            }
            
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return {
                "status": "error",
                "message": f"❌ Web search failed: {str(e)}"
            }

    def open_email(self, to: str = "", subject: str = "", body: str = "") -> Dict[str, Any]:
        """Open email client with optional pre-filled content"""
        try:
            mailto_url = "mailto:"
            if to:
                mailto_url += to
            
            params = []
            if subject:
                params.append(f"subject={subject}")
            if body:
                params.append(f"body={body}")
                
            if params:
                mailto_url += "?" + "&".join(params)
            
            webbrowser.open(mailto_url)
            
            return {
                "status": "success",
                "message": "📧 Opening email client...",
                "action": "email_opened",
                "mailto_url": mailto_url
            }
            
        except Exception as e:
            logger.error(f"Email open error: {e}")
            return {
                "status": "error",
                "message": f"❌ Failed to open email: {str(e)}"
            }

    def open_messaging_app(self, app: str = "telegram") -> Dict[str, Any]:
        """Open messaging applications"""
        try:
            messaging_apps = {
                "telegram": {"windows": "tg:", "darwin": "Telegram", "linux": "telegram-desktop"},
                "whatsapp": {"windows": "whatsapp:", "darwin": "WhatsApp", "linux": "whatsapp-for-linux"},
                "discord": {"windows": "discord:", "darwin": "Discord", "linux": "discord"},
                "teams": {"windows": "ms-teams:", "darwin": "Microsoft Teams", "linux": "teams"},
                "slack": {"windows": "slack:", "darwin": "Slack", "linux": "slack"}
            }
            
            if app.lower() in messaging_apps:
                app_info = messaging_apps[app.lower()]
                app_path = app_info.get(self.platform)
                
                if app_path:
                    if self.platform == "windows":
                        os.startfile(app_path)
                    elif self.platform == "darwin":
                        subprocess.Popen(["open", "-a", app_path])
                    else:
                        subprocess.Popen([app_path])
                    
                    return {
                        "status": "success",
                        "message": f"💬 Opening {app.title()}...",
                        "action": "messaging_opened",
                        "app": app
                    }
            
            return {
                "status": "error",
                "message": f"❌ {app.title()} not available on {self.platform}",
                "available_apps": list(messaging_apps.keys())
            }
            
        except Exception as e:
            logger.error(f"Messaging app error: {e}")
            return {
                "status": "error",
                "message": f"❌ Failed to open {app}: {str(e)}"
            }

    def get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        try:
            import psutil
            
            return {
                "status": "success",
                "system_info": {
                    "platform": platform.system(),
                    "platform_version": platform.version(),
                    "architecture": platform.architecture()[0],
                    "processor": platform.processor(),
                    "cpu_count": psutil.cpu_count(),
                    "memory_total": psutil.virtual_memory().total,
                    "memory_available": psutil.virtual_memory().available,
                    "disk_usage": psutil.disk_usage('/').percent if self.platform != "windows" else psutil.disk_usage('C:').percent
                }
            }
            
        except Exception as e:
            logger.error(f"System info error: {e}")
            return {
                "status": "error",
                "message": f"❌ Failed to get system info: {str(e)}"
            }

    def list_available_apps(self) -> Dict[str, Any]:
        """List available applications"""
        platform_apps = self.common_apps.get(self.platform, {})
        
        return {
            "status": "success",
            "available_apps": list(platform_apps.keys()),
            "platform": self.platform,
            "total_apps": len(platform_apps)
        }

    def execute_system_command(self, command: str) -> Dict[str, Any]:
        """Execute system command (with safety restrictions)"""
        # Safety: Only allow safe commands
        safe_commands = [
            "dir", "ls", "pwd", "whoami", "date", "time", "echo",
            "ipconfig", "ifconfig", "ping", "nslookup", "systeminfo"
        ]
        
        cmd_parts = command.split()
        if not cmd_parts or cmd_parts[0].lower() not in safe_commands:
            return {
                "status": "error",
                "message": f"❌ Command '{command}' not allowed for security reasons",
                "safe_commands": safe_commands
            }
        
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
            
            return {
                "status": "success",
                "command": command,
                "output": result.stdout,
                "error": result.stderr,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "status": "error",
                "message": "❌ Command timed out"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"❌ Command failed: {str(e)}"
            }
