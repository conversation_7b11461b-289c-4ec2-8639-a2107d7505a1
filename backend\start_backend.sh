#!/bin/bash
# <PERSON> script to start <PERSON>'s backend controller

echo "Starting Agent <PERSON> Backend Controller..."

# Check if Python is installed
if command -v python3 &>/dev/null; then
    PYTHON=python3
    echo "Using $(python3 --version)"
elif command -v python &>/dev/null; then
    PYTHON=python
    echo "Using $(python --version)"
else
    echo "Error: Python not found. Please install Python 3.8 or higher."
    exit 1
fi

# Create and activate virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    $PYTHON -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Start the backend server
echo "Starting backend server with diagnostics enabled..."
uvicorn agentlee_controller:app --host 127.0.0.1 --port 8000 --reload