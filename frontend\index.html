<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent Lee™ System</title>
  <style>
    /* Basic Reset */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      background-color: #0a0e29; /* Dark blue background */
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      position: relative;
      overflow: hidden;
    }
    
    /* Animated background grid */
    .grid-background {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: 
          linear-gradient(rgba(0, 242, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 242, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      z-index: -1;
      animation: gridMove 20s linear infinite;
    }

    @keyframes gridMove {
      0% { transform: translate(0, 0); }
      100% { transform: translate(50px, 50px); }
    }
    
    /* Boot screen overlay */
    .boot-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(10, 14, 41, 0.95);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      transition: opacity 1s ease;
    }

    .boot-overlay.hidden {
      opacity: 0;
      pointer-events: none;
    }

    .boot-logo {
      font-size: 4rem;
      font-weight: 900;
      color: #00f2ff;
      text-shadow: 0 0 30px rgba(0, 242, 255, 0.6);
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
    }
    
    .boot-logo-image {
      width: 120px;
      height: 120px;
      margin-bottom: 2rem;
      animation: pulse 2s infinite;
      border-radius: 20px;
      box-shadow: 0 0 30px rgba(0, 242, 255, 0.6);
    }

    .boot-status {
      font-size: 1.2rem;
      color: #e0e7ff;
      margin-bottom: 3rem;
      text-align: center;
    }

    .boot-progress {
      width: 400px;
      height: 4px;
      background: rgba(0, 242, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 2rem;
    }

    .boot-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #00f2ff, #9854ff);
      width: 0%;
      border-radius: 2px;
      transition: width 0.5s ease;
    }

    .boot-console {
      width: 500px;
      height: 200px;
      background: rgba(0, 0, 0, 0.8);
      border: 1px solid #00f2ff;
      border-radius: 8px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      overflow-y: auto;
      color: #e0e7ff;
    }

    .console-line {
      margin-bottom: 5px;
      opacity: 0;
      animation: fadeIn 0.5s forwards;
    }

    .console-line.success { color: #06d6a0; }
    .console-line.warning { color: #ffd166; }
    .console-line.error { color: #ff416c; }
    .console-line.info { color: #00f2ff; }

    @keyframes fadeIn {
      to { opacity: 1; }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    /* Agent Card Styles - MAIN INTERFACE */
    #agent-card {
      width: 320px;
      background-color: #1e293b;
      color: white;
      border-radius: 16px;
      border: 4px solid #3b82f6;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2), 0 0 30px rgba(59, 130, 246, 0.3);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 16px;
      z-index: 1000;
      transition: all 0.3s ease-in-out;
    }
    
    /* Card Header */
    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: move;
      margin-bottom: 16px;
    }
    
    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid #93c5fd;
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
    }
    
    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .agent-details h3 {
      color: #93c5fd;
      font-size: 18px;
      margin-bottom: 4px;
    }
    
    .agent-details p {
      color: #bfdbfe;
      font-size: 14px;
    }
    
    /* Navigation Grid */
    .navigation-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .nav-button {
      background-color: #334155;
      border: none;
      color: white;
      padding: 8px 4px;
      text-align: center;
      text-decoration: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    .nav-button:hover {
      background-color: #475569;
    }
    
    .nav-button span {
      font-size: 16px;
      margin-bottom: 4px;
      color: #60a5fa;
    }
    
    /* Chat Area */
    .chat-area {
      height: 144px;
      background-color: #334155;
      border-radius: 8px;
      padding: 8px;
      margin-bottom: 8px;
      overflow-y: auto;
    }
    
    .message {
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 8px;
    }
    
    .user-message {
      background-color: #475569;
      margin-left: 16px;
    }
    
    .agent-message {
      background-color: #3b82f6;
      margin-right: 16px;
    }
    
    .empty-chat {
      color: #94a3b8;
      text-align: center;
      font-style: italic;
      margin-top: 48px;
    }
    
    /* Message Input */
    .message-input {
      width: 100%;
      padding: 8px;
      border-radius: 8px;
      border: 1px solid #475569;
      background-color: #475569;
      color: white;
      resize: none;
      margin-bottom: 12px;
    }
    
    .message-input::placeholder {
      color: #94a3b8;
    }
    
    /* Control Buttons */
    .control-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      margin-bottom: 8px;
    }
    
    .control-button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 4px;
      border: none;
      border-radius: 8px;
      color: white;
      font-size: 12px;
      cursor: pointer;
    }
    
    .send-btn { background-color: #2563eb; }
    .send-btn:hover { background-color: #3b82f6; }
    
    .listen-btn { background-color: #16a34a; }
    .listen-btn:hover { background-color: #22c55e; }
    .listen-btn.active { background-color: #22c55e; box-shadow: 0 0 10px #22c55e; }
    
    .stop-btn { background-color: #dc2626; }
    .stop-btn:hover { background-color: #ef4444; }
    
    .finish-btn { background-color: #ca8a04; }
    .finish-btn:hover { background-color: #eab308; }
    
    .email-btn { background-color: #4f46e5; }
    .email-btn:hover { background-color: #6366f1; }
    
    .phone-btn { background-color: #0d9488; }
    .phone-btn:hover { background-color: #14b8a6; }
    
    .chat-btn { background-color: #3b82f6; }
    .chat-btn:hover { background-color: #60a5fa; }
    
    .camera-btn { background-color: #9333ea; }
    .camera-btn:hover { background-color: #a855f7; }
    
    /* Permissions modal */
    .permissions-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 2000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .permissions-modal.active {
      opacity: 1;
      visibility: visible;
    }

    .modal-content {
      background: rgba(16, 20, 40, 0.6);
      border: 1px solid rgba(0, 242, 255, 0.2);
      border-radius: 20px;
      padding: 40px;
      max-width: 500px;
      text-align: center;
      backdrop-filter: blur(10px);
    }

    .modal-title {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #00f2ff;
    }

    .modal-description {
      margin-bottom: 30px;
      line-height: 1.6;
      color: rgba(224, 231, 255, 0.9);
    }

    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px;
      margin-bottom: 10px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      border: 1px solid rgba(0, 242, 255, 0.2);
      color: white; /* Added white text color */
    }

    .permission-item strong {
      color: #e0e7ff; /* Lighter color for strong text */
      font-weight: 600;
    }

    .permission-item small {
      color: #bfdbfe; /* Light blue for small description text */
      display: block;
      margin-top: 5px;
    }

    .permission-toggle {
      width: 50px;
      height: 25px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      position: relative;
      cursor: pointer;
      transition: background 0.3s ease;
    }

    .permission-toggle.active {
      background: #06d6a0;
    }

    .permission-toggle::after {
      content: '';
      position: absolute;
      width: 21px;
      height: 21px;
      background: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: transform 0.3s ease;
    }

    .permission-toggle.active::after {
      transform: translateX(25px);
    }

    .modal-buttons {
      display: flex;
      gap: 15px;
      margin-top: 30px;
    }

    .modal-button {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: 10px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .modal-button.primary {
      background: linear-gradient(135deg, #00f2ff, #9854ff);
      color: white;
    }

    .modal-button.secondary {
      background: transparent;
      color: #e0e7ff;
      border: 1px solid rgba(0, 242, 255, 0.2);
    }

    .modal-button:hover {
      transform: translateY(-2px);
    }

    /* Help link for permissions */
    .help-link {
      color: #00f2ff;
      text-decoration: underline;
      cursor: pointer;
      margin-top: 10px;
      display: inline-block;
      font-size: 0.9rem;
    }

    .help-link:hover {
      text-decoration: none;
      color: #9854ff;
    }
    
    /* Camera feed */
    .camera-container {
      position: fixed;
      top: 20px;
      right: 20px;
      width: 320px;
      height: 240px;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #3b82f6;
      z-index: 900;
      display: none;
    }
    
    .camera-container.active {
      display: block;
    }
    
    #camera-feed {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    /* System status indicator */
    .system-status {
      position: fixed;
      bottom: 20px;
      left: 20px;
      background: rgba(16, 20, 40, 0.6);
      border: 1px solid rgba(0, 242, 255, 0.2);
      padding: 8px 15px;
      border-radius: 30px;
      font-size: 12px;
      color: #e0e7ff;
      display: flex;
      align-items: center;
      gap: 8px;
      backdrop-filter: blur(5px);
      z-index: 800;
    }
    
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #06d6a0;
      box-shadow: 0 0 10px #06d6a0;
    }
    
    .status-dot.warning {
      background: #ffd166;
      box-shadow: 0 0 10px #ffd166;
    }
    
    .status-dot.error {
      background: #ff416c;
      box-shadow: 0 0 10px #ff416c;
    }
  </style>
</head>
<body>
  <div class="grid-background"></div>
  
  <!-- Boot Screen -->
  <div class="boot-overlay" id="bootOverlay">
    <img src="r239gfoi0w.png" alt="Agent Lee" class="boot-logo-image">
    <div class="boot-status" id="bootStatus">Initializing Agent Lee Protocol...</div>
    <div class="boot-progress">
      <div class="boot-progress-bar" id="bootProgress"></div>
    </div>
    <div class="boot-console" id="bootConsole">
      <div class="console-line info">[SYSTEM] Starting Agent Lee™ Operating System...</div>
    </div>
  </div>
  
  <!-- Camera feed container -->
  <div class="camera-container" id="cameraContainer">
    <video id="camera-feed" autoplay playsinline></video>
  </div>
  
  <!-- System status indicator -->
  <div class="system-status" id="systemStatus">
    <div class="status-dot" id="statusDot"></div>
    <span id="statusText">Initializing...</span>
  </div>
  
  <!-- Agent Card - Main Interface -->
  <div id="agent-card">
    <!-- Card Header -->
    <div class="card-header" id="drag-handle">
      <div class="avatar">
        <img src="r239gfoi0w.png" alt="Agent Lee">
      </div>
      <div class="agent-details">
        <h3>Agent Lee</h3>
        <p>Your AI Assistant</p>
      </div>
    </div>
    
    <!-- Navigation Grid -->
    <div class="navigation-grid">
      <button class="nav-button" data-dashboard="todo">
        <span>📋</span>
        To-Do List
      </button>
      <button class="nav-button" data-dashboard="agents">
        <span>👥</span>
        Agents
      </button>
      <button class="nav-button" data-dashboard="database">
        <span>🗄️</span>
        Database
      </button>
      <button class="nav-button" data-dashboard="llm">
        <span>🧠</span>
        LLM Brain
      </button>
      <button class="nav-button" data-dashboard="workers">
        <span>⚙️</span>
        Workers
      </button>
      <button class="nav-button" id="diagnosticsBtn">
        <span>🔍</span>
        Diagnostics
      </button>
    </div>
    
    <!-- Chat Area -->
    <div class="chat-area" id="chat-messages">
      <div class="empty-chat" id="empty-message">
        Agent Lee initialized and ready to assist
      </div>
      <!-- Messages will be added here dynamically -->
    </div>
    
    <!-- Message Input -->
    <textarea 
      class="message-input" 
      id="message-input" 
      rows="2" 
      placeholder="Type your message or command..."></textarea>
    
    <!-- Control Buttons - First Row -->
    <div class="control-row">
      <button class="control-button send-btn" id="send-button">
        <span class="icon">✉️</span> Send
      </button>
      <button class="control-button listen-btn" id="listen-button">
        <span class="icon">🎤</span> Listen
      </button>
      <button class="control-button stop-btn" id="stop-button">
        <span class="icon">⏹️</span> Stop
      </button>
      <button class="control-button finish-btn" id="finish-button">
        <span class="icon">✅</span> Finish
      </button>
    </div>
    
    <!-- Control Buttons - Second Row -->
    <div class="control-row">
      <button class="control-button email-btn" id="email-button">
        <span class="icon">📧</span> Email
      </button>
      <button class="control-button phone-btn" id="phone-button">
        <span class="icon">📱</span> Phone
      </button>
      <button class="control-button chat-btn" id="chat-button">
        <span class="icon">💬</span> Chat
      </button>
      <button class="control-button camera-btn" id="camera-button">
        <span class="icon">📷</span> Camera
      </button>
    </div>
  </div>

  <!-- Permissions Modal -->
  <div class="permissions-modal" id="permissionsModal">
    <div class="modal-content">
      <h2 class="modal-title">System Permissions</h2>
      <p class="modal-description">
        Agent Lee™ requires certain permissions to function properly. Please review and enable the permissions you'd like to grant.
      </p>
      
      <div class="permission-item">
        <div>
          <strong>Microphone Access</strong>
          <br><small>For voice commands and speech recognition</small>
        </div>
        <div class="permission-toggle" data-permission="microphone"></div>
      </div>
      
      <div class="permission-item">
        <div>
          <strong>Camera Access</strong>
          <br><small>For visual input and monitoring</small>
        </div>
        <div class="permission-toggle" data-permission="camera"></div>
      </div>
      
      <div class="permission-item">
        <div>
          <strong>File Access</strong>
          <br><small>For document processing and storage</small>
        </div>
        <div class="permission-toggle" data-permission="files"></div>
      </div>
      
      <div class="permission-item">
        <div>
          <strong>Background Tasks</strong>
          <br><small>For continuous monitoring and updates</small>
        </div>
        <div class="permission-toggle" data-permission="background"></div>
      </div>

      <p><a class="help-link" id="browser-help-link">Need help with browser permissions?</a></p>
      
      <div class="modal-buttons">
        <button class="modal-button secondary" id="cancelPermissions">Cancel</button>
        <button class="modal-button primary" id="savePermissions">Save & Continue</button>
      </div>
    </div>
  </div>

  <script>
    // Global state
    const systemState = {
      permissions: {
        microphone: false,
        camera: false,
        files: false,
        background: false
      },
      voiceActive: false,
      cameraActive: false,
      backendConnected: false,
      dashboards: {
        todo: '72kh0rx6ss.html',
        agents: 'ikuoqw85ur.html', 
        database: 'aqylvu95i5.html',
        llm: 'jfy4eqk6tq.html',
        workers: 's3enpoc0b9.html'
      }
    };

    // Boot sequence
    async function startBootSequence() {
      const bootOverlay = document.getElementById('bootOverlay');
      const bootStatus = document.getElementById('bootStatus');
      const bootProgress = document.getElementById('bootProgress');
      const bootConsole = document.getElementById('bootConsole');

      const bootSteps = [
        { message: 'Loading core modules...', progress: 20, type: 'info' },
        { message: 'Initializing neural pathways...', progress: 40, type: 'info' },
        { message: 'Connecting to backend services...', progress: 60, type: 'info' },
        { message: 'Loading Agent Lee interface...', progress: 80, type: 'success' },
        { message: 'Agent Lee™ is ready!', progress: 100, type: 'success' }
      ];

      for (let i = 0; i < bootSteps.length; i++) {
        const step = bootSteps[i];
        
        // Update status
        bootStatus.textContent = step.message;
        bootProgress.style.width = step.progress + '%';
        
        // Add console line
        const consoleLine = document.createElement('div');
        consoleLine.className = `console-line ${step.type}`;
        consoleLine.textContent = `[${new Date().toLocaleTimeString()}] ${step.message}`;
        consoleLine.style.animationDelay = '0s';
        bootConsole.appendChild(consoleLine);
        
        // Scroll to bottom
        bootConsole.scrollTop = bootConsole.scrollHeight;
        
        // Wait
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // Finish boot sequence
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      bootOverlay.classList.add('hidden');
      
      // Test backend connection
      testBackendConnection();
      
      // Check for first-time permissions
      if (!localStorage.getItem('agentlee_permissions')) {
        setTimeout(() => showPermissionsModal(), 2000);
      } else {
        // Load saved permissions
        const savedPermissions = JSON.parse(localStorage.getItem('agentlee_permissions'));
        systemState.permissions = savedPermissions;
        
        // Initialize with saved permissions
        if (systemState.permissions.microphone || systemState.permissions.camera) {
          requestMediaPermissions();
        }
      }
      
      // Welcome message
      addMessage("Hello! I'm Agent Lee, your AI assistant. How can I help you today?", 'agent');
    }

    // Backend connection test
    async function testBackendConnection() {
      try {
        const response = await fetch('http://localhost:8000/api/system_status');
        if (response.ok) {
          systemState.backendConnected = true;
          updateSystemStatus('Connected to backend', 'success');
          addConsoleMessage('Backend connection established', 'success');
        } else {
          throw new Error('Backend not responding');
        }
      } catch (error) {
        systemState.backendConnected = false;
        updateSystemStatus('Backend offline - demo mode', 'warning');
        addConsoleMessage('Running in demo mode (backend offline)', 'warning');
      }
    }

    // Console logging
    function addConsoleMessage(message, type = 'info') {
      console.log(`[AGENT LEE] ${message}`);
      const bootConsole = document.getElementById('bootConsole');
      
      if (bootConsole) {
        const consoleLine = document.createElement('div');
        consoleLine.className = `console-line ${type}`;
        consoleLine.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        bootConsole.appendChild(consoleLine);
        bootConsole.scrollTop = bootConsole.scrollHeight;
      }
    }

    // Update system status
    function updateSystemStatus(text, type = 'success') {
      const statusText = document.getElementById('statusText');
      const statusDot = document.getElementById('statusDot');
      
      statusText.textContent = text;
      
      // Update status dot
      statusDot.className = 'status-dot';
      if (type === 'warning') statusDot.classList.add('warning');
      if (type === 'error') statusDot.classList.add('error');
    }

    // Dashboard navigation
    function openDashboard(dashboardKey) {
      const filename = systemState.dashboards[dashboardKey];
      if (filename) {
        if (systemState.backendConnected) {
          // Use backend API to open in new window/tab
          fetch('http://localhost:8000/api/open_chrome', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ url: filename })
          }).catch(() => {
            // Fallback to direct navigation
            window.open(filename, '_blank');
          });
        } else {
          // Direct navigation in demo mode
          window.open(filename, '_blank');
        }
        addConsoleMessage(`Opening ${dashboardKey} dashboard`, 'info');
        addMessage(`Opening the ${dashboardKey} dashboard for you.`, 'agent');
      }
    }

    // Chat functionality
    function addMessage(text, sender) {
      // Hide empty message if this is the first message
      const emptyMessage = document.getElementById('empty-message');
      if (emptyMessage) {
        emptyMessage.style.display = 'none';
      }
      
      const chatMessages = document.getElementById('chat-messages');
      const messageElement = document.createElement('div');
      messageElement.classList.add('message');
      messageElement.classList.add(sender === 'user' ? 'user-message' : 'agent-message');
      messageElement.textContent = text;
      
      chatMessages.appendChild(messageElement);
      chatMessages.scrollTop = chatMessages.scrollHeight;
      
      // If it's a user message, process it as a command
      if (sender === 'user') {
        processCommand(text);
      }
    }
    
    function sendMessage() {
      const messageInput = document.getElementById('message-input');
      const message = messageInput.value.trim();
      if (!message) return;
      
      // Add user message
      addMessage(message, 'user');
      messageInput.value = '';
    }
    
    // Process user commands
    function processCommand(text) {
      const lowercaseText = text.toLowerCase();
      
      // Open dashboard commands
      if (lowercaseText.includes('open') && lowercaseText.includes('todo')) {
        openDashboard('todo');
      } else if (lowercaseText.includes('open') && lowercaseText.includes('agent')) {
        openDashboard('agents');
      } else if (lowercaseText.includes('open') && lowercaseText.includes('database')) {
        openDashboard('database');
      } else if (lowercaseText.includes('open') && lowercaseText.includes('brain') || lowercaseText.includes('llm')) {
        openDashboard('llm');
      } else if (lowercaseText.includes('open') && lowercaseText.includes('worker')) {
        openDashboard('workers');
      } 
      // Camera commands
      else if (lowercaseText.includes('camera') && (lowercaseText.includes('on') || lowercaseText.includes('open'))) {
        toggleCamera(true);
      } else if (lowercaseText.includes('camera') && (lowercaseText.includes('off') || lowercaseText.includes('close'))) {
        toggleCamera(false);
      } 
      // Voice commands
      else if (lowercaseText.includes('listen') || lowercaseText.includes('voice') && lowercaseText.includes('on')) {
        toggleVoiceRecognition(true);
      } else if (lowercaseText.includes('stop') && lowercaseText.includes('listen')) {
        toggleVoiceRecognition(false);
      } 
      // Status commands
      else if (lowercaseText.includes('status') || lowercaseText.includes('health')) {
        if (systemState.backendConnected) {
          sendStatusRequest();
        } else {
          setTimeout(() => {
            addMessage("All systems are operating in demo mode. Backend services are not connected.", 'agent');
          }, 1000);
        }
      } 
      // Help command
      else if (lowercaseText.includes('help')) {
        setTimeout(() => {
          addMessage("I can help you access the Agent Lee dashboards. Try commands like 'open todo list', 'open agents', or use the navigation buttons above. You can also control your camera with 'camera on/off' or use voice commands with 'listen'.", 'agent');
        }, 1000);
      }
      // Unknown command - default response
      else {
        setTimeout(() => {
          addMessage("I understand you want to " + text + ". How would you like me to help with that?", 'agent');
        }, 1000);
      }
    }

    // Browser permissions help
    function showBrowserPermissionsHelp() {
      const browserName = detectBrowser();
      let helpMessage = "To fix permission issues in ";
      
      switch(browserName) {
        case 'chrome':
          helpMessage += "Chrome: Click the padlock icon in the address bar → Site Settings → Allow camera/microphone access.";
          break;
        case 'firefox':
          helpMessage += "Firefox: Click the padlock icon in the address bar → Connection secure → More Information → Permissions → Allow camera/microphone access.";
          break;
        case 'safari':
          helpMessage += "Safari: Safari menu → Preferences → Websites → Camera/Microphone → Allow for this website.";
          break;
        case 'edge':
          helpMessage += "Edge: Click the padlock icon in the address bar → Site permissions → Allow camera/microphone access.";
          break;
        default:
          helpMessage += "your browser: Check your browser settings to allow camera and microphone permissions for this website.";
      }
      
      addMessage(helpMessage, 'agent');
      hidePermissionsModal();
    }

    function detectBrowser() {
      const userAgent = navigator.userAgent.toLowerCase();
      if (userAgent.indexOf("chrome") > -1) return 'chrome';
      if (userAgent.indexOf("firefox") > -1) return 'firefox';
      if (userAgent.indexOf("safari") > -1 && userAgent.indexOf("chrome") === -1) return 'safari';
      if (userAgent.indexOf("edg") > -1) return 'edge';
      return 'unknown';
    }

    // Camera functionality
    function toggleCamera(enable = true) {
      const cameraContainer = document.getElementById('cameraContainer');
      const cameraFeed = document.getElementById('camera-feed');
      const cameraButton = document.getElementById('camera-button');
      
      if (!systemState.permissions.camera) {
        addMessage("Camera permission is required. Please enable it in the permissions settings.", 'agent');
        showPermissionsModal();
        return;
      }
      
      if (enable) {
        if (!systemState.cameraActive) {
          // Start camera
          navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
              cameraFeed.srcObject = stream;
              cameraContainer.classList.add('active');
              systemState.cameraActive = true;
              cameraButton.style.backgroundColor = '#a855f7';
              cameraButton.style.boxShadow = '0 0 10px #a855f7';
              addMessage("Camera activated. You can turn it off by saying 'camera off' or clicking the Camera button again.", 'agent');
            })
            .catch(err => {
              console.error('Camera error:', err);
              addMessage(`Sorry, I couldn't access your camera. Please check your browser permissions. ${createHelpLink()}`, 'agent');
            });
        }
      } else {
        if (systemState.cameraActive) {
          // Stop camera
          const stream = cameraFeed.srcObject;
          const tracks = stream.getTracks();
          
          tracks.forEach(track => track.stop());
          cameraFeed.srcObject = null;
          cameraContainer.classList.remove('active');
          systemState.cameraActive = false;
          cameraButton.style.backgroundColor = '';
          cameraButton.style.boxShadow = '';
          addMessage("Camera deactivated.", 'agent');
        }
      }
    }

    function createHelpLink() {
      return `<a onclick="showPermissionsModal()" style="color:#00f2ff;text-decoration:underline;cursor:pointer;">Help fix these issues here</a>`;
    }

    // Voice recognition
    let recognition = null;

    function toggleVoiceRecognition(enable = true) {
      const listenButton = document.getElementById('listen-button');
      
      if (!systemState.permissions.microphone) {
        addMessage("Microphone permission is required. Please enable it in the permissions settings.", 'agent');
        showPermissionsModal();
        return;
      }
      
      if (enable) {
        if (!systemState.voiceActive) {
          if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            recognition = new SpeechRecognition();
            
            recognition.continuous = true;
            recognition.interimResults = false;
            recognition.lang = 'en-US';
            
            recognition.onstart = function() {
              systemState.voiceActive = true;
              listenButton.classList.add('active');
              updateSystemStatus('Listening...', 'success');
              addMessage("I'm listening. Speak a command.", 'agent');
            };
            
            recognition.onresult = function(event) {
              const last = event.results.length - 1;
              const command = event.results[last][0].transcript;
              
              addMessage(command, 'user');
            };
            
            recognition.onerror = function(event) {
              console.error('Voice recognition error:', event.error);
              updateSystemStatus('Recognition error: ' + event.error, 'error');
              const errorMessage = `Sorry, I've encountered a voice recognition error: ${event.error}. ${createHelpLink()}`;
              addMessage(errorMessage, 'agent');
            };
            
            recognition.onend = function() {
              if (systemState.voiceActive) {
                // If we're still supposed to be active, restart
                recognition.start();
              }
            };
            
            recognition.start();
          } else {
            addMessage("Sorry, your browser doesn't support speech recognition.", 'agent');
            updateSystemStatus('Speech recognition not supported', 'error');
          }
        }
      } else {
        if (systemState.voiceActive && recognition) {
          recognition.stop();
          systemState.voiceActive = false;
          listenButton.classList.remove('active');
          updateSystemStatus('Connected to backend', systemState.backendConnected ? 'success' : 'warning');
          addMessage("Voice recognition stopped.", 'agent');
        }
      }
    }

    // Text-to-speech
    function speakText(text) {
      if (systemState.backendConnected) {
        try {
          fetch('http://localhost:8000/api/speak', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ text: text })
          });
          return;
        } catch (error) {
          console.warn('Backend speech failed, using browser TTS');
        }
      }
      
      // Fallback to browser speech synthesis
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 0.8;
        speechSynthesis.speak(utterance);
      }
    }

    // Permissions management
    function showPermissionsModal() {
      document.getElementById('permissionsModal').classList.add('active');
    }

    function hidePermissionsModal() {
      document.getElementById('permissionsModal').classList.remove('active');
    }

    function savePermissions() {
      const toggles = document.querySelectorAll('.permission-toggle');
      const permissions = {};
      
      toggles.forEach(toggle => {
        const permission = toggle.dataset.permission;
        permissions[permission] = toggle.classList.contains('active');
      });
      
      systemState.permissions = permissions;
      localStorage.setItem('agentlee_permissions', JSON.stringify(permissions));
      
      addConsoleMessage('Permissions saved', 'success');
      hidePermissionsModal();
      
      // Request actual permissions if enabled
      if (systemState.permissions.microphone || systemState.permissions.camera) {
        requestMediaPermissions();
      }
    }

    function requestMediaPermissions() {
      const constraints = {
        audio: systemState.permissions.microphone,
        video: systemState.permissions.camera
      };
      
      if (constraints.audio || constraints.video) {
        navigator.mediaDevices.getUserMedia(constraints)
          .then(stream => {
            // Stop all tracks immediately - we just want the permissions
            stream.getTracks().forEach(track => track.stop());
            
            if (constraints.audio) {
              addConsoleMessage('Microphone permission granted', 'success');
            }
            
            if (constraints.video) {
              addConsoleMessage('Camera permission granted', 'success');
              // Enable camera automatically if permission was just granted
              if (systemState.permissions.camera) {
                setTimeout(() => toggleCamera(true), 1000);
              }
            }
          })
          .catch(err => {
            console.error('Media permission error:', err);
            addConsoleMessage('Media permission denied: ' + err.message, 'error');
            addMessage(`Media permission denied: ${err.message}. Please check your browser settings to grant permission. ${createHelpLink()}`, 'agent');
          });
      }
    }

    // Backend API calls
    async function sendStatusRequest() {
      try {
        const response = await fetch('http://localhost:8000/api/system_status');
        if (response.ok) {
          const status = await response.json();
          
          let statusMessage = "All systems operational. ";
          
          if (!status.llm_ready) {
            statusMessage += "LLM system is offline. ";
          }
          
          if (!status.agents_ready) {
            statusMessage += "Agent system is offline. ";
          }
          
          if (!status.workers_ready) {
            statusMessage += "Worker system is offline. ";
          }
          
          if (!status.db_ready) {
            statusMessage += "Database system is offline. ";
          }
          
          if (!status.notepad_ready) {
            statusMessage += "Notepad system is offline. ";
          }
          
          statusMessage += `System health: ${status.system_health.toFixed(1)}%.`;
          
          addMessage(statusMessage, 'agent');
          speakText(statusMessage);
        } else {
          throw new Error('Status request failed');
        }
      } catch (error) {
        console.error('Status request error:', error);
        addMessage("I couldn't retrieve the system status. The backend might be offline.", 'agent');
      }
    }

    // Diagnostics popup
    function showDiagnostics() {
      if (systemState.backendConnected) {
        sendStatusRequest();
      } else {
        addMessage("Diagnostics are limited in demo mode. Backend connection is required for full system diagnostics.", 'agent');
      }
    }

    // Drag functionality
    function setupDragFunctionality() {
      const agentCard = document.getElementById('agent-card');
      const dragHandle = document.getElementById('drag-handle');
      
      let isDragging = false;
      let offsetX, offsetY;
      
      dragHandle.addEventListener('mousedown', (e) => {
        isDragging = true;
        const rect = agentCard.getBoundingClientRect();
        offsetX = e.clientX - rect.left;
        offsetY = e.clientY - rect.top;
        agentCard.style.cursor = 'grabbing';
      });
      
      document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        
        agentCard.style.top = `${e.clientY - offsetY}px`;
        agentCard.style.left = `${e.clientX - offsetX}px`;
        agentCard.style.transform = 'none';
      });
      
      document.addEventListener('mouseup', () => {
        isDragging = false;
        agentCard.style.cursor = 'default';
      });
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Start boot sequence
      startBootSequence();
      
      // Setup drag functionality
      setupDragFunctionality();
      
      // Dashboard navigation buttons
      document.querySelectorAll('.nav-button[data-dashboard]').forEach(button => {
        button.addEventListener('click', function() {
          const dashboard = this.dataset.dashboard;
          openDashboard(dashboard);
        });
      });
      
      // Diagnostics button
      document.getElementById('diagnosticsBtn').addEventListener('click', showDiagnostics);
      
      // Message input
      const messageInput = document.getElementById('message-input');
      const sendButton = document.getElementById('send-button');
      
      sendButton.addEventListener('click', sendMessage);
      
      messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });
      
      // Control buttons
      document.getElementById('listen-button').addEventListener('click', () => {
        toggleVoiceRecognition(!systemState.voiceActive);
      });
      
      document.getElementById('stop-button').addEventListener('click', () => {
        if (systemState.voiceActive) {
          toggleVoiceRecognition(false);
        }
        
        if (systemState.cameraActive) {
          toggleCamera(false);
        }
        
        addMessage("All active processes stopped.", 'agent');
      });
      
      document.getElementById('camera-button').addEventListener('click', () => {
        toggleCamera(!systemState.cameraActive);
      });
      
      document.getElementById('email-button').addEventListener('click', () => {
        addMessage("What email would you like to compose?", 'agent');
      });
      
      document.getElementById('phone-button').addEventListener('click', () => {
        addMessage("Who would you like to call?", 'agent');
      });
      
      document.getElementById('chat-button').addEventListener('click', () => {
        addMessage("Chat mode activated. How can I help you today?", 'agent');
      });
      
      document.getElementById('finish-button').addEventListener('click', () => {
        addMessage("Is there anything else you'd like me to do?", 'agent');
      });
      
      // Permission toggles
      document.querySelectorAll('.permission-toggle').forEach(toggle => {
        toggle.addEventListener('click', function() {
          this.classList.toggle('active');
        });
      });
      
      // Help link for browser permissions
      document.getElementById('browser-help-link').addEventListener('click', showBrowserPermissionsHelp);
      
      // Load saved permissions
      const savedPermissions = localStorage.getItem('agentlee_permissions');
      if (savedPermissions) {
        const permissions = JSON.parse(savedPermissions);
        
        // Update UI
        Object.keys(permissions).forEach(permission => {
          const toggle = document.querySelector(`[data-permission="${permission}"]`);
          if (toggle && permissions[permission]) {
            toggle.classList.add('active');
          }
        });
      }
      
      // Modal buttons
      document.getElementById('cancelPermissions').addEventListener('click', hidePermissionsModal);
      document.getElementById('savePermissions').addEventListener('click', savePermissions);
      
      // For testing the permissions modal
      // Uncomment to test: showPermissionsModal();
    });
  </script>
</body>
</html>