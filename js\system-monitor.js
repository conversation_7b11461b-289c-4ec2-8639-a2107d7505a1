/**
 * Agent Lee™ System Monitor
 * Provides real-time monitoring and diagnostics for the Agent Lee system
 */

import apiClient from './api-client.js';

class SystemMonitor {
    constructor() {
        this.isInitialized = false;
        this.updateInterval = null;
        this.diagnosticData = {
            systemStatus: null,
            moduleMetrics: null,
            taskQueue: null,
            version: null,
            lastUpdate: null
        };
        this.listeners = {
            'status-update': [],
            'metrics-update': [],
            'queue-update': [],
            'connection-change': []
        };
    }

    /**
     * Initialize the system monitor
     * @param {number} updateInterval - Update interval in milliseconds
     */
    async initialize(updateInterval = 30000) {
        if (this.isInitialized) return;
        
        this.isInitialized = true;
        
        // Get initial status
        await this.updateAllMetrics();
        
        // Set up polling
        this.startUpdatePolling(updateInterval);
        
        console.log('System Monitor initialized');
        return true;
    }

    /**
     * Start polling for updates
     * @param {number} interval - Update interval in milliseconds
     */
    startUpdatePolling(interval) {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            this.updateAllMetrics();
        }, interval);
    }

    /**
     * Stop polling for updates
     */
    stopUpdatePolling() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Update all metrics
     * @returns {Promise<boolean>} Success status
     */
    async updateAllMetrics() {
        try {
            // Check connection status first
            const isConnected = await apiClient.checkConnection();
            
            // Notify about connection changes
            this.notifyListeners('connection-change', {
                connected: isConnected,
                status: apiClient.connectionStatus
            });
            
            // If not connected, don't attempt to fetch data
            if (!isConnected) {
                return false;
            }
            
            // Update system status
            await this.updateSystemStatus();
            
            // Update module metrics
            await this.updateModuleMetrics();
            
            // Update task queue
            await this.updateTaskQueue();
            
            // Update version info (less frequently)
            if (!this.diagnosticData.version) {
                await this.updateVersionInfo();
            }
            
            this.diagnosticData.lastUpdate = new Date();
            
            return true;
        } catch (error) {
            console.error('Failed to update metrics:', error);
            return false;
        }
    }

    /**
     * Update system status
     * @returns {Promise<Object>} System status data
     */
    async updateSystemStatus() {
        try {
            const status = await apiClient.getSystemStatus();
            this.diagnosticData.systemStatus = status;
            this.notifyListeners('status-update', status);
            return status;
        } catch (error) {
            console.error('Failed to update system status:', error);
            return null;
        }
    }

    /**
     * Update module metrics
     * @returns {Promise<Object>} Module metrics data
     */
    async updateModuleMetrics() {
        try {
            const metrics = await apiClient.getModuleMetrics();
            this.diagnosticData.moduleMetrics = metrics;
            this.notifyListeners('metrics-update', metrics);
            return metrics;
        } catch (error) {
            console.error('Failed to update module metrics:', error);
            return null;
        }
    }

    /**
     * Update task queue data
     * @returns {Promise<Object>} Task queue data
     */
    async updateTaskQueue() {
        try {
            const queue = await apiClient.getTaskQueue();
            this.diagnosticData.taskQueue = queue;
            this.notifyListeners('queue-update', queue);
            return queue;
        } catch (error) {
            console.error('Failed to update task queue:', error);
            return null;
        }
    }

    /**
     * Update version info
     * @returns {Promise<Object>} Version info
     */
    async updateVersionInfo() {
        try {
            const version = await apiClient.getVersion();
            this.diagnosticData.version = version;
            return version;
        } catch (error) {
            console.error('Failed to update version info:', error);
            return null;
        }
    }

    /**
     * Check the health of a specific module
     * @param {string} module - Module name
     * @returns {boolean} Module health status
     */
    isModuleHealthy(module) {
        if (!this.diagnosticData.systemStatus) return false;
        
        switch (module) {
            case 'llm':
                return this.diagnosticData.systemStatus.llm_ready;
            case 'agents':
                return this.diagnosticData.systemStatus.agents_ready;
            case 'workers':
                return this.diagnosticData.systemStatus.workers_ready;
            case 'db':
                return this.diagnosticData.systemStatus.db_ready;
            case 'notepad':
                return this.diagnosticData.systemStatus.notepad_ready;
            default:
                return false;
        }
    }

    /**
     * Get the current system health score
     * @returns {number} Health score (0-100)
     */
    getSystemHealthScore() {
        if (!this.diagnosticData.systemStatus) return 0;
        return this.diagnosticData.systemStatus.system_health || 0;
    }

    /**
     * Register a listener for monitor events
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
    addEventListener(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback);
        }
    }

    /**
     * Remove a listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
    removeEventListener(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
        }
    }

    /**
     * Notify all listeners of an event
     * @param {string} event - Event name
     * @param {*} data - Event data
     */
    notifyListeners(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }

    /**
     * Get all diagnostic data
     * @returns {Object} All diagnostic data
     */
    getAllDiagnostics() {
        return {
            ...this.diagnosticData,
            isConnected: apiClient.isConnected,
            connectionStatus: apiClient.connectionStatus
        };
    }
}

// Create and export singleton instance
const systemMonitor = new SystemMonitor();
export default systemMonitor;