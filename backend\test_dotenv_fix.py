#!/usr/bin/env python3
"""
Quick test to verify dotenv loading fix
"""

import os
import sys

def test_dotenv_loading():
    """Test that environment variables are properly loaded"""
    print("🧪 Testing dotenv loading fix...")
    
    # Test 1: Check if .env file exists
    if os.path.exists('.env'):
        print("✅ .env file found")
    else:
        print("⚠️ .env file not found - creating from .env.example")
        if os.path.exists('.env.example'):
            with open('.env.example', 'r') as src, open('.env', 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created")
        else:
            print("❌ No .env.example file found")
            return False
    
    # Test 2: Try loading dotenv
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ dotenv loaded successfully")
    except ImportError:
        print("❌ python-dotenv not installed")
        return False
    except Exception as e:
        print(f"❌ dotenv loading failed: {e}")
        return False
    
    # Test 3: Try importing config
    try:
        from config import AppConfig
        print("✅ config module imported successfully")
        
        # Test configuration
        summary = AppConfig.get_config_summary()
        print(f"✅ Configuration loaded: {summary['host']}:{summary['port']}")
        
    except Exception as e:
        print(f"❌ config import failed: {e}")
        return False
    
    # Test 4: Try importing LLM service
    try:
        from llm_service import LLMService
        llm = LLMService()
        status = llm.get_service_status()
        print(f"✅ LLM service loaded: {status['primary_model']}")
        
    except Exception as e:
        print(f"❌ LLM service import failed: {e}")
        return False
    
    # Test 5: Try importing models
    try:
        from models import create_tables
        print("✅ models module imported successfully")
        
    except Exception as e:
        print(f"❌ models import failed: {e}")
        return False
    
    print("\n🎉 All dotenv loading tests passed!")
    return True

def test_enhanced_startup():
    """Test the enhanced startup script"""
    print("\n🚀 Testing enhanced startup script...")
    
    try:
        # Import the startup script
        import start_enhanced
        print("✅ start_enhanced.py imported successfully")
        
        # Test individual functions
        if start_enhanced.check_python_version():
            print("✅ Python version check passed")
        
        if start_enhanced.check_dependencies():
            print("✅ Dependencies check passed")
        
        if start_enhanced.check_configuration():
            print("✅ Configuration check passed")
        
        print("✅ Enhanced startup script working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced startup test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Agent Lee™ Dotenv Fix Verification")
    print("=" * 40)
    
    success = True
    
    # Run tests
    if not test_dotenv_loading():
        success = False
    
    if not test_enhanced_startup():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✅ All tests passed! The dotenv fix is working correctly.")
        print("\n💡 You can now run:")
        print("   python start_enhanced.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)
